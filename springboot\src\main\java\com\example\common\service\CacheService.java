package com.example.common.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Qualifier;

/**
 * 缓存服务工具类
 */
@Service
public class CacheService {

    private static final Logger log = LoggerFactory.getLogger(CacheService.class);

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    @Qualifier("cacheObjectMapper")
    private ObjectMapper objectMapper;

    private boolean redisAvailable = true;

    /**
     * 初始化时检查Redis连接
     */
    @PostConstruct
    public void checkRedisConnection() {
        try {
            redisTemplate.opsForValue().set("health:check", "ok", 10, TimeUnit.SECONDS);
            redisTemplate.delete("health:check");
            redisAvailable = true;
            log.info("Redis连接正常");
        } catch (Exception e) {
            redisAvailable = false;
            log.warn("Redis连接失败，将使用降级模式: {}", e.getMessage());
        }
    }

    /**
     * 设置缓存
     */
    public void set(String key, Object value) {
        if (!redisAvailable) {
            log.debug("Redis不可用，跳过缓存设置: {}", key);
            return;
        }
        try {
            redisTemplate.opsForValue().set(key, value);
            log.debug("设置缓存成功，key: {}", key);
        } catch (Exception e) {
            log.error("设置缓存失败，key: {}", key, e);
            redisAvailable = false;
        }
    }

    /**
     * 设置缓存并指定过期时间
     */
    public void set(String key, Object value, long timeout) {
        if (!redisAvailable) {
            log.debug("Redis不可用，跳过缓存设置: {}", key);
            return;
        }
        try {
            redisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
            log.debug("设置缓存成功，key: {}, timeout: {}s", key, timeout);
        } catch (Exception e) {
            log.error("设置缓存失败，key: {}", key, e);
            redisAvailable = false;
        }
    }

    /**
     * 获取缓存
     */
    public Object get(String key) {
        if (!redisAvailable) {
            log.debug("Redis不可用，返回null: {}", key);
            return null;
        }
        try {
            Object value = redisTemplate.opsForValue().get(key);
            log.debug("获取缓存，key: {}, value: {}", key, value != null ? "存在" : "不存在");
            return value;
        } catch (Exception e) {
            log.error("获取缓存失败，key: {}", key, e);
            redisAvailable = false;
            return null;
        }
    }

    /**
     * 获取缓存并转换为指定类型
     */
    public <T> T get(String key, Class<T> clazz) {
        try {
            Object value = get(key);
            if (value == null) {
                return null;
            }
            
            if (clazz.isInstance(value)) {
                return clazz.cast(value);
            }
            
            // 如果类型不匹配，尝试通过JSON转换
            try {
                String jsonString = objectMapper.writeValueAsString(value);
                return objectMapper.readValue(jsonString, clazz);
            } catch (JsonProcessingException e) {
                log.warn("缓存类型转换失败，key: {}, class: {}, 尝试直接转换", key, clazz.getName());
                // 如果JSON转换失败，尝试直接转换
                if (clazz == String.class) {
                    return clazz.cast(value.toString());
                }
                return null;
            }
        } catch (Exception e) {
            log.error("获取缓存失败，key: {}", key, e);
            return null;
        }
    }

    /**
     * 删除缓存
     */
    public void delete(String key) {
        try {
            redisTemplate.delete(key);
            log.debug("删除缓存成功，key: {}", key);
        } catch (Exception e) {
            log.error("删除缓存失败，key: {}", key, e);
        }
    }

    /**
     * 检查缓存是否存在
     */
    public boolean hasKey(String key) {
        try {
            Boolean exists = redisTemplate.hasKey(key);
            return exists != null && exists;
        } catch (Exception e) {
            log.error("检查缓存存在性失败，key: {}", key, e);
            return false;
        }
    }

    /**
     * 检查缓存是否存在（别名方法）
     */
    public boolean exists(String key) {
        return hasKey(key);
    }

    /**
     * 设置缓存过期时间
     */
    public void expire(String key, long timeout) {
        try {
            redisTemplate.expire(key, timeout, TimeUnit.SECONDS);
            log.debug("设置缓存过期时间成功，key: {}, timeout: {}s", key, timeout);
        } catch (Exception e) {
            log.error("设置缓存过期时间失败，key: {}", key, e);
        }
    }

    /**
     * 获取缓存过期时间
     */
    public long getExpire(String key) {
        try {
            Long expire = redisTemplate.getExpire(key, TimeUnit.SECONDS);
            return expire != null ? expire : -1;
        } catch (Exception e) {
            log.error("获取缓存过期时间失败，key: {}", key, e);
            return -1;
        }
    }

    /**
     * 批量删除缓存
     */
    public void deletePattern(String pattern) {
        if (!redisAvailable) {
            log.debug("Redis不可用，跳过批量删除: {}", pattern);
            return;
        }
        try {
            redisTemplate.delete(redisTemplate.keys(pattern));
            log.debug("批量删除缓存成功，pattern: {}", pattern);
        } catch (Exception e) {
            log.error("批量删除缓存失败，pattern: {}", pattern, e);
            redisAvailable = false;
        }
    }

    /**
     * 检查Redis是否可用
     */
    public boolean isRedisAvailable() {
        return redisAvailable;
    }

    /**
     * 重新检查Redis连接状态
     */
    public void recheckRedisConnection() {
        checkRedisConnection();
    }

    /**
     * 尝试获取分布式锁
     * @param lockKey 锁的键
     * @param lockValue 锁的值（通常是唯一标识）
     * @param expireTime 锁的过期时间（秒）
     * @return 是否获取成功
     */
    public boolean tryLock(String lockKey, String lockValue, long expireTime) {
        if (!redisAvailable) {
            log.debug("Redis不可用，锁获取失败: {}", lockKey);
            return false;
        }
        try {
            Boolean result = redisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, expireTime, TimeUnit.SECONDS);
            boolean success = result != null && result;
            log.debug("尝试获取锁，key: {}, value: {}, expire: {}s, result: {}", lockKey, lockValue, expireTime, success);
            return success;
        } catch (Exception e) {
            log.error("获取分布式锁失败，key: {}", lockKey, e);
            return false;
        }
    }

    /**
     * 释放分布式锁
     * @param lockKey 锁的键
     * @param lockValue 锁的值（用于验证锁的所有者）
     * @return 是否释放成功
     */
    public boolean releaseLock(String lockKey, String lockValue) {
        if (!redisAvailable) {
            log.debug("Redis不可用，锁释放失败: {}", lockKey);
            return false;
        }
        try {
            // 使用Spring Redis的脚本执行方式，兼容不同的Redis客户端
            String luaScript = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
            
            // 使用RedisTemplate的execute方法执行Lua脚本
            Long result = redisTemplate.execute((RedisCallback<Long>) connection -> {
                try {
                    // 使用connection的eval方法，这样可以兼容不同的Redis客户端实现
                    byte[] keyBytes = redisTemplate.getStringSerializer().serialize(lockKey);
                    byte[] valueBytes = redisTemplate.getStringSerializer().serialize(lockValue);
                    byte[] scriptBytes = luaScript.getBytes();
                    
                    Object evalResult = connection.eval(scriptBytes, org.springframework.data.redis.connection.ReturnType.INTEGER, 1, keyBytes, valueBytes);
                    return evalResult instanceof Long ? (Long) evalResult : 0L;
                } catch (Exception e) {
                    log.warn("Lua脚本执行失败，尝试简单删除: {}", e.getMessage());
                    // 如果Lua脚本执行失败，作为降级方案，先检查值再删除（非原子性但基本可用）
                    try {
                        Object currentValueObj = redisTemplate.opsForValue().get(lockKey);
                        String currentValue = currentValueObj != null ? currentValueObj.toString() : null;
                        if (lockValue.equals(currentValue)) {
                            Boolean deleted = redisTemplate.delete(lockKey);
                            return deleted != null && deleted ? 1L : 0L;
                        }
                        return 0L;
                    } catch (Exception ex) {
                        log.error("降级删除锁也失败: {}", ex.getMessage());
                        return 0L;
                    }
                }
            });
            
            boolean success = result != null && result > 0;
            log.debug("释放锁，key: {}, value: {}, result: {}", lockKey, lockValue, success);
            return success;
        } catch (Exception e) {
            log.error("释放分布式锁失败，key: {}", lockKey, e);
            return false;
        }
    }

    /**
     * 检查锁是否存在
     * @param lockKey 锁的键
     * @return 锁是否存在
     */
    public boolean isLocked(String lockKey) {
        return exists(lockKey);
    }
} 