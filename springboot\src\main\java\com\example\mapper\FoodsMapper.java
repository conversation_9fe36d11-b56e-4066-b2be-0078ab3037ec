package com.example.mapper;

import com.example.entity.Foods;
import java.util.List;

/**
 * 操作商品表相关数据接口
 */
public interface FoodsMapper {

    /**
     * 新增
     */
    int insert(Foods foods);

    /**
     * 删除
     */
    int deleteById(Integer id);

    /**
     * 修改
     */
    int updateById(Foods foods);

    /**
     * 根据ID查询
     */
    Foods selectById(Integer id);

    /**
     * 查询所有
     */
    List<Foods> selectAll(Foods foods);

    /**
     * 根据分类ID查询食物
     */
    List<Foods> selectByCategoryId(Integer categoryId);
    
    /**
     * 统计指定分类下的食物数量
     */
    int countByCategoryId(Integer categoryId);
    
    /**
     * 检查商品库存是否充足
     */
    int checkStock(Integer foodId, Integer requiredQuantity);
    
    /**
     * 扣减商品库存
     */
    int reduceStock(Integer foodId, Integer quantity);
    
    /**
     * 恢复商品库存
     */
    int restoreStock(Integer foodId, Integer quantity);
    
    /**
     * 获取商品当前库存
     */
    Integer getCurrentStock(Integer foodId);
}