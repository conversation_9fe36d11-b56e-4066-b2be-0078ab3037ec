<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.TableMapper">

    <sql id="Base_Column_List">
        id, table_number, seats, area, status, create_time, update_time
    </sql>

    <resultMap id="BaseResultMap" type="com.example.entity.Table">
        <id column="id" property="id" />
        <result column="table_number" property="tableNumber" />
        <result column="seats" property="seats" />
        <result column="area" property="area" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 查询所有餐桌（支持条件查询） -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM sf_table
        <where>
            <if test="id != null">AND id = #{id}</if>
            <if test="tableNumber != null and tableNumber != ''">AND table_number LIKE CONCAT('%', #{tableNumber}, '%')</if>
            <if test="area != null and area != ''">AND area = #{area}</if>
            <if test="status != null and status != ''">AND status = #{status}</if>
        </where>
        ORDER BY id ASC
    </select>

    <!-- 根据ID查询餐桌 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM sf_table
        WHERE id = #{id}
    </select>

    <!-- 根据餐桌号查询餐桌 -->
    <select id="selectByTableNumber" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM sf_table
        WHERE table_number = #{tableNumber}
    </select>

    <!-- 查询所有可用餐桌（状态为空闲且无未完成订单） -->
    <select id="selectAvailable" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM sf_table t
        WHERE t.status = '空闲'
        AND NOT EXISTS (
            SELECT 1 FROM sf_order o
            WHERE o.table_number = t.table_number
            AND o.status IN ('待支付', '已支付', '制作中', '待取餐')
        )
        ORDER BY t.table_number ASC
    </select>

    <!-- 新增餐桌 -->
    <insert id="insert" parameterType="com.example.entity.Table" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sf_table
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tableNumber != null">table_number,</if>
            <if test="seats != null">seats,</if>
            <if test="area != null">area,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="tableNumber != null">#{tableNumber},</if>
            <if test="seats != null">#{seats},</if>
            <if test="area != null">#{area},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <!-- 更新餐桌 -->
    <update id="updateById" parameterType="com.example.entity.Table">
        UPDATE sf_table
        <set>
            <if test="tableNumber != null">table_number = #{tableNumber},</if>
            <if test="seats != null">seats = #{seats},</if>
            <if test="area != null">area = #{area},</if>
            <if test="status != null">status = #{status},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除餐桌 -->
    <delete id="deleteById">
        DELETE FROM sf_table WHERE id = #{id}
    </delete>

    <!-- 检查餐桌是否被占用（有未完成订单） -->
    <select id="checkTableOccupied" resultType="int">
        SELECT COUNT(*)
        FROM sf_order
        WHERE table_number = #{tableNumber}
        AND status IN ('待支付', '已支付', '制作中', '待取餐')
    </select>

    <!-- 查询餐桌状态统计 -->
    <select id="selectTableStatusStats" resultType="map">
        SELECT 
            status as name,
            COUNT(*) as value
        FROM sf_table
        GROUP BY status
    </select>

    <!-- 查询餐桌详细状态（包含占用信息） -->
    <select id="selectTableStatusDetail" resultType="map">
        SELECT 
            t.id,
            t.table_number as tableNumber,
            t.seats,
            t.area,
            t.status as tableStatus,
            CASE 
                WHEN COUNT(o.id) > 0 THEN '占用中'
                ELSE '空闲'
            END as occupyStatus,
            COUNT(o.id) as activeOrders
        FROM sf_table t
        LEFT JOIN sf_order o ON t.table_number = o.table_number 
            AND o.status IN ('待支付', '已支付', '制作中', '待取餐')
        GROUP BY t.id, t.table_number, t.seats, t.area, t.status
        ORDER BY t.table_number ASC
    </select>

</mapper> 