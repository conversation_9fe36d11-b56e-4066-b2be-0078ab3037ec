<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.FoodReviewMapper">

    <sql id="Base_Column_List">
        id, user_id, user_name, food_id, food_name, order_id, rating, content, images, 
        status, reply_content, reply_time, reply_user, create_time, update_time, 
        is_anonymous, helpful_count
    </sql>

    <!-- 查询所有评价 -->
    <select id="selectAll" resultType="com.example.entity.FoodReview">
        select
        fr.id, fr.user_id, fr.user_name, fr.food_id, fr.food_name, fr.order_id, 
        fr.rating, fr.content, fr.images, fr.status, fr.reply_content, fr.reply_time, 
        fr.reply_user, fr.create_time, fr.update_time, fr.is_anonymous, fr.helpful_count
        from sf_food_review fr
        <where>
            <if test="id != null"> and fr.id = #{id}</if>
            <if test="userId != null"> and fr.user_id = #{userId}</if>
            <if test="foodId != null"> and fr.food_id = #{foodId}</if>
            <if test="rating != null"> and fr.rating = #{rating}</if>
            <if test="status != null and status != ''"> and fr.status = #{status}</if>
            <if test="userName != null and userName != ''"> and fr.user_name like concat('%', #{userName}, '%')</if>
            <if test="foodName != null and foodName != ''"> and fr.food_name like concat('%', #{foodName}, '%')</if>
        </where>
        order by fr.create_time desc
    </select>

    <!-- 根据ID查询评价 -->
    <select id="selectById" resultType="com.example.entity.FoodReview">
        select
        <include refid="Base_Column_List" />
        from sf_food_review
        where id = #{id}
    </select>

    <!-- 根据菜品ID查询评价列表（支持分页） -->
    <select id="selectByFoodId" resultType="com.example.entity.FoodReview">
        select
        fr.id, fr.user_id, fr.user_name, fr.food_id, fr.food_name, fr.order_id, 
        fr.rating, fr.content, fr.images, fr.status, fr.reply_content, fr.reply_time, 
        fr.reply_user, fr.create_time, fr.update_time, fr.is_anonymous, fr.helpful_count
        from sf_food_review fr
        where fr.food_id = #{foodId} and fr.status = '正常'
        <if test="rating != null"> and fr.rating = #{rating}</if>
        order by fr.create_time desc
        <if test="limit != null and offset != null">
            limit #{limit} offset #{offset}
        </if>
    </select>

    <!-- 根据用户ID查询评价历史 -->
    <select id="selectByUserId" resultType="com.example.entity.FoodReview">
        select
        fr.id, fr.user_id, fr.user_name, fr.food_id, fr.food_name, fr.order_id, 
        fr.rating, fr.content, fr.images, fr.status, fr.reply_content, fr.reply_time, 
        fr.reply_user, fr.create_time, fr.update_time, fr.is_anonymous, fr.helpful_count
        from sf_food_review fr
        where fr.user_id = #{userId}
        <if test="status != null and status != ''"> and fr.status = #{status}</if>
        <if test="rating != null"> and fr.rating = #{rating}</if>
        <if test="foodName != null and foodName != ''"> and fr.food_name like concat('%', #{foodName}, '%')</if>
        order by fr.create_time desc
        <if test="limit != null and offset != null">
            limit #{limit} offset #{offset}
        </if>
    </select>

    <!-- 检查用户是否已评价某菜品 -->
    <select id="checkUserReviewExists" resultType="int">
        select count(1)
        from sf_food_review
        where user_id = #{userId} and food_id = #{foodId}
    </select>

    <!-- 获取菜品评价统计信息 -->
    <select id="getReviewStats" resultType="com.example.entity.FoodReviewStats">
        select 
            #{foodId} as foodId,
            f.name as foodName,
            COALESCE(AVG(fr.rating), 0) as avgRating,
            COUNT(fr.id) as totalReviews,
            COUNT(CASE WHEN fr.rating = 5 THEN 1 END) as fiveStar,
            COUNT(CASE WHEN fr.rating = 4 THEN 1 END) as fourStar,
            COUNT(CASE WHEN fr.rating = 3 THEN 1 END) as threeStar,
            COUNT(CASE WHEN fr.rating = 2 THEN 1 END) as twoStar,
            COUNT(CASE WHEN fr.rating = 1 THEN 1 END) as oneStar,
            MAX(fr.create_time) as latestReviewTime
        from sf_food f
        left join sf_food_review fr on f.id = fr.food_id and fr.status = '正常'
        where f.id = #{foodId}
        group by f.id, f.name
    </select>

    <!-- 检查用户是否购买过某菜品 -->
    <select id="checkUserPurchased" resultType="int">
        select count(1)
        from sf_order o
        inner join sf_order_item oi on o.id = oi.order_id
        where o.sf_user_id = #{userId} and oi.food_id = #{foodId} 
        and o.status in ('已支付', '制作中', '待取餐', '已完成')
    </select>

    <!-- 根据状态查询评价列表 -->
    <select id="selectByStatus" resultType="com.example.entity.FoodReview">
        select
        fr.id, fr.user_id, fr.user_name, fr.food_id, fr.food_name, fr.order_id, 
        fr.rating, fr.content, fr.images, fr.status, fr.reply_content, fr.reply_time, 
        fr.reply_user, fr.create_time, fr.update_time, fr.is_anonymous, fr.helpful_count
        from sf_food_review fr
        where fr.status = #{status}
        <if test="foodId != null"> and fr.food_id = #{foodId}</if>
        <if test="userId != null"> and fr.user_id = #{userId}</if>
        <if test="rating != null"> and fr.rating = #{rating}</if>
        <if test="foodName != null and foodName != ''"> and fr.food_name like concat('%', #{foodName}, '%')</if>
        <if test="userName != null and userName != ''"> and fr.user_name like concat('%', #{userName}, '%')</if>
        order by fr.create_time desc
        <if test="limit != null and offset != null">
            limit #{limit} offset #{offset}
        </if>
    </select>

    <!-- 统计菜品评价数量 -->
    <select id="countByFoodId" resultType="int">
        select count(1)
        from sf_food_review
        where food_id = #{foodId} and status = '正常'
    </select>

    <!-- 统计用户评价数量 -->
    <select id="countByUserId" resultType="int">
        select count(1)
        from sf_food_review
        where user_id = #{userId}
    </select>

    <!-- 统计用户评价数量（支持筛选条件） -->
    <select id="countByUserIdWithFilter" resultType="int">
        select count(1)
        from sf_food_review
        where user_id = #{userId}
        <if test="rating != null"> and rating = #{rating}</if>
        <if test="status != null and status != ''"> and status = #{status}</if>
        <if test="foodName != null and foodName != ''"> and food_name like concat('%', #{foodName}, '%')</if>
    </select>

    <!-- 获取用户对菜品的评价 -->
    <select id="getUserFoodReview" resultType="com.example.entity.FoodReview">
        select
        <include refid="Base_Column_List" />
        from sf_food_review
        where user_id = #{userId} and food_id = #{foodId}
    </select>

    <!-- 新增评价 -->
    <insert id="insert" parameterType="com.example.entity.FoodReview" useGeneratedKeys="true" keyProperty="id">
        insert into sf_food_review
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="foodId != null">food_id,</if>
            <if test="foodName != null">food_name,</if>
            <if test="orderId != null">order_id,</if>
            <if test="rating != null">rating,</if>
            <if test="content != null">content,</if>
            <if test="images != null">images,</if>
            <if test="status != null">status,</if>
            <if test="replyContent != null">reply_content,</if>
            <if test="replyTime != null">reply_time,</if>
            <if test="replyUser != null">reply_user,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="isAnonymous != null">is_anonymous,</if>
            <if test="helpfulCount != null">helpful_count,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="foodId != null">#{foodId},</if>
            <if test="foodName != null">#{foodName},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="rating != null">#{rating},</if>
            <if test="content != null">#{content},</if>
            <if test="images != null">#{images},</if>
            <if test="status != null">#{status},</if>
            <if test="replyContent != null">#{replyContent},</if>
            <if test="replyTime != null">#{replyTime},</if>
            <if test="replyUser != null">#{replyUser},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="isAnonymous != null">#{isAnonymous},</if>
            <if test="helpfulCount != null">#{helpfulCount},</if>
        </trim>
    </insert>

    <!-- 删除评价 -->
    <delete id="deleteById">
        delete from sf_food_review
        where id = #{id}
    </delete>

    <!-- 批量删除评价 -->
    <delete id="deleteByIds">
        delete from sf_food_review
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 修改评价 -->
    <update id="updateById" parameterType="com.example.entity.FoodReview">
        update sf_food_review
        <set>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="foodId != null">food_id = #{foodId},</if>
            <if test="foodName != null">food_name = #{foodName},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="rating != null">rating = #{rating},</if>
            <if test="content != null">content = #{content},</if>
            <if test="images != null">images = #{images},</if>
            <if test="status != null">status = #{status},</if>
            <if test="replyContent != null">reply_content = #{replyContent},</if>
            <if test="replyTime != null">reply_time = #{replyTime},</if>
            <if test="replyUser != null">reply_user = #{replyUser},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isAnonymous != null">is_anonymous = #{isAnonymous},</if>
            <if test="helpfulCount != null">helpful_count = #{helpfulCount},</if>
        </set>
        where id = #{id}
    </update>

    <!-- 更新评价状态 -->
    <update id="updateStatus">
        update sf_food_review
        set status = #{status}
        where id = #{id}
    </update>

    <!-- 更新菜品评价统计信息 -->
    <update id="updateFoodReviewStats">
        update sf_food
        set average_rating = #{avgRating}, 
            review_count = #{reviewCount}, 
            last_review_time = #{lastReviewTime}
        where id = #{foodId}
    </update>

</mapper> 