package com.example.controller;

import com.example.common.Result;
import com.example.entity.FoodReview;
import com.example.service.FoodReviewService;
import com.example.utils.TokenUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 菜品评价前端操作接口
 **/
@RestController
@RequestMapping("/foodReview")
public class FoodReviewController {

    @Resource
    private FoodReviewService foodReviewService;

    /**
     * 添加评价
     */
    @PostMapping("/add")
    public Result add(@RequestBody FoodReview foodReview, HttpServletRequest request) {
        // 从token中获取当前用户ID
        String token = request.getHeader("token");
        if (token != null && !token.isEmpty()) {
            Integer currentUserId = TokenUtils.getCurrentUser().getId();
            foodReview.setUserId(currentUserId);
        }
        
        return foodReviewService.addReview(foodReview);
    }

    /**
     * 检查用户是否可以评价某菜品
     */
    @GetMapping("/canReview")
    public Result canReview(@RequestParam Integer foodId, HttpServletRequest request) {
        String token = request.getHeader("token");
        if (token == null || token.isEmpty()) {
            return Result.error("401", "请先登录");
        }
        
        Integer currentUserId = TokenUtils.getCurrentUser().getId();
        return foodReviewService.checkReviewPermission(currentUserId, foodId);
    }

    /**
     * 获取菜品评价列表（分页）
     */
    @GetMapping("/food/{foodId}")
    public Result getReviewsByFoodId(@PathVariable Integer foodId,
                                     @RequestParam(defaultValue = "1") Integer pageNum,
                                     @RequestParam(defaultValue = "10") Integer pageSize,
                                     @RequestParam(required = false) Integer rating) {
        return foodReviewService.getReviewsByFoodId(foodId, pageNum, pageSize, rating);
    }

    /**
     * 获取用户评价列表
     */
    @GetMapping("/user/{userId}")
    public Result getReviewsByUserId(@PathVariable Integer userId,
                                     @RequestParam(defaultValue = "1") Integer pageNum,
                                     @RequestParam(defaultValue = "10") Integer pageSize) {
        return foodReviewService.getReviewsByUserId(userId, pageNum, pageSize);
    }

    /**
     * 获取当前用户的评价列表
     */
    @GetMapping("/my")
    public Result getMyReviews(@RequestParam(defaultValue = "1") Integer pageNum,
                               @RequestParam(defaultValue = "10") Integer pageSize,
                               @RequestParam(required = false) String rating,
                               @RequestParam(required = false) String status,
                               @RequestParam(required = false) String foodName,
                               HttpServletRequest request) {
        String token = request.getHeader("token");
        if (token == null || token.isEmpty()) {
            return Result.error("401", "请先登录");
        }
        
        Integer currentUserId = TokenUtils.getCurrentUser().getId();
        return foodReviewService.getMyReviews(currentUserId, pageNum, pageSize, rating, status, foodName);
    }

    /**
     * 获取评价统计信息
     */
    @GetMapping("/stats/{foodId}")
    public Result getReviewStats(@PathVariable Integer foodId) {
        return foodReviewService.getReviewStats(foodId);
    }

    /**
     * 获取用户对某菜品的评价
     */
    @GetMapping("/getUserReview")
    public Result getUserFoodReview(@RequestParam Integer foodId, HttpServletRequest request) {
        String token = request.getHeader("token");
        if (token == null || token.isEmpty()) {
            return Result.error("401", "请先登录");
        }
        
        Integer currentUserId = TokenUtils.getCurrentUser().getId();
        return foodReviewService.getUserFoodReview(currentUserId, foodId);
    }

    // ========== 管理端接口 ==========

    /**
     * 管理员查看所有评价
     */
    @GetMapping("/manage/list")
    public Result getAllReviews(@RequestParam(defaultValue = "1") Integer pageNum,
                                @RequestParam(defaultValue = "10") Integer pageSize,
                                @RequestParam(required = false) String status,
                                @RequestParam(required = false) String foodName,
                                @RequestParam(required = false) String userName,
                                @RequestParam(required = false) Integer rating) {
        return foodReviewService.getAllReviews(pageNum, pageSize, status, foodName, userName, rating);
    }

    /**
     * 根据ID查询评价详情
     */
    @GetMapping("/manage/{id}")
    public Result selectById(@PathVariable Integer id) {
        FoodReview review = foodReviewService.selectById(id);
        return Result.success(review);
    }

    /**
     * 删除评价
     */
    @DeleteMapping("/manage/{id}")
    public Result deleteReview(@PathVariable Integer id) {
        return foodReviewService.deleteReview(id);
    }

    /**
     * 批量删除评价
     */
    @DeleteMapping("/manage/batch")
    public Result deleteBatch(@RequestBody List<Integer> ids) {
        return foodReviewService.deleteBatch(ids);
    }

    /**
     * 更新评价状态
     */
    @PutMapping("/manage/status")
    public Result updateStatus(@RequestBody Map<String, Object> params) {
        Integer id = (Integer) params.get("id");
        String status = (String) params.get("status");
        return foodReviewService.updateReviewStatus(id, status);
    }

    /**
     * 管理员回复评价
     */
    @PostMapping("/manage/reply")
    public Result replyReview(@RequestBody Map<String, Object> params, HttpServletRequest request) {
        Integer reviewId = (Integer) params.get("reviewId");
        String replyContent = (String) params.get("replyContent");
        
        // 获取当前管理员信息
        String replyUser = "管理员";
        try {
            String token = request.getHeader("token");
            if (token != null && !token.isEmpty()) {
                replyUser = TokenUtils.getCurrentUser().getName();
            }
        } catch (Exception e) {
            // 使用默认值
        }
        
        return foodReviewService.replyReview(reviewId, replyContent, replyUser);
    }

    /**
     * 修改评价（管理员）
     */
    @PutMapping("/manage/update")
    public Result updateById(@RequestBody FoodReview foodReview) {
        return foodReviewService.updateReview(foodReview);
    }
} 