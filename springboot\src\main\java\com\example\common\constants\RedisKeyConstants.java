package com.example.common.constants;

/**
 * Redis键名常量类
 */
public class RedisKeyConstants {

    /**
     * 用户信息缓存键前缀
     */
    public static final String USER_INFO_PREFIX = "user:info:";
    
    /**
     * 管理员信息缓存键前缀
     */
    public static final String ADMIN_INFO_PREFIX = "admin:info:";
    
    /**
     * 商家信息缓存键前缀
     */
    public static final String BUSINESS_INFO_PREFIX = "business:info:";
    
    /**
     * 商品信息缓存键前缀
     */
    public static final String GOODS_INFO_PREFIX = "goods:info:";
    
    /**
     * 公告列表缓存键
     */
    public static final String NOTICE_LIST = "notice:list";
    
    /**
     * 订单统计缓存键
     */
    public static final String ORDER_STATS_STATUS = "stats:order:status";
    
    /**
     * 月度订单统计缓存键前缀
     */
    public static final String ORDER_STATS_MONTHLY_PREFIX = "stats:order:monthly:";
    
    /**
     * 博客信息缓存键前缀
     */
    public static final String BLOG_INFO_PREFIX = "blog:info:";
    
    /**
     * 用户名查询缓存键前缀
     */
    public static final String USER_BY_USERNAME_PREFIX = "user:username:";
    
    /**
     * 管理员用户名查询缓存键前缀
     */
    public static final String ADMIN_BY_USERNAME_PREFIX = "admin:username:";
    
    /**
     * 商家用户名查询缓存键前缀
     */
    public static final String BUSINESS_BY_USERNAME_PREFIX = "business:username:";
    
    /**
     * 短信验证码缓存键前缀
     */
    public static final String SMS_VERIFY_CODE_PREFIX = "sms:code:";
    
    /**
     * 短信发送间隔限制缓存键前缀
     */
    public static final String SMS_SEND_INTERVAL_PREFIX = "sms:interval:";
    
    /**
     * 缓存过期时间常量（秒）
     */
    public static class ExpireTime {
        public static final int USER_INFO = 30 * 60;        // 30分钟
        public static final int GOODS_INFO = 60 * 60;       // 1小时
        public static final int NOTICE_LIST = 10 * 60;      // 10分钟
        public static final int ORDER_STATS = 5 * 60;       // 5分钟
        public static final int BLOG_INFO = 30 * 60;        // 30分钟
        public static final int USERNAME_QUERY = 60 * 60;   // 1小时
    }
} 