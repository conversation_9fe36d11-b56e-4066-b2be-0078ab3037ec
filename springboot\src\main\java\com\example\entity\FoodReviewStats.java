package com.example.entity;

import java.io.Serializable;

/**
 * 菜品评价统计信息
 */
public class FoodReviewStats implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 菜品ID */
    private Integer foodId;
    /** 菜品名称 */
    private String foodName;
    /** 平均评分 */
    private Double avgRating;
    /** 总评价数 */
    private Integer totalReviews;
    /** 5星评价数 */
    private Integer fiveStar;
    /** 4星评价数 */
    private Integer fourStar;
    /** 3星评价数 */
    private Integer threeStar;
    /** 2星评价数 */
    private Integer twoStar;
    /** 1星评价数 */
    private Integer oneStar;
    /** 最新评价时间 */
    private String latestReviewTime;

    public Integer getFoodId() {
        return foodId;
    }

    public void setFoodId(Integer foodId) {
        this.foodId = foodId;
    }

    public String getFoodName() {
        return foodName;
    }

    public void setFoodName(String foodName) {
        this.foodName = foodName;
    }

    public Double getAvgRating() {
        return avgRating;
    }

    public void setAvgRating(Double avgRating) {
        this.avgRating = avgRating;
    }

    public Integer getTotalReviews() {
        return totalReviews;
    }

    public void setTotalReviews(Integer totalReviews) {
        this.totalReviews = totalReviews;
    }

    public Integer getFiveStar() {
        return fiveStar;
    }

    public void setFiveStar(Integer fiveStar) {
        this.fiveStar = fiveStar;
    }

    public Integer getFourStar() {
        return fourStar;
    }

    public void setFourStar(Integer fourStar) {
        this.fourStar = fourStar;
    }

    public Integer getThreeStar() {
        return threeStar;
    }

    public void setThreeStar(Integer threeStar) {
        this.threeStar = threeStar;
    }

    public Integer getTwoStar() {
        return twoStar;
    }

    public void setTwoStar(Integer twoStar) {
        this.twoStar = twoStar;
    }

    public Integer getOneStar() {
        return oneStar;
    }

    public void setOneStar(Integer oneStar) {
        this.oneStar = oneStar;
    }

    public String getLatestReviewTime() {
        return latestReviewTime;
    }

    public void setLatestReviewTime(String latestReviewTime) {
        this.latestReviewTime = latestReviewTime;
    }
} 