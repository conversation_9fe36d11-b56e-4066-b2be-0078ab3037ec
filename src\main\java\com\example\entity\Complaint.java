package com.example.entity;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 投诉表
 */
public class Complaint {
    /** 主键 */
    private Integer id;
    /** 标题 */
    private String title;
    /** 投诉内容 */
    private String sfContent;
    /** 投诉图片 */
    private String sfImage;
    /** 投诉状态 */
    private String status;
    /** 投诉日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sfComplaintDate;
    /** 回复信息 */
    private String reply;
    /** 用户ID */
    private Integer sfUserId;
    /** 投诉类型 */
    private String complaintType;
    /** 联系电话 */
    private String phone;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSfContent() {
        return sfContent;
    }

    public void setSfContent(String sfContent) {
        this.sfContent = sfContent;
    }

    public String getSfImage() {
        return sfImage;
    }

    public void setSfImage(String sfImage) {
        this.sfImage = sfImage;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getSfComplaintDate() {
        return sfComplaintDate;
    }

    public void setSfComplaintDate(Date sfComplaintDate) {
        this.sfComplaintDate = sfComplaintDate;
    }

    public String getReply() {
        return reply;
    }

    public void setReply(String reply) {
        this.reply = reply;
    }

    public Integer getSfUserId() {
        return sfUserId;
    }

    public void setSfUserId(Integer sfUserId) {
        this.sfUserId = sfUserId;
    }

    public String getComplaintType() {
        return complaintType;
    }

    public void setComplaintType(String complaintType) {
        this.complaintType = complaintType;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
} 