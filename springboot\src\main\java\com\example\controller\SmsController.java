package com.example.controller;

import cn.hutool.core.util.StrUtil;
import com.example.common.Result;
import com.example.common.enums.ResultCodeEnum;
import com.example.service.SmsService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 短信服务控制器
 */
@RestController
@RequestMapping("/sms")
public class SmsController {

    @Resource
    private SmsService smsService;

    /**
     * 发送验证码
     */
    @PostMapping("/sendCode")
    public Result sendVerifyCode(@RequestParam String phoneNumber) {
        if (StrUtil.isBlank(phoneNumber)) {
            return Result.error(ResultCodeEnum.PARAM_LOST_ERROR);
        }

        // 简单的手机号格式验证
        if (!isValidPhoneNumber(phoneNumber)) {
            return Result.error("400", "手机号格式不正确");
        }

        try {
            // 检查发送间隔
            long remainTime = smsService.getSendIntervalRemainTime(phoneNumber);
            if (remainTime > 0) {
                Map<String, Object> data = new HashMap<>();
                data.put("remainTime", remainTime);
                return Result.error("400", "发送过于频繁，请" + remainTime + "秒后重试");
            }

            boolean success = smsService.sendVerifyCode(phoneNumber);
            if (success) {
                Map<String, Object> data = new HashMap<>();
                data.put("message", "验证码发送成功");
                data.put("phoneNumber", phoneNumber);
                return Result.success(data);
            } else {
                return Result.error("500", "验证码发送失败，请稍后重试");
            }
        } catch (Exception e) {
            return Result.error("500", e.getMessage());
        }
    }

    /**
     * 验证验证码
     */
    @PostMapping("/verifyCode")
    public Result verifyCode(@RequestParam String phoneNumber, @RequestParam String code) {
        if (StrUtil.isBlank(phoneNumber) || StrUtil.isBlank(code)) {
            return Result.error(ResultCodeEnum.PARAM_LOST_ERROR);
        }

        boolean isValid = smsService.verifyCode(phoneNumber, code);
        if (isValid) {
            return Result.success("验证码验证成功");
        } else {
            return Result.error("400", "验证码错误或已过期");
        }
    }

    /**
     * 获取验证码状态
     */
    @GetMapping("/codeStatus")
    public Result getCodeStatus(@RequestParam String phoneNumber) {
        if (StrUtil.isBlank(phoneNumber)) {
            return Result.error(ResultCodeEnum.PARAM_LOST_ERROR);
        }

        Map<String, Object> data = new HashMap<>();
        data.put("codeRemainTime", smsService.getCodeRemainTime(phoneNumber));
        data.put("sendIntervalRemainTime", smsService.getSendIntervalRemainTime(phoneNumber));
        
        return Result.success(data);
    }

    /**
     * 简单的手机号格式验证
     */
    private boolean isValidPhoneNumber(String phoneNumber) {
        if (StrUtil.isBlank(phoneNumber)) {
            return false;
        }
        // 简单验证：11位数字，以1开头
        return phoneNumber.matches("^1[3-9]\\d{9}$");
    }
} 