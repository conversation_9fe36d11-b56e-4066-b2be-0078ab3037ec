package com.example.entity;

import java.io.Serializable;

/**
 * 菜品评价表
 */
public class FoodReview implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Integer id;
    /** 评价用户ID */
    private Integer userId;
    /** 用户昵称（冗余字段，提高查询效率） */
    private String userName;
    /** 菜品ID */
    private Integer foodId;
    /** 菜品名称（冗余字段） */
    private String foodName;
    /** 关联订单ID */
    private Integer orderId;
    /** 评分（1-5星） */
    private Integer rating;
    /** 评价内容 */
    private String content;
    /** 评价图片（多张图片以逗号分隔） */
    private String images;
    /** 评价状态：正常、已删除、待审核 */
    private String status;
    /** 商家/管理员回复内容 */
    private String replyContent;
    /** 回复时间 */
    private String replyTime;
    /** 回复人 */
    private String replyUser;
    /** 评价时间 */
    private String createTime;
    /** 更新时间 */
    private String updateTime;
    /** 是否匿名评价（0-否，1-是） */
    private Integer isAnonymous;
    /** 有用评价数量 */
    private Integer helpfulCount;

    // 关联对象（用于连表查询）
    /** 用户对象 */
    private User user;
    /** 菜品对象 */
    private Foods food;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getFoodId() {
        return foodId;
    }

    public void setFoodId(Integer foodId) {
        this.foodId = foodId;
    }

    public String getFoodName() {
        return foodName;
    }

    public void setFoodName(String foodName) {
        this.foodName = foodName;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getRating() {
        return rating;
    }

    public void setRating(Integer rating) {
        this.rating = rating;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getImages() {
        return images;
    }

    public void setImages(String images) {
        this.images = images;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getReplyContent() {
        return replyContent;
    }

    public void setReplyContent(String replyContent) {
        this.replyContent = replyContent;
    }

    public String getReplyTime() {
        return replyTime;
    }

    public void setReplyTime(String replyTime) {
        this.replyTime = replyTime;
    }

    public String getReplyUser() {
        return replyUser;
    }

    public void setReplyUser(String replyUser) {
        this.replyUser = replyUser;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getIsAnonymous() {
        return isAnonymous;
    }

    public void setIsAnonymous(Integer isAnonymous) {
        this.isAnonymous = isAnonymous;
    }

    public Integer getHelpfulCount() {
        return helpfulCount;
    }

    public void setHelpfulCount(Integer helpfulCount) {
        this.helpfulCount = helpfulCount;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Foods getFood() {
        return food;
    }

    public void setFood(Foods food) {
        this.food = food;
    }
} 