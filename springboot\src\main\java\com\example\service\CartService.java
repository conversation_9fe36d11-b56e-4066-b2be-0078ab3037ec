package com.example.service;

import com.example.common.Result;
import com.example.common.enums.ResultCodeEnum;
import com.example.common.service.CacheService;
import com.example.entity.*;
import com.example.exception.CustomException;
import com.example.mapper.CartMapper;
import com.example.mapper.FoodsMapper;
import com.example.mapper.DingdanMapper;
import com.example.mapper.OrderItemMapper;
import com.example.service.FoodsService;
import com.example.utils.TokenUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 购物车业务处理
 */
@Service
public class CartService {

    @Resource
    private CartMapper cartMapper;
    
    @Resource
    private FoodsMapper foodsMapper;
    
    @Resource
    private DingdanMapper dingdanMapper;
    
    @Resource
    private OrderItemMapper orderItemMapper;
    
    @Resource
    private DingdanService dingdanService;
    
    @Resource
    private TableService tableService;
    
    @Resource
    private CacheService cacheService;
    
    @Resource
    private FoodsService foodsService;

    /**
     * 获取用户购物车列表（含商品详情和价格计算）
     */
    public List<CartVO> getUserCartList(Integer userId) {
        if (userId == null) {
            Account currentUser = TokenUtils.getCurrentUser();
            userId = currentUser.getId();
        }
        return cartMapper.selectCartVOByUserId(userId);
    }

    /**
     * 添加商品到购物车
     */
    @Transactional
    public void addToCart(Integer userId, Integer foodId, Integer quantity) {
        if (userId == null) {
            Account currentUser = TokenUtils.getCurrentUser();
            userId = currentUser.getId();
        }
        
        if (foodId == null || quantity == null || quantity <= 0) {
            throw new CustomException(ResultCodeEnum.PARAM_LOST_ERROR);
        }
        
        // 验证商品是否存在
        Foods food = foodsMapper.selectById(foodId);
        if (food == null) {
            throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, "商品不存在");
        }
        
        // 检查商品是否上架
        if (!"上架".equals(food.getSfShelfStatus())) {
            throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, "商品已下架");
        }
        
        // 检查库存是否充足（包括购物车中已有的数量）
        Cart existingCart = cartMapper.selectByUserIdAndFoodId(userId, foodId);
        int totalQuantityNeeded = quantity;
        if (existingCart != null) {
            totalQuantityNeeded += existingCart.getQuantity();
        }
        
        if (!foodsService.checkStock(foodId, totalQuantityNeeded)) {
            Integer currentStock = foodsService.getCurrentStock(foodId);
            throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, 
                food.getName() + " 库存不足！当前库存：" + currentStock + "，需要：" + totalQuantityNeeded);
        }
        
        // 检查是否已在购物车中
        if (existingCart != null) {
            // 已存在，更新数量
            int newQuantity = existingCart.getQuantity() + quantity;
            cartMapper.updateQuantity(existingCart.getId(), newQuantity);
        } else {
            // 不存在，新增
            Cart cart = new Cart();
            cart.setUserId(userId);
            cart.setFoodId(foodId);
            cart.setQuantity(quantity);
            cartMapper.insert(cart);
        }
    }

    /**
     * 更新商品数量
     */
    @Transactional
    public void updateQuantity(Integer userId, Integer foodId, Integer quantity) {
        if (userId == null) {
            Account currentUser = TokenUtils.getCurrentUser();
            userId = currentUser.getId();
        }
        
        if (quantity == null || quantity < 0) {
            throw new CustomException(ResultCodeEnum.PARAM_LOST_ERROR);
        }
        
        if (quantity == 0) {
            // 数量为0，删除该项
            cartMapper.deleteByUserIdAndFoodId(userId, foodId);
            return;
        }
        
        Cart cart = cartMapper.selectByUserIdAndFoodId(userId, foodId);
        if (cart == null) {
            throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, "购物车中不存在该商品");
        }
        
        // 检查库存是否充足
        if (!foodsService.checkStock(foodId, quantity)) {
            Foods food = foodsService.selectById(foodId);
            Integer currentStock = foodsService.getCurrentStock(foodId);
            String foodName = food != null ? food.getName() : "商品ID:" + foodId;
            throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, 
                foodName + " 库存不足！当前库存：" + currentStock + "，需要：" + quantity);
        }
        
        cartMapper.updateQuantity(cart.getId(), quantity);
    }

    /**
     * 删除购物车商品
     */
    @Transactional
    public void removeFromCart(Integer userId, Integer foodId) {
        if (userId == null) {
            Account currentUser = TokenUtils.getCurrentUser();
            userId = currentUser.getId();
        }
        
        cartMapper.deleteByUserIdAndFoodId(userId, foodId);
    }

    /**
     * 清空购物车
     */
    @Transactional
    public void clearCart(Integer userId) {
        if (userId == null) {
            Account currentUser = TokenUtils.getCurrentUser();
            userId = currentUser.getId();
        }
        
        cartMapper.deleteByUserId(userId);
    }

    /**
     * 购物车结算（不指定餐桌）
     */
    @Transactional
    public Integer checkout(Integer userId, String remark) {
        return checkout(userId, remark, null);
    }
    
    /**
     * 购物车结算（指定餐桌）
     */
    @Transactional
    public Integer checkout(Integer userId, String remark, String tableNumber) {
        if (userId == null) {
            Account currentUser = TokenUtils.getCurrentUser();
            userId = currentUser.getId();
        }
        
        // 获取购物车商品列表
        List<CartVO> cartItems = cartMapper.selectCartVOByUserId(userId);
        if (cartItems == null || cartItems.isEmpty()) {
            throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, "购物车为空");
        }
        
        // 批量检查所有商品库存是否充足
        List<FoodsService.StockCheckItem> stockCheckList = new ArrayList<>();
        for (CartVO item : cartItems) {
            stockCheckList.add(new FoodsService.StockCheckItem(item.getFoodId(), item.getQuantity()));
        }
        
        List<String> insufficientItems = foodsService.batchCheckStock(stockCheckList);
        if (!insufficientItems.isEmpty()) {
            throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, 
                "以下商品库存不足：" + String.join("、", insufficientItems));
        }
        
        // 计算总金额
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (CartVO item : cartItems) {
            totalAmount = totalAmount.add(item.getSubtotal());
        }
        
        // 处理餐桌信息（使用分布式锁）
        String lockKey = null;
        String lockValue = null;
        if (tableNumber != null && !tableNumber.trim().isEmpty()) {
            // 生成锁的键和值
            lockKey = "table:lock:" + tableNumber;
            lockValue = "user:" + userId + ":" + System.currentTimeMillis();
            
            // 尝试获取餐桌锁（30秒过期）
            if (!cacheService.tryLock(lockKey, lockValue, 30)) {
                throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, "该餐桌正在被其他用户选择，请稍后重试");
            }
            
            try {
                // 检查餐桌是否存在且可用
                if (!tableService.isTableAvailable(tableNumber)) {
                    throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, "该餐桌不可用或已被占用");
                }
                
                // 检查餐桌占用冲突
                tableService.checkTableConflict(tableNumber);
            } catch (Exception e) {
                // 如果检查失败，释放锁
                cacheService.releaseLock(lockKey, lockValue);
                throw e;
            }
        }
        
        // 创建订单
        Dingdan order = new Dingdan();
        Account currentUser = TokenUtils.getCurrentUser();
        order.setSfUserName(currentUser.getUsername());
        order.setSfUserId(userId);
        
        // 设置餐桌信息
        if (tableNumber != null && !tableNumber.trim().isEmpty()) {
            order.setTableNumber(tableNumber);
            // 获取餐桌ID
            com.example.entity.Table table = tableService.selectByTableNumber(tableNumber);
            if (table != null) {
                order.setTableId(table.getId());
            }
        }
        
        order.setStatus("已支付"); // 购物车结算默认为已支付状态
        order.setSfOrderNumber(dingdanService.generateOrderId());
        order.setSfCreateTime(new Date());
        order.setSfRemark(remark);
        order.setSfTotalPrice(totalAmount);
        
        dingdanMapper.insert(order);
        
        // 扣减商品库存（在订单创建成功后立即扣减）
        try {
            for (CartVO cartItem : cartItems) {
                foodsService.reduceStock(cartItem.getFoodId(), cartItem.getQuantity());
            }
        } catch (Exception e) {
            // 库存扣减失败，需要回滚订单（抛出异常让事务回滚）
            throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, 
                "库存扣减失败：" + e.getMessage());
        }
        
        // 如果有餐桌，更新餐桌状态为使用中
        if (tableNumber != null && !tableNumber.trim().isEmpty()) {
            com.example.entity.Table table = tableService.selectByTableNumber(tableNumber);
            if (table != null && "空闲".equals(table.getStatus())) {
                tableService.updateTableStatus(table.getId(), "使用中");
            }
        }
        
        // 创建订单详情
        List<OrderItem> orderItems = new ArrayList<>();
        for (CartVO cartItem : cartItems) {
            OrderItem orderItem = new OrderItem();
            orderItem.setOrderId(order.getId());
            orderItem.setFoodId(cartItem.getFoodId());
            orderItem.setFoodName(cartItem.getFoodName());
            orderItem.setFoodPrice(cartItem.getFoodPrice());
            orderItem.setQuantity(cartItem.getQuantity());
            orderItem.setSubtotal(cartItem.getSubtotal());
            orderItems.add(orderItem);
        }
        
        if (!orderItems.isEmpty()) {
            orderItemMapper.insertBatch(orderItems);
        }
        
        // 清空购物车
        cartMapper.deleteByUserId(userId);
        
        // 释放餐桌锁
        if (lockKey != null && lockValue != null) {
            cacheService.releaseLock(lockKey, lockValue);
        }
        
        return order.getId();
    }

    /**
     * 获取购物车统计信息
     */
    public CartStatistics getCartStatistics(Integer userId) {
        if (userId == null) {
            Account currentUser = TokenUtils.getCurrentUser();
            userId = currentUser.getId();
        }
        
        List<CartVO> cartItems = cartMapper.selectCartVOByUserId(userId);
        
        if (cartItems == null || cartItems.isEmpty()) {
            return new CartStatistics();
        }
        
        int totalQuantity = 0;
        int totalItems = cartItems.size();
        BigDecimal totalAmount = BigDecimal.ZERO;
        
        for (CartVO item : cartItems) {
            totalQuantity += item.getQuantity();
            totalAmount = totalAmount.add(item.getSubtotal());
        }
        
        return new CartStatistics(totalQuantity, totalItems, totalAmount);
    }

    /**
     * 获取用户购物车商品数量
     */
    public Integer getCartCount(Integer userId) {
        if (userId == null) {
            Account currentUser = TokenUtils.getCurrentUser();
            userId = currentUser.getId();
        }
        
        Integer count = cartMapper.countByUserId(userId);
        return count != null ? count : 0;
    }
} 