package com.example.service;

import cn.hutool.core.util.ObjectUtil;
import com.example.common.Constants;
import com.example.common.constants.RedisKeyConstants;
import com.example.common.enums.ResultCodeEnum;
import com.example.common.enums.RoleEnum;
import com.example.common.service.CacheService;
import com.example.entity.Account;
import com.example.entity.Admin;
import com.example.exception.CustomException;
import com.example.mapper.AdminMapper;
import com.example.utils.TokenUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 管理员业务处理
 **/
@Service
public class AdminService {

    @Resource
    private AdminMapper adminMapper;

    @Resource
    private CacheService cacheService;

    /**
     * 新增
     */
    public void add(Admin admin) {
        Admin dbAdmin = adminMapper.selectByUsername(admin.getUsername());
        if (ObjectUtil.isNotNull(dbAdmin)) {
            throw new CustomException(ResultCodeEnum.USER_EXIST_ERROR);
        }
        if (ObjectUtil.isEmpty(admin.getPassword())) {
            admin.setPassword(Constants.USER_DEFAULT_PASSWORD);
        }
        if (ObjectUtil.isEmpty(admin.getName())) {
            admin.setName(admin.getUsername());
        }
        admin.setRole(RoleEnum.ADMIN.name());
        adminMapper.insert(admin);
    }

    /**
     * 删除
     */
    public void deleteById(Integer id) {
        adminMapper.deleteById(id);
        // 删除缓存
        cacheService.delete(RedisKeyConstants.ADMIN_INFO_PREFIX + id);
    }

    /**
     * 批量删除
     */
    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            deleteById(id);
        }
    }

    /**
     * 修改
     */
    public void updateById(Admin admin) {
        adminMapper.updateById(admin);
        // 更新缓存
        if (admin.getId() != null) {
            cacheService.set(RedisKeyConstants.ADMIN_INFO_PREFIX + admin.getId(), admin, RedisKeyConstants.ExpireTime.USER_INFO);
            // 如果用户名发生变化，删除用户名缓存
            if (admin.getUsername() != null) {
                cacheService.delete(RedisKeyConstants.ADMIN_BY_USERNAME_PREFIX + admin.getUsername());
            }
        }
    }

    /**
     * 根据ID查询（带缓存）
     */
    public Admin selectById(Integer id) {
        if (id == null) {
            return null;
        }
        
        // 先从缓存获取
        String cacheKey = RedisKeyConstants.ADMIN_INFO_PREFIX + id;
        Admin admin = cacheService.get(cacheKey, Admin.class);
        if (admin != null) {
            return admin;
        }
        
        // 缓存未命中，从数据库查询
        admin = adminMapper.selectById(id);
        if (admin != null) {
            // 写入缓存
            cacheService.set(cacheKey, admin, RedisKeyConstants.ExpireTime.USER_INFO);
        }
        return admin;
    }

    /**
     * 查询所有
     */
    public List<Admin> selectAll(Admin admin) {
        return adminMapper.selectAll(admin);
    }

    /**
     * 分页查询
     */
    public PageInfo<Admin> selectPage(Admin admin, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Admin> list = adminMapper.selectAll(admin);
        return PageInfo.of(list);
    }

    /**
     * 登录
     */
    public Account login(Account account) {
        Account dbAdmin = adminMapper.selectByUsername(account.getUsername());
        if (ObjectUtil.isNull(dbAdmin)) {
            throw new CustomException(ResultCodeEnum.USER_NOT_EXIST_ERROR);
        }
        if (!account.getPassword().equals(dbAdmin.getPassword())) {
            throw new CustomException(ResultCodeEnum.USER_ACCOUNT_ERROR);
        }
        // 生成token
        String tokenData = dbAdmin.getId() + "-" + RoleEnum.ADMIN.name();
        String token = TokenUtils.createToken(tokenData, dbAdmin.getPassword());
        dbAdmin.setToken(token);
        
        // 缓存管理员信息
        cacheService.set(RedisKeyConstants.ADMIN_INFO_PREFIX + dbAdmin.getId(), dbAdmin, RedisKeyConstants.ExpireTime.USER_INFO);
        
        return dbAdmin;
    }

    /**
     * 注册
     */
    public void register(Account account) {
        Admin admin = new Admin();
        BeanUtils.copyProperties(account, admin);
        add(admin);
    }

    /**
     * 修改密码
     */
    public void updatePassword(Account account) {
        Admin dbAdmin = adminMapper.selectByUsername(account.getUsername());
        if (ObjectUtil.isNull(dbAdmin)) {
            throw new CustomException(ResultCodeEnum.USER_NOT_EXIST_ERROR);
        }
        if (!account.getPassword().equals(dbAdmin.getPassword())) {
            throw new CustomException(ResultCodeEnum.PARAM_PASSWORD_ERROR);
        }
        dbAdmin.setPassword(account.getNewPassword());
        adminMapper.updateById(dbAdmin);
        
        // 清除缓存，强制重新加载
        cacheService.delete(RedisKeyConstants.ADMIN_INFO_PREFIX + dbAdmin.getId());
        cacheService.delete(RedisKeyConstants.ADMIN_BY_USERNAME_PREFIX + dbAdmin.getUsername());
    }

    /**
     * 根据用户名查询（带缓存）
     */
    public Admin selectByUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            return null;
        }
        
        // 先从缓存获取
        String cacheKey = RedisKeyConstants.ADMIN_BY_USERNAME_PREFIX + username;
        Admin admin = cacheService.get(cacheKey, Admin.class);
        if (admin != null) {
            return admin;
        }
        
        // 缓存未命中，从数据库查询
        admin = adminMapper.selectByUsername(username);
        if (admin != null) {
            // 写入缓存
            cacheService.set(cacheKey, admin, RedisKeyConstants.ExpireTime.USERNAME_QUERY);
        }
        return admin;
    }

    /**
     * 根据手机号查询
     */
    public Admin selectByPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return null;
        }
        return adminMapper.selectByPhone(phone);
    }

    /**
     * 手机号登录
     */
    public Account loginByPhone(String phone) {
        Admin dbAdmin = selectByPhone(phone);
        if (ObjectUtil.isNull(dbAdmin)) {
            throw new CustomException(ResultCodeEnum.USER_NOT_EXIST_ERROR);
        }
        
        // 生成token
        String tokenData = dbAdmin.getId() + "-" + RoleEnum.ADMIN.name();
        String token = TokenUtils.createToken(tokenData, dbAdmin.getPassword());
        dbAdmin.setToken(token);
        
        // 缓存管理员信息
        cacheService.set(RedisKeyConstants.ADMIN_INFO_PREFIX + dbAdmin.getId(), dbAdmin, RedisKeyConstants.ExpireTime.USER_INFO);
        
        return dbAdmin;
    }

    /**
     * 通过手机号修改密码
     */
    public void updatePasswordByPhone(Account account) {
        Admin dbAdmin = selectByPhone(account.getPhone());
        if (ObjectUtil.isNull(dbAdmin)) {
            throw new CustomException(ResultCodeEnum.USER_NOT_EXIST_ERROR);
        }
        
        // 验证用户名是否匹配
        if (!account.getUsername().equals(dbAdmin.getUsername())) {
            throw new CustomException("400", "用户名与手机号不匹配");
        }
        
        dbAdmin.setPassword(account.getNewPassword());
        adminMapper.updateById(dbAdmin);
        
        // 清除缓存，强制重新加载
        cacheService.delete(RedisKeyConstants.ADMIN_INFO_PREFIX + dbAdmin.getId());
        cacheService.delete(RedisKeyConstants.ADMIN_BY_USERNAME_PREFIX + dbAdmin.getUsername());
    }

}