<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.CategoryMapper">

    <sql id="Base_Column_List">
        id, name, description, icon, sort_order, status, create_time, update_time
    </sql>

    <select id="selectAll" resultType="com.example.entity.Category">
        select
        <include refid="Base_Column_List" />
        from sf_category
        <where>
            <if test="id != null"> and id = #{id}</if>
            <if test="name != null and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="status != null and status != ''"> and status = #{status}</if>
        </where>
        order by sort_order asc, id desc
    </select>

    <select id="selectById" resultType="com.example.entity.Category">
        select
        <include refid="Base_Column_List" />
        from sf_category
        where id = #{id}
    </select>

    <select id="selectEnabled" resultType="com.example.entity.Category">
        select
        <include refid="Base_Column_List" />
        from sf_category
        where status = '启用'
        order by sort_order asc, id desc
    </select>

    <select id="selectByStatus" resultType="com.example.entity.Category">
        select
        <include refid="Base_Column_List" />
        from sf_category
        where status = #{status}
        order by sort_order asc, id desc
    </select>

    <select id="countByName" resultType="int">
        select count(1) from sf_category where name = #{name}
    </select>

    <insert id="insert" parameterType="com.example.entity.Category" useGeneratedKeys="true" keyProperty="id">
        insert into sf_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="description != null">description,</if>
            <if test="icon != null">icon,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="icon != null">#{icon},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.example.entity.Category">
        update sf_category
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
        </set>
        where id = #{id}
    </update>

    <delete id="deleteById">
        delete from sf_category where id = #{id}
    </delete>

</mapper> 