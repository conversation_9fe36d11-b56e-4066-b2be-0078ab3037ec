{"ast": null, "code": "import TableSelect from '@/components/TableSelect.vue';\nexport default {\n  name: \"GoodsList\",\n  components: {\n    TableSelect\n  },\n  data() {\n    return {\n      tableData: [],\n      // 商品数据\n      pageNum: 1,\n      // 当前页码\n      pageSize: 12,\n      // 每页12条\n      total: 0,\n      // 总数\n\n      detailVisible: false,\n      // 详情弹窗显示\n      currentGoods: null,\n      // 当前查看的商品\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      // 当前登录用户\n\n      // 立即购买餐桌选择相关\n      buyTableDialogVisible: false,\n      selectedBuyTable: null,\n      buyOrderForm: {\n        goodsId: null,\n        goodsName: '',\n        goodsPrice: 0\n      },\n      buySubmitting: false // 防重复提交\n    };\n  },\n  created() {\n    this.load(1);\n  },\n  methods: {\n    load(pageNum) {\n      // 加载商品数据\n      if (pageNum) this.pageNum = pageNum;\n      this.$request.get('/foods/selectPage', {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize\n        }\n      }).then(res => {\n        if (res.code === '200') {\n          this.tableData = res.data?.list;\n          this.total = res.data?.total;\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    showDetail(item) {\n      // 显示商品详情\n      this.currentGoods = item;\n      this.detailVisible = true;\n    },\n    // 加入购物车\n    addToCart(goodsId) {\n      this.$request.post('/dingdan/add', {\n        sfUserName: this.user.name || '匿名用户',\n        sfUserId: this.user.id || 0,\n        sfProductIds: goodsId.toString(),\n        status: '未付款',\n        sfCartStatus: '已加入购物车',\n        sfTotalPrice: 0,\n        // 购物车状态下价格为0\n        sfCreateTime: new Date().toISOString()\n      }).then(res => {\n        if (res.code === '200') {\n          this.$message.success('商品已加入购物车！');\n        } else {\n          this.$message.error(res.msg || '操作失败');\n        }\n      }).catch(() => {\n        this.$message.error('操作失败，请重试');\n      });\n    },\n    // 显示立即购买对话框\n    showBuyDialog(goods) {\n      this.buyOrderForm = {\n        goodsId: goods.id,\n        goodsName: goods.name,\n        goodsPrice: goods.sfPrice\n      };\n      this.detailVisible = false;\n      this.selectedBuyTable = null;\n      this.buyTableDialogVisible = true;\n    },\n    // 处理立即购买的餐桌选择\n    handleBuyTableSelected(table) {\n      this.selectedBuyTable = table;\n    },\n    // 确认立即购买订单（选择餐桌后）\n    confirmBuyOrder() {\n      if (!this.selectedBuyTable) {\n        this.$message.warning('请选择餐桌');\n        return;\n      }\n\n      // 防重复提交\n      if (this.buySubmitting) {\n        this.$message.warning('正在处理中，请勿重复提交');\n        return;\n      }\n      this.$confirm(`确认要在${this.selectedBuyTable.tableNumber}号桌下单吗？总金额：¥${this.buyOrderForm.goodsPrice}`, '立即购买确认', {\n        confirmButtonText: '确定下单',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.buySubmitting = true;\n        this.$request.post('/dingdan/add', {\n          sfUserName: this.user.name || '匿名用户',\n          sfUserId: this.user.id || 0,\n          sfProductIds: this.buyOrderForm.goodsId.toString(),\n          status: '已支付',\n          // 立即购买直接设为已支付状态\n          sfCartStatus: '',\n          // 购物车状态为空\n          sfTotalPrice: this.buyOrderForm.goodsPrice,\n          // 商品价格放入订单价格字段\n          tableNumber: this.selectedBuyTable.tableNumber,\n          // 添加餐桌号\n          sfCreateTime: new Date().toISOString()\n        }).then(res => {\n          if (res.code === '200') {\n            this.$message.success(`订单创建成功！餐桌：${this.selectedBuyTable.tableNumber}号`);\n            this.buyTableDialogVisible = false;\n            this.selectedBuyTable = null;\n          } else {\n            this.$message.error(res.msg || '下单失败');\n          }\n        }).catch(err => {\n          console.error('下单失败:', err);\n          if (err.response && err.response.data && err.response.data.msg) {\n            this.$message.error(err.response.data.msg);\n          } else {\n            this.$message.error('下单失败，请重试');\n          }\n        }).finally(() => {\n          this.buySubmitting = false;\n        });\n      }).catch(() => {\n        this.$message.info('已取消下单');\n      });\n    },\n    handleCurrentChange(pageNum) {\n      // 分页变化\n      this.load(pageNum);\n    }\n  }\n};", "map": {"version": 3, "names": ["TableSelect", "name", "components", "data", "tableData", "pageNum", "pageSize", "total", "detailVisible", "currentGoods", "user", "JSON", "parse", "localStorage", "getItem", "buyTableDialogVisible", "selectedBuyTable", "buyOrderForm", "goodsId", "goodsName", "goodsPrice", "buySubmitting", "created", "load", "methods", "$request", "get", "params", "then", "res", "code", "list", "$message", "error", "msg", "showDetail", "item", "addToCart", "post", "sfUserName", "sfUserId", "id", "sfProductIds", "toString", "status", "sfCartStatus", "sfTotalPrice", "sfCreateTime", "Date", "toISOString", "success", "catch", "showBuyDialog", "goods", "sfPrice", "handleBuyTableSelected", "table", "confirmBuyOrder", "warning", "$confirm", "tableNumber", "confirmButtonText", "cancelButtonText", "type", "err", "console", "response", "finally", "info", "handleCurrentChange"], "sources": ["src/views/front/Foods.vue"], "sourcesContent": ["<template>\r\n    <div class=\"taobao-container\">\r\n        <!-- 商品展示区 -->\r\n        <div class=\"goods-container\">\r\n            <div\r\n                class=\"goods-item\"\r\n                v-for=\"item in tableData\"\r\n                :key=\"item.id\"\r\n                @click=\"showDetail(item)\"\r\n            >\r\n                <div class=\"goods-img-container\">\r\n                    <el-image\r\n                        :src=\"item.sfImage\"\r\n                        fit=\"cover\"\r\n                        class=\"goods-img\"\r\n                    ></el-image>\r\n                </div>\r\n                <div class=\"goods-info\">\r\n                    <div class=\"goods-title\">{{ item.name }}</div>\r\n                    <div class=\"goods-price\">¥{{ item.sfPrice }}</div>\r\n                    <div class=\"goods-stock\" :class=\"{'low-stock': item.sfStock <= 10, 'out-stock': item.sfStock <= 0}\">\r\n                        库存：{{ item.sfStock }}\r\n                    </div>\r\n\r\n                    <div class=\"goods-actions\">\r\n                        <el-button\r\n                            type=\"warning\"\r\n                            size=\"mini\"\r\n                            @click.stop=\"addToCart(item.id)\"\r\n                            class=\"cart-btn\"\r\n                            :disabled=\"item.sfStock <= 0\"\r\n                        >\r\n                            <i class=\"el-icon-goods\"></i> \r\n                            {{ item.sfStock <= 0 ? '缺货' : '加购物车' }}\r\n                        </el-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-container\">\r\n            <el-pagination\r\n                background\r\n                @current-change=\"handleCurrentChange\"\r\n                :current-page=\"pageNum\"\r\n                :page-size=\"pageSize\"\r\n                layout=\"prev, pager, next\"\r\n                :total=\"total\"\r\n                :pager-count=\"5\"\r\n                prev-text=\"上一页\"\r\n                next-text=\"下一页\"\r\n            >\r\n            </el-pagination>\r\n        </div>\r\n\r\n        <!-- 商品详情弹窗 -->\r\n        <el-dialog\r\n            :visible.sync=\"detailVisible\"\r\n            width=\"60%\"\r\n            top=\"5vh\"\r\n            custom-class=\"goods-detail-dialog\"\r\n        >\r\n            <div class=\"detail-container\" v-if=\"currentGoods\">\r\n                <div class=\"detail-left\">\r\n                    <el-image\r\n                        :src=\"currentGoods.sfImage\"\r\n                        fit=\"contain\"\r\n                        class=\"detail-img\"\r\n                    ></el-image>\r\n                </div>\r\n                <div class=\"detail-right\">\r\n                    <h2 class=\"detail-title\">{{ currentGoods.name }}</h2>\r\n                    <div class=\"detail-price\">\r\n                        <span class=\"price-symbol\">¥</span>\r\n                        <span class=\"price-number\">{{ currentGoods.sfPrice }}</span>\r\n                    </div>\r\n                    <div class=\"detail-info\">\r\n                        <div class=\"info-item\">\r\n                            <span class=\"info-label\">商品类型:</span>\r\n                            <span class=\"info-value\">{{ currentGoods.sfCategory }}</span>\r\n                        </div>\r\n                        <div class=\"info-item\">\r\n                            <span class=\"info-label\">库存状态:</span>\r\n                            <span class=\"info-value\" :class=\"{'low-stock': currentGoods.sfStock <= 10, 'out-stock': currentGoods.sfStock <= 0}\">\r\n                                {{ currentGoods.sfStock }}件\r\n                                <span v-if=\"currentGoods.sfStock <= 0\" class=\"stock-warning\">（缺货）</span>\r\n                                <span v-else-if=\"currentGoods.sfStock <= 10\" class=\"stock-warning\">（库存紧张）</span>\r\n                            </span>\r\n                        </div>\r\n                        <div class=\"info-item\">\r\n                            <span class=\"info-label\">上架状态:</span>\r\n                            <span class=\"info-value\">{{ currentGoods.sfShelfStatus }}</span>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"detail-desc\">\r\n                        <h3>商品描述</h3>\r\n                        <p>{{ currentGoods.sfDescription }}</p>\r\n                    </div>\r\n                    <div class=\"detail-actions\">\r\n                        <el-button\r\n                            type=\"warning\"\r\n                            class=\"cart-btn\"\r\n                            @click=\"addToCart(currentGoods.id)\"\r\n                        >\r\n                            <i class=\"el-icon-goods\"></i> 加入购物车\r\n                        </el-button>\r\n                        <el-button\r\n                            type=\"danger\"\r\n                            class=\"buy-btn\"\r\n                            @click=\"showBuyDialog(currentGoods)\"\r\n                        >\r\n                            立即购买\r\n                        </el-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!-- 餐桌选择对话框（立即购买用） -->\r\n        <el-dialog \r\n            title=\"选择餐桌\" \r\n            :visible.sync=\"buyTableDialogVisible\" \r\n            width=\"800px\"\r\n            :close-on-click-modal=\"false\">\r\n            <div class=\"table-selection-container\">\r\n                <div class=\"table-selection-header\">\r\n                    <div class=\"selection-info\">\r\n                        <p v-if=\"buyOrderForm.goodsName\">立即购买：{{ buyOrderForm.goodsName }}，总金额：<span class=\"total-price\">¥{{ buyOrderForm.goodsPrice }}</span></p>\r\n                        <p v-if=\"selectedBuyTable\">已选择：{{ selectedBuyTable.tableNumber }}号桌 ({{ selectedBuyTable.seats }}人座，{{ selectedBuyTable.area }})</p>\r\n                    </div>\r\n                </div>\r\n                \r\n                <!-- 餐桌选择组件 -->\r\n                <table-select \r\n                    v-model=\"selectedBuyTable\" \r\n                    @table-selected=\"handleBuyTableSelected\">\r\n                </table-select>\r\n            </div>\r\n            \r\n            <template slot=\"footer\">\r\n                <span class=\"dialog-footer\">\r\n                    <el-button @click=\"buyTableDialogVisible = false\">取消</el-button>\r\n                    <el-button \r\n                        type=\"primary\" \r\n                        @click=\"confirmBuyOrder\"\r\n                        :disabled=\"!selectedBuyTable\">\r\n                        确认下单\r\n                    </el-button>\r\n                </span>\r\n            </template>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport TableSelect from '@/components/TableSelect.vue'\r\n\r\nexport default {\r\n    name: \"GoodsList\",\r\n    components: {\r\n        TableSelect\r\n    },\r\n    data() {\r\n        return {\r\n            tableData: [],  // 商品数据\r\n            pageNum: 1,     // 当前页码\r\n            pageSize: 12,   // 每页12条\r\n            total: 0,       // 总数\r\n\r\n            detailVisible: false, // 详情弹窗显示\r\n            currentGoods: null,   // 当前查看的商品\r\n            user: JSON.parse(localStorage.getItem('xm-user') || '{}'), // 当前登录用户\r\n\r\n            // 立即购买餐桌选择相关\r\n            buyTableDialogVisible: false,\r\n            selectedBuyTable: null,\r\n            buyOrderForm: {\r\n                goodsId: null,\r\n                goodsName: '',\r\n                goodsPrice: 0\r\n            },\r\n            buySubmitting: false // 防重复提交\r\n        }\r\n    },\r\n    created() {\r\n        this.load(1)\r\n    },\r\n    methods: {\r\n        load(pageNum) {  // 加载商品数据\r\n            if (pageNum) this.pageNum = pageNum\r\n            this.$request.get('/foods/selectPage', {\r\n                params: {\r\n                    pageNum: this.pageNum,\r\n                    pageSize: this.pageSize,\r\n\r\n                }\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.tableData = res.data?.list\r\n                    this.total = res.data?.total\r\n                } else {\r\n                    this.$message.error(res.msg)\r\n                }\r\n            })\r\n        },\r\n        showDetail(item) {  // 显示商品详情\r\n            this.currentGoods = item\r\n            this.detailVisible = true\r\n        },\r\n        // 加入购物车\r\n        addToCart(goodsId) {\r\n            this.$request.post('/dingdan/add', {\r\n                sfUserName: this.user.name || '匿名用户',\r\n                sfUserId: this.user.id || 0,\r\n                sfProductIds: goodsId.toString(),\r\n                status: '未付款',\r\n                sfCartStatus: '已加入购物车',\r\n                sfTotalPrice: 0, // 购物车状态下价格为0\r\n                sfCreateTime: new Date().toISOString()\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.$message.success('商品已加入购物车！')\r\n                } else {\r\n                    this.$message.error(res.msg || '操作失败')\r\n                }\r\n            }).catch(() => {\r\n                this.$message.error('操作失败，请重试')\r\n            })\r\n        },\r\n        // 显示立即购买对话框\r\n        showBuyDialog(goods) {\r\n            this.buyOrderForm = {\r\n                goodsId: goods.id,\r\n                goodsName: goods.name,\r\n                goodsPrice: goods.sfPrice\r\n            }\r\n            this.detailVisible = false\r\n            this.selectedBuyTable = null\r\n            this.buyTableDialogVisible = true\r\n        },\r\n\r\n        // 处理立即购买的餐桌选择\r\n        handleBuyTableSelected(table) {\r\n            this.selectedBuyTable = table\r\n        },\r\n\r\n        // 确认立即购买订单（选择餐桌后）\r\n        confirmBuyOrder() {\r\n            if (!this.selectedBuyTable) {\r\n                this.$message.warning('请选择餐桌')\r\n                return\r\n            }\r\n\r\n            // 防重复提交\r\n            if (this.buySubmitting) {\r\n                this.$message.warning('正在处理中，请勿重复提交')\r\n                return\r\n            }\r\n\r\n            this.$confirm(`确认要在${this.selectedBuyTable.tableNumber}号桌下单吗？总金额：¥${this.buyOrderForm.goodsPrice}`, '立即购买确认', {\r\n                confirmButtonText: '确定下单',\r\n                cancelButtonText: '取消',\r\n                type: 'warning'\r\n            }).then(() => {\r\n                this.buySubmitting = true\r\n                this.$request.post('/dingdan/add', {\r\n                    sfUserName: this.user.name || '匿名用户',\r\n                    sfUserId: this.user.id || 0,\r\n                    sfProductIds: this.buyOrderForm.goodsId.toString(),\r\n                    status: '已支付', // 立即购买直接设为已支付状态\r\n                    sfCartStatus: '', // 购物车状态为空\r\n                    sfTotalPrice: this.buyOrderForm.goodsPrice, // 商品价格放入订单价格字段\r\n                    tableNumber: this.selectedBuyTable.tableNumber, // 添加餐桌号\r\n                    sfCreateTime: new Date().toISOString()\r\n                }).then(res => {\r\n                    if (res.code === '200') {\r\n                        this.$message.success(`订单创建成功！餐桌：${this.selectedBuyTable.tableNumber}号`)\r\n                        this.buyTableDialogVisible = false\r\n                        this.selectedBuyTable = null\r\n                    } else {\r\n                        this.$message.error(res.msg || '下单失败')\r\n                    }\r\n                }).catch(err => {\r\n                    console.error('下单失败:', err)\r\n                    if (err.response && err.response.data && err.response.data.msg) {\r\n                        this.$message.error(err.response.data.msg)\r\n                    } else {\r\n                        this.$message.error('下单失败，请重试')\r\n                    }\r\n                }).finally(() => {\r\n                    this.buySubmitting = false\r\n                })\r\n            }).catch(() => {\r\n                this.$message.info('已取消下单')\r\n            })\r\n        },\r\n        handleCurrentChange(pageNum) {  // 分页变化\r\n            this.load(pageNum)\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 样式部分保持不变 */\r\n.taobao-container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 20px;\r\n}\r\n\r\n\r\n\r\n.goods-container {\r\n    display: grid;\r\n    grid-template-columns: repeat(4, 1fr);\r\n    gap: 20px;\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.goods-item {\r\n    background: white;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    transition: transform 0.3s, box-shadow 0.3s;\r\n    cursor: pointer;\r\n}\r\n\r\n.goods-item:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.goods-img-container {\r\n    height: 200px;\r\n    overflow: hidden;\r\n}\r\n\r\n.goods-img {\r\n    width: 100%;\r\n    height: 100%;\r\n}\r\n\r\n.goods-info {\r\n    padding: 15px;\r\n}\r\n\r\n.goods-title {\r\n    font-size: 14px;\r\n    color: #333;\r\n    margin-bottom: 10px;\r\n    height: 40px;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n}\r\n\r\n.goods-price {\r\n    color: #FF0036;\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n    margin-bottom: 5px;\r\n}\r\n\r\n.goods-sales {\r\n    font-size: 12px;\r\n    color: #999;\r\n}\r\n\r\n.goods-actions {\r\n    margin-top: 10px;\r\n}\r\n\r\n.pagination-container {\r\n    display: flex;\r\n    justify-content: center;\r\n    margin-top: 30px;\r\n}\r\n\r\n.detail-container {\r\n    display: flex;\r\n}\r\n\r\n.detail-left {\r\n    flex: 1;\r\n    padding: 20px;\r\n}\r\n\r\n.detail-right {\r\n    flex: 1;\r\n    padding: 20px;\r\n}\r\n\r\n.detail-img {\r\n    width: 100%;\r\n    height: 400px;\r\n}\r\n\r\n.detail-title {\r\n    font-size: 24px;\r\n    color: #333;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.detail-price {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.price-symbol {\r\n    color: #FF0036;\r\n    font-size: 20px;\r\n}\r\n\r\n.price-number {\r\n    color: #FF0036;\r\n    font-size: 28px;\r\n    font-weight: bold;\r\n}\r\n\r\n.detail-info {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.info-item {\r\n    margin-bottom: 10px;\r\n    font-size: 14px;\r\n}\r\n\r\n.info-label {\r\n    color: #999;\r\n    margin-right: 10px;\r\n}\r\n\r\n.info-value {\r\n    color: #333;\r\n}\r\n\r\n.detail-desc {\r\n    margin-top: 30px;\r\n    border-top: 1px solid #eee;\r\n    padding-top: 20px;\r\n}\r\n\r\n.detail-desc h3 {\r\n    font-size: 16px;\r\n    color: #333;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.detail-desc p {\r\n    font-size: 14px;\r\n    color: #666;\r\n    line-height: 1.6;\r\n}\r\n\r\n.detail-actions {\r\n    margin-top: 30px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n}\r\n\r\n.cart-btn {\r\n    background-color: #FF9500;\r\n    border-color: #FF9500;\r\n    color: white;\r\n    width: 48%;\r\n    height: 50px;\r\n    font-size: 18px;\r\n}\r\n\r\n.cart-btn:hover {\r\n    background-color: #FFAA33;\r\n    border-color: #FFAA33;\r\n}\r\n\r\n.buy-btn {\r\n    width: 48%;\r\n    height: 50px;\r\n    font-size: 18px;\r\n    background: linear-gradient(to right, #FF0036, #FF0036);\r\n    border: none;\r\n}\r\n\r\n/deep/ .goods-detail-dialog {\r\n    border-radius: 8px;\r\n}\r\n\r\n/deep/ .goods-detail-dialog .el-dialog__header {\r\n    border-bottom: 1px solid #eee;\r\n}\r\n\r\n/deep/ .goods-detail-dialog .el-dialog__body {\r\n    padding: 0;\r\n}\r\n</style>"], "mappings": "AA4JA,OAAAA,WAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;EACAG,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MAAA;;MAEAC,aAAA;MAAA;MACAC,YAAA;MAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MAAA;;MAEA;MACAC,qBAAA;MACAC,gBAAA;MACAC,YAAA;QACAC,OAAA;QACAC,SAAA;QACAC,UAAA;MACA;MACAC,aAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAD,KAAAlB,OAAA;MAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAAoB,QAAA,CAAAC,GAAA;QACAC,MAAA;UACAtB,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA;QAEA;MACA,GAAAsB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAA1B,SAAA,GAAAyB,GAAA,CAAA1B,IAAA,EAAA4B,IAAA;UACA,KAAAxB,KAAA,GAAAsB,GAAA,CAAA1B,IAAA,EAAAI,KAAA;QACA;UACA,KAAAyB,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA;IACA;IACAC,WAAAC,IAAA;MAAA;MACA,KAAA3B,YAAA,GAAA2B,IAAA;MACA,KAAA5B,aAAA;IACA;IACA;IACA6B,UAAAnB,OAAA;MACA,KAAAO,QAAA,CAAAa,IAAA;QACAC,UAAA,OAAA7B,IAAA,CAAAT,IAAA;QACAuC,QAAA,OAAA9B,IAAA,CAAA+B,EAAA;QACAC,YAAA,EAAAxB,OAAA,CAAAyB,QAAA;QACAC,MAAA;QACAC,YAAA;QACAC,YAAA;QAAA;QACAC,YAAA,MAAAC,IAAA,GAAAC,WAAA;MACA,GAAArB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAE,QAAA,CAAAkB,OAAA;QACA;UACA,KAAAlB,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA,GAAAiB,KAAA;QACA,KAAAnB,QAAA,CAAAC,KAAA;MACA;IACA;IACA;IACAmB,cAAAC,KAAA;MACA,KAAApC,YAAA;QACAC,OAAA,EAAAmC,KAAA,CAAAZ,EAAA;QACAtB,SAAA,EAAAkC,KAAA,CAAApD,IAAA;QACAmB,UAAA,EAAAiC,KAAA,CAAAC;MACA;MACA,KAAA9C,aAAA;MACA,KAAAQ,gBAAA;MACA,KAAAD,qBAAA;IACA;IAEA;IACAwC,uBAAAC,KAAA;MACA,KAAAxC,gBAAA,GAAAwC,KAAA;IACA;IAEA;IACAC,gBAAA;MACA,UAAAzC,gBAAA;QACA,KAAAgB,QAAA,CAAA0B,OAAA;QACA;MACA;;MAEA;MACA,SAAArC,aAAA;QACA,KAAAW,QAAA,CAAA0B,OAAA;QACA;MACA;MAEA,KAAAC,QAAA,aAAA3C,gBAAA,CAAA4C,WAAA,mBAAA3C,YAAA,CAAAG,UAAA;QACAyC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAnC,IAAA;QACA,KAAAP,aAAA;QACA,KAAAI,QAAA,CAAAa,IAAA;UACAC,UAAA,OAAA7B,IAAA,CAAAT,IAAA;UACAuC,QAAA,OAAA9B,IAAA,CAAA+B,EAAA;UACAC,YAAA,OAAAzB,YAAA,CAAAC,OAAA,CAAAyB,QAAA;UACAC,MAAA;UAAA;UACAC,YAAA;UAAA;UACAC,YAAA,OAAA7B,YAAA,CAAAG,UAAA;UAAA;UACAwC,WAAA,OAAA5C,gBAAA,CAAA4C,WAAA;UAAA;UACAb,YAAA,MAAAC,IAAA,GAAAC,WAAA;QACA,GAAArB,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACA,KAAAE,QAAA,CAAAkB,OAAA,mBAAAlC,gBAAA,CAAA4C,WAAA;YACA,KAAA7C,qBAAA;YACA,KAAAC,gBAAA;UACA;YACA,KAAAgB,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA,GAAAiB,KAAA,CAAAa,GAAA;UACAC,OAAA,CAAAhC,KAAA,UAAA+B,GAAA;UACA,IAAAA,GAAA,CAAAE,QAAA,IAAAF,GAAA,CAAAE,QAAA,CAAA/D,IAAA,IAAA6D,GAAA,CAAAE,QAAA,CAAA/D,IAAA,CAAA+B,GAAA;YACA,KAAAF,QAAA,CAAAC,KAAA,CAAA+B,GAAA,CAAAE,QAAA,CAAA/D,IAAA,CAAA+B,GAAA;UACA;YACA,KAAAF,QAAA,CAAAC,KAAA;UACA;QACA,GAAAkC,OAAA;UACA,KAAA9C,aAAA;QACA;MACA,GAAA8B,KAAA;QACA,KAAAnB,QAAA,CAAAoC,IAAA;MACA;IACA;IACAC,oBAAAhE,OAAA;MAAA;MACA,KAAAkB,IAAA,CAAAlB,OAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}