<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.CartMapper">

    <resultMap id="BaseResultMap" type="com.example.entity.Cart">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="food_id" property="foodId" />
        <result column="quantity" property="quantity" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <resultMap id="CartVOResultMap" type="com.example.entity.CartVO">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="food_id" property="foodId" />
        <result column="food_name" property="foodName" />
        <result column="food_price" property="foodPrice" />
        <result column="food_image" property="foodImage" />
        <result column="food_description" property="foodDescription" />
        <result column="quantity" property="quantity" />
        <result column="subtotal" property="subtotal" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 根据用户ID查询购物车列表 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT * FROM sf_cart 
        WHERE user_id = #{userId} 
        ORDER BY create_time DESC
    </select>

    <!-- 根据用户ID查询购物车列表（包含商品详情） -->
    <select id="selectCartVOByUserId" resultMap="CartVOResultMap">
        SELECT 
            c.id,
            c.user_id,
            c.food_id,
            f.name AS food_name,
            CAST(f.sf_price AS DECIMAL(10,2)) AS food_price,
            f.sf_image AS food_image,
            f.sf_description AS food_description,
            c.quantity,
            CAST(f.sf_price AS DECIMAL(10,2)) * c.quantity AS subtotal,
            c.create_time,
            c.update_time
        FROM sf_cart c
        INNER JOIN sf_food f ON c.food_id = f.id
        WHERE c.user_id = #{userId}
        ORDER BY c.create_time DESC
    </select>

    <!-- 根据用户ID和商品ID查询购物车项 -->
    <select id="selectByUserIdAndFoodId" resultMap="BaseResultMap">
        SELECT * FROM sf_cart 
        WHERE user_id = #{userId} AND food_id = #{foodId}
    </select>

    <!-- 插入购物车项 -->
    <insert id="insert" parameterType="com.example.entity.Cart">
        INSERT INTO sf_cart (user_id, food_id, quantity, create_time, update_time)
        VALUES (#{userId}, #{foodId}, #{quantity}, NOW(), NOW())
    </insert>

    <!-- 更新购物车项数量 -->
    <update id="updateQuantity">
        UPDATE sf_cart 
        SET quantity = #{quantity}, update_time = NOW() 
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除购物车项 -->
    <delete id="deleteById">
        DELETE FROM sf_cart WHERE id = #{id}
    </delete>

    <!-- 根据用户ID删除所有购物车项（清空购物车） -->
    <delete id="deleteByUserId">
        DELETE FROM sf_cart WHERE user_id = #{userId}
    </delete>

    <!-- 根据用户ID和商品ID删除购物车项 -->
    <delete id="deleteByUserIdAndFoodId">
        DELETE FROM sf_cart WHERE user_id = #{userId} AND food_id = #{foodId}
    </delete>

    <!-- 统计用户购物车商品数量 -->
    <select id="countByUserId" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(quantity), 0) FROM sf_cart WHERE user_id = #{userId}
    </select>

</mapper> 