package com.example.entity;

import java.math.BigDecimal;

/**
 * 购物车统计信息
 */
public class CartStatistics {
    /** 商品总数量 */
    private Integer totalQuantity;
    /** 商品种类数 */
    private Integer totalItems;
    /** 总金额 */
    private BigDecimal totalAmount;

    public CartStatistics() {
        this.totalQuantity = 0;
        this.totalItems = 0;
        this.totalAmount = BigDecimal.ZERO;
    }

    public CartStatistics(Integer totalQuantity, Integer totalItems, BigDecimal totalAmount) {
        this.totalQuantity = totalQuantity;
        this.totalItems = totalItems;
        this.totalAmount = totalAmount;
    }

    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public Integer getTotalItems() {
        return totalItems;
    }

    public void setTotalItems(Integer totalItems) {
        this.totalItems = totalItems;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    @Override
    public String toString() {
        return "CartStatistics{" +
                "totalQuantity=" + totalQuantity +
                ", totalItems=" + totalItems +
                ", totalAmount=" + totalAmount +
                '}';
    }
} 