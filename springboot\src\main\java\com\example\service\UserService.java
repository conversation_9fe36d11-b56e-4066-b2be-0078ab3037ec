package com.example.service;

import cn.hutool.core.util.ObjectUtil;
import com.example.common.Constants;
import com.example.common.constants.RedisKeyConstants;
import com.example.common.enums.ResultCodeEnum;
import com.example.common.enums.RoleEnum;
import com.example.common.enums.StatusEnum;
import com.example.common.service.CacheService;
import com.example.entity.Account;
import com.example.entity.User;
import com.example.exception.CustomException;
import com.example.mapper.UserMapper;
import com.example.utils.TokenUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户业务处理
 **/
@Service
public class UserService {

    @Resource
    private UserMapper userMapper;

    @Resource
    private CacheService cacheService;

    /**
     * 新增
     */
    public void add(User user) {
        User dbUser = userMapper.selectByUsername(user.getUsername());
        if (ObjectUtil.isNotNull(dbUser)) {
            throw new CustomException(ResultCodeEnum.USER_EXIST_ERROR);
        }
        if (ObjectUtil.isEmpty(user.getPassword())) {
            user.setPassword(Constants.USER_DEFAULT_PASSWORD);
        }
        if (ObjectUtil.isEmpty(user.getName())) {
            user.setName(user.getUsername());
        }
        user.setRole(RoleEnum.USER.name());
        userMapper.insert(user);
    }

    /**
     * 删除
     */
    public void deleteById(Integer id) {
        userMapper.deleteById(id);
        // 删除缓存
        cacheService.delete(RedisKeyConstants.USER_INFO_PREFIX + id);
    }

    /**
     * 批量删除
     */
    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            deleteById(id);
        }
    }

    /**
     * 修改
     */
    public void updateById(User user) {
        userMapper.updateById(user);
        // 更新缓存
        if (user.getId() != null) {
            cacheService.set(RedisKeyConstants.USER_INFO_PREFIX + user.getId(), user, RedisKeyConstants.ExpireTime.USER_INFO);
            // 如果用户名发生变化，删除用户名缓存
            if (user.getUsername() != null) {
                cacheService.delete(RedisKeyConstants.USER_BY_USERNAME_PREFIX + user.getUsername());
            }
        }
    }

    /**
     * 根据ID查询（带缓存）
     */
    public User selectById(Integer id) {
        if (id == null) {
            return null;
        }
        
        // 先从缓存获取
        String cacheKey = RedisKeyConstants.USER_INFO_PREFIX + id;
        User user = cacheService.get(cacheKey, User.class);
        if (user != null) {
            return user;
        }
        
        // 缓存未命中，从数据库查询
        user = userMapper.selectById(id);
        if (user != null) {
            // 写入缓存
            cacheService.set(cacheKey, user, RedisKeyConstants.ExpireTime.USER_INFO);
        }
        return user;
    }

    /**
     * 查询所有
     */
    public List<User> selectAll(User user) {
        return userMapper.selectAll(user);
    }

    /**
     * 分页查询
     */
    public PageInfo<User> selectPage(User user, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<User> list = userMapper.selectAll(user);
        return PageInfo.of(list);
    }

    /**
     * 登录
     */
    public Account login(Account account) {
        Account dbUser = userMapper.selectByUsername(account.getUsername());
        if (ObjectUtil.isNull(dbUser)) {
            throw new CustomException(ResultCodeEnum.USER_NOT_EXIST_ERROR);
        }
        if (!account.getPassword().equals(dbUser.getPassword())) {
            throw new CustomException(ResultCodeEnum.USER_ACCOUNT_ERROR);
        }
        // 生成token
        String tokenData = dbUser.getId() + "-" + RoleEnum.USER.name();
        String token = TokenUtils.createToken(tokenData, dbUser.getPassword());
        dbUser.setToken(token);
        
        // 缓存用户信息
        cacheService.set(RedisKeyConstants.USER_INFO_PREFIX + dbUser.getId(), dbUser, RedisKeyConstants.ExpireTime.USER_INFO);
        
        return dbUser;
    }

    /**
     * 注册
     */
    public void register(Account account) {
        User user = new User();
        BeanUtils.copyProperties(account, user);
        add(user);
    }

    /**
     * 修改密码
     */
    public void updatePassword(Account account) {
        User dbUser = userMapper.selectByUsername(account.getUsername());
        if (ObjectUtil.isNull(dbUser)) {
            throw new CustomException(ResultCodeEnum.USER_NOT_EXIST_ERROR);
        }
        if (!account.getPassword().equals(dbUser.getPassword())) {
            throw new CustomException(ResultCodeEnum.PARAM_PASSWORD_ERROR);
        }
        dbUser.setPassword(account.getNewPassword());
        userMapper.updateById(dbUser);
        
        // 清除缓存，强制重新加载
        cacheService.delete(RedisKeyConstants.USER_INFO_PREFIX + dbUser.getId());
        cacheService.delete(RedisKeyConstants.USER_BY_USERNAME_PREFIX + dbUser.getUsername());
    }

    /**
     * 根据用户名查询（带缓存）
     */
    public User selectByUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            return null;
        }
        
        // 先从缓存获取
        String cacheKey = RedisKeyConstants.USER_BY_USERNAME_PREFIX + username;
        User user = cacheService.get(cacheKey, User.class);
        if (user != null) {
            return user;
        }
        
        // 缓存未命中，从数据库查询
        user = userMapper.selectByUsername(username);
        if (user != null) {
            // 写入缓存
            cacheService.set(cacheKey, user, RedisKeyConstants.ExpireTime.USERNAME_QUERY);
        }
        return user;
    }

    /**
     * 根据手机号查询
     */
    public User selectByPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return null;
        }
        return userMapper.selectByPhone(phone);
    }

    /**
     * 手机号登录
     */
    public Account loginByPhone(String phone) {
        User dbUser = selectByPhone(phone);
        if (ObjectUtil.isNull(dbUser)) {
            throw new CustomException(ResultCodeEnum.USER_NOT_EXIST_ERROR);
        }
        
        // 生成token
        String tokenData = dbUser.getId() + "-" + RoleEnum.USER.name();
        String token = TokenUtils.createToken(tokenData, dbUser.getPassword());
        dbUser.setToken(token);
        
        // 缓存用户信息
        cacheService.set(RedisKeyConstants.USER_INFO_PREFIX + dbUser.getId(), dbUser, RedisKeyConstants.ExpireTime.USER_INFO);
        
        return dbUser;
    }

    /**
     * 通过手机号修改密码
     */
    public void updatePasswordByPhone(Account account) {
        User dbUser = selectByPhone(account.getPhone());
        if (ObjectUtil.isNull(dbUser)) {
            throw new CustomException(ResultCodeEnum.USER_NOT_EXIST_ERROR);
        }
        
        // 验证用户名是否匹配
        if (!account.getUsername().equals(dbUser.getUsername())) {
            throw new CustomException("400", "用户名与手机号不匹配");
        }
        
        dbUser.setPassword(account.getNewPassword());
        userMapper.updateById(dbUser);
        
        // 清除缓存，强制重新加载
        cacheService.delete(RedisKeyConstants.USER_INFO_PREFIX + dbUser.getId());
        cacheService.delete(RedisKeyConstants.USER_BY_USERNAME_PREFIX + dbUser.getUsername());
    }

}