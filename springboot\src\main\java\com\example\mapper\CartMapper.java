package com.example.mapper;

import com.example.entity.Cart;
import com.example.entity.CartVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 购物车数据访问接口
 */
public interface CartMapper {

    /**
     * 根据用户ID查询购物车列表
     */
    List<Cart> selectByUserId(@Param("userId") Integer userId);

    /**
     * 根据用户ID查询购物车列表（包含商品详情）
     */
    List<CartVO> selectCartVOByUserId(@Param("userId") Integer userId);

    /**
     * 根据用户ID和商品ID查询购物车项
     */
    Cart selectByUserIdAndFoodId(@Param("userId") Integer userId, @Param("foodId") Integer foodId);

    /**
     * 插入购物车项
     */
    void insert(Cart cart);

    /**
     * 更新购物车项数量
     */
    void updateQuantity(@Param("id") Integer id, @Param("quantity") Integer quantity);

    /**
     * 根据ID删除购物车项
     */
    void deleteById(@Param("id") Integer id);

    /**
     * 根据用户ID删除所有购物车项（清空购物车）
     */
    void deleteByUserId(@Param("userId") Integer userId);

    /**
     * 根据用户ID和商品ID删除购物车项
     */
    void deleteByUserIdAndFoodId(@Param("userId") Integer userId, @Param("foodId") Integer foodId);

    /**
     * 统计用户购物车商品数量
     */
    Integer countByUserId(@Param("userId") Integer userId);
} 