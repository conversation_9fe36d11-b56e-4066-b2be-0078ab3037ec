package com.example.controller;

import com.example.common.Result;
import com.example.entity.Category;
import com.example.service.CategoryService;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

/**
 * 分类管理前端操作接口
 */
@RestController
@RequestMapping("/category")
public class CategoryController {

    @Resource
    private CategoryService categoryService;

    /**
     * 新增分类
     */
    @PostMapping("/add")
    public Result add(@RequestBody Category category) {
        try {
            categoryService.add(category);
            return Result.success();
        } catch (Exception e) {
            return Result.error("500", e.getMessage());
        }
    }

    /**
     * 删除分类
     */
    @DeleteMapping("/delete/{id}")
    public Result deleteById(@PathVariable Integer id) {
        try {
            categoryService.deleteById(id);
            return Result.success();
        } catch (Exception e) {
            return Result.error("500", e.getMessage());
        }
    }

    /**
     * 批量删除
     */
    @DeleteMapping("/delete/batch")
    public Result deleteBatch(@RequestBody List<Integer> ids) {
        try {
            categoryService.deleteBatch(ids);
            return Result.success();
        } catch (Exception e) {
            return Result.error("500", e.getMessage());
        }
    }

    /**
     * 修改分类
     */
    @PutMapping("/update")
    public Result updateById(@RequestBody Category category) {
        try {
            categoryService.updateById(category);
            return Result.success();
        } catch (Exception e) {
            return Result.error("500", e.getMessage());
        }
    }

    /**
     * 根据ID查询
     */
    @GetMapping("/selectById/{id}")
    public Result selectById(@PathVariable Integer id) {
        try {
            if (id == null || id <= 0) {
                return Result.error("400", "分类ID不能为空或无效");
            }
        Category category = categoryService.selectById(id);
        return Result.success(category);
        } catch (Exception e) {
            return Result.error("500", e.getMessage());
        }
    }

    /**
     * 查询所有分类
     */
    @GetMapping("/selectAll")
    public Result selectAll(Category category) {
        try {
        List<Category> list = categoryService.selectAll(category);
        return Result.success(list);
        } catch (Exception e) {
            return Result.error("500", e.getMessage());
        }
    }

    /**
     * 查询启用的分类
     */
    @GetMapping("/selectEnabled")
    public Result selectEnabled() {
        try {
        List<Category> list = categoryService.selectEnabled();
        return Result.success(list);
        } catch (Exception e) {
            return Result.error("500", e.getMessage());
        }
    }

    /**
     * 分页查询
     */
    @GetMapping("/selectPage")
    public Result selectPage(Category category,
                             @RequestParam(defaultValue = "1") Integer pageNum,
                             @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            if (pageNum <= 0 || pageSize <= 0) {
                return Result.error("400", "分页参数必须大于0");
            }
        PageInfo<Category> page = categoryService.selectPage(category, pageNum, pageSize);
        return Result.success(page);
        } catch (Exception e) {
            return Result.error("500", e.getMessage());
        }
    }
} 