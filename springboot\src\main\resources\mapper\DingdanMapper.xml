<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.DingdanMapper">

    <sql id="Base_Column_List">
        id,sf_user_name,sf_user_id,table_id,table_number,status,sf_order_number,sf_create_time,sf_remark,sf_evaluation,sf_total_price
    </sql>
    <!-- 统计订单状态分布 -->
    <select id="countByStatus" resultType="map">
        SELECT status as name, COUNT(*) as value
        FROM sf_order
        GROUP BY status
    </select>

    <!-- 统计每月订单量 -->
    <select id="countByMonth" resultType="map">
        SELECT
            MONTH(sf_create_time) as month,
            COUNT(*) as count
        FROM sf_order
        WHERE YEAR(sf_create_time) = #{year}
        GROUP BY MONTH(sf_create_time)
        ORDER BY month
    </select>

    <!-- 统计每月收入 -->
    <select id="revenueByMonth" resultType="map">
        SELECT
            MONTH(sf_create_time) as month,
            COALESCE(SUM(sf_total_price), 0) as revenue
        FROM sf_order
        WHERE YEAR(sf_create_time) = #{year}
        AND status IN ('已支付', '制作中', '待取餐', '已完成')
        GROUP BY MONTH(sf_create_time)
        ORDER BY month
    </select>

    <!-- 统计菜品销量排行 -->
    <select id="getPopularFoods" resultType="map">
        SELECT 
            oi.food_name as name,
            SUM(oi.quantity) as sales,
            COALESCE(SUM(oi.subtotal), 0) as revenue,
            COUNT(DISTINCT o.id) as orderCount,
            COALESCE(AVG(fr.rating), 0) as avgRating
        FROM sf_order_item oi
        INNER JOIN sf_order o ON oi.order_id = o.id
        LEFT JOIN sf_food_review fr ON oi.food_id = fr.food_id AND fr.status = '正常'
        WHERE o.status IN ('已支付', '制作中', '待取餐', '已完成')
        GROUP BY oi.food_id, oi.food_name
        ORDER BY sales DESC
        LIMIT #{limit}
    </select>

    <!-- 统计用户注册趋势 -->
    <select id="getUserRegistrationByMonth" resultType="map">
        SELECT 
            m.month,
            COALESCE(u.count, 0) as count
        FROM (
            SELECT 1 as month UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL SELECT 4 
            UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL SELECT 8 
            UNION ALL SELECT 9 UNION ALL SELECT 10 UNION ALL SELECT 11 UNION ALL SELECT 12
        ) m
        LEFT JOIN (
            SELECT 
                MONTH(NOW()) as month,
                COUNT(*) as count
            FROM sf_user 
            WHERE YEAR(NOW()) = #{year}
            GROUP BY MONTH(NOW())
        ) u ON m.month = u.month
        ORDER BY m.month
    </select>



    <!-- 统计分类销售占比 -->
    <select id="getCategoryRevenueStats" resultType="map">
        SELECT 
            c.name,
            COALESCE(SUM(oi.subtotal), 0) as value,
            COUNT(DISTINCT oi.order_id) as orderCount
        FROM sf_category c
        LEFT JOIN sf_food f ON c.id = f.category_id
        LEFT JOIN sf_order_item oi ON f.id = oi.food_id
        LEFT JOIN sf_order o ON oi.order_id = o.id AND o.status IN ('已支付', '制作中', '待取餐', '已完成')
        GROUP BY c.id, c.name
        ORDER BY value DESC
    </select>



    <!-- 统计用户活跃度 -->
    <select id="getUserActivityStats" resultType="map">
        SELECT 
            DATE(sf_create_time) as date,
            COUNT(DISTINCT sf_user_id) as activeUsers,
            COUNT(*) as orderCount
        FROM sf_order
        WHERE sf_create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        GROUP BY DATE(sf_create_time)
        ORDER BY date DESC
    </select>

    <!-- 获取综合统计数据 -->
    <select id="getOverviewStats" resultType="map">
        SELECT 
            (SELECT COUNT(*) FROM sf_user) as totalUsers,
            (SELECT COUNT(*) FROM sf_order) as totalOrders,
            (SELECT COUNT(*) FROM sf_order WHERE status IN ('已支付', '制作中', '待取餐', '已完成')) as completedOrders,
            (SELECT COALESCE(SUM(sf_total_price), 0) FROM sf_order WHERE status IN ('已支付', '制作中', '待取餐', '已完成')) as totalRevenue,
            (SELECT COUNT(*) FROM sf_food WHERE sf_shelf_status = '上架') as availableFoods,
            (SELECT COUNT(*) FROM sf_table) as totalTables,
            (SELECT COUNT(*) FROM sf_complaint WHERE status = '待处理') as pendingComplaints,
            (SELECT COALESCE(AVG(rating), 0) FROM sf_food_review WHERE status = '正常') as avgRating
    </select>

    <select id="selectAll" resultType="com.example.entity.Dingdan">
        select
        <include refid="Base_Column_List" />
        from sf_order
        <where>
            <if test="id != null"> and id = #{id}</if>
            <if test="sfUserName != null"> and sf_user_name like concat('%', #{sfUserName}, '%')</if>
            <if test="sfUserId != null"> and sf_user_id = #{sfUserId}</if>
            <if test="tableId != null"> and table_id = #{tableId}</if>
            <if test="tableNumber != null"> and table_number = #{tableNumber}</if>
            <if test="status != null"> and status = #{status}</if>
            <if test="sfOrderNumber != null"> and sf_order_number = #{sfOrderNumber}</if>
            <if test="sfCreateTime != null"> and sf_create_time = #{sfCreateTime}</if>
            <if test="sfRemark != null"> and sf_remark = #{sfRemark}</if>
            <if test="sfEvaluation != null"> and sf_evaluation = #{sfEvaluation}</if>
            <if test="sfTotalPrice != null"> and sf_total_price = #{sfTotalPrice}</if>
        </where>
        order by id desc
    </select>

    <select id="selectById" resultType="com.example.entity.Dingdan">
        select
        <include refid="Base_Column_List" />
        from sf_order
        where id = #{id}
    </select>

    <delete id="deleteById">
        delete from sf_order
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.example.entity.Dingdan" useGeneratedKeys="true" keyProperty="id">
        insert into sf_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="sfUserName != null">sf_user_name,</if>
            <if test="sfUserId != null">sf_user_id,</if>
            <if test="tableId != null">table_id,</if>
            <if test="tableNumber != null">table_number,</if>
            <if test="status != null">status,</if>
            <if test="sfOrderNumber != null">sf_order_number,</if>
            <if test="sfCreateTime != null">sf_create_time,</if>
            <if test="sfRemark != null">sf_remark,</if>
            <if test="sfEvaluation != null">sf_evaluation,</if>
            <if test="sfTotalPrice != null">sf_total_price,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="sfUserName != null">#{sfUserName},</if>
            <if test="sfUserId != null">#{sfUserId},</if>
            <if test="tableId != null">#{tableId},</if>
            <if test="tableNumber != null">#{tableNumber},</if>
            <if test="status != null">#{status},</if>
            <if test="sfOrderNumber != null">#{sfOrderNumber},</if>
            <if test="sfCreateTime != null">#{sfCreateTime},</if>
            <if test="sfRemark != null">#{sfRemark},</if>
            <if test="sfEvaluation != null">#{sfEvaluation},</if>
            <if test="sfTotalPrice != null">#{sfTotalPrice},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.example.entity.Dingdan">
        update sf_order
        <set>
            <if test="sfUserName != null">
                sf_user_name = #{sfUserName},
            </if>
            <if test="sfUserId != null">
                sf_user_id = #{sfUserId},
            </if>
            <if test="tableId != null">
                table_id = #{tableId},
            </if>
            <if test="tableNumber != null">
                table_number = #{tableNumber},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="sfOrderNumber != null">
                sf_order_number = #{sfOrderNumber},
            </if>
            <if test="sfCreateTime != null">
                sf_create_time = #{sfCreateTime},
            </if>
            <if test="sfRemark != null">
                sf_remark = #{sfRemark},
            </if>
            <if test="sfEvaluation != null">
                sf_evaluation = #{sfEvaluation},
            </if>
            <if test="sfTotalPrice != null">
                sf_total_price = #{sfTotalPrice},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>