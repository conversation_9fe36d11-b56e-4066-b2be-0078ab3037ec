-- 项目管理系统数据库脚本
-- 数据库名称: diancan002

DROP DATABASE IF EXISTS `diancan002`;
CREATE DATABASE `diancan002` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `diancan002`;

-- 1. 管理员表 (sf_admin)
DROP TABLE IF EXISTS `sf_admin`;
CREATE TABLE `sf_admin` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `username` varchar(255) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `name` varchar(255) DEFAULT NULL COMMENT '姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `email` varchar(255) DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像',
  `role` varchar(50) DEFAULT 'ADMIN' COMMENT '角色标识',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- 2. 用户表 (sf_user)
DROP TABLE IF EXISTS `sf_user`;
CREATE TABLE `sf_user` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `username` varchar(255) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `name` varchar(255) DEFAULT NULL COMMENT '昵称',
  `phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `email` varchar(255) DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像',
  `role` varchar(50) DEFAULT 'USER' COMMENT '角色标识',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 3. 商家表 (sf_business)
DROP TABLE IF EXISTS `sf_business`;
CREATE TABLE `sf_business` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `username` varchar(255) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `name` varchar(255) DEFAULT NULL COMMENT '姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `email` varchar(255) DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像',
  `role` varchar(50) DEFAULT 'BUSINESS' COMMENT '角色标识',
  `sf_description` text COMMENT '商家描述',
  `status` varchar(20) DEFAULT '待审核' COMMENT '状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商家表';

-- 4. 分类表 (sf_category)
DROP TABLE IF EXISTS `sf_category`;
CREATE TABLE `sf_category` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `icon` varchar(500) DEFAULT NULL COMMENT '分类图标',
  `sort_order` int DEFAULT 0 COMMENT '排序权重',
  `status` varchar(20) DEFAULT '启用' COMMENT '分类状态：启用/禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='食物分类表';

-- 5. 食品表 (sf_food)
DROP TABLE IF EXISTS `sf_food`;
CREATE TABLE `sf_food` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) NOT NULL COMMENT '食品名称',
  `sf_image` varchar(500) DEFAULT NULL COMMENT '食品图片',
  `sf_description` text COMMENT '食品描述',
  `sf_category` varchar(100) DEFAULT NULL COMMENT '食品类型（保留兼容性）',
  `category_id` int DEFAULT NULL COMMENT '分类ID',
  `sf_price` varchar(20) DEFAULT NULL COMMENT '食品价格',
  `sf_stock` varchar(20) DEFAULT NULL COMMENT '库存数量',
  `sf_shelf_status` varchar(20) DEFAULT '上架' COMMENT '上架状态',
  `average_rating` decimal(3,2) DEFAULT 0.00 COMMENT '平均评分',
  `review_count` int DEFAULT 0 COMMENT '评价总数',
  `last_review_time` datetime DEFAULT NULL COMMENT '最新评价时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_sf_food_name` (`name`),
  KEY `idx_average_rating` (`average_rating`),
  KEY `idx_review_count` (`review_count`),
  CONSTRAINT `fk_food_category` FOREIGN KEY (`category_id`) REFERENCES `sf_category` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='食品表';

-- 6. 购物车表 (sf_cart)
DROP TABLE IF EXISTS `sf_cart`;
CREATE TABLE `sf_cart` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `food_id` int NOT NULL COMMENT '商品ID',
  `quantity` int NOT NULL DEFAULT 1 COMMENT '商品数量',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_food` (`user_id`, `food_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_food_id` (`food_id`),
  CONSTRAINT `fk_cart_user` FOREIGN KEY (`user_id`) REFERENCES `sf_user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_cart_food` FOREIGN KEY (`food_id`) REFERENCES `sf_food` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购物车表';

-- 7. 餐桌管理表 (sf_table)
DROP TABLE IF EXISTS `sf_table`;
CREATE TABLE `sf_table` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `table_number` varchar(20) NOT NULL COMMENT '餐桌号',
  `seats` int DEFAULT 4 COMMENT '座位数',
  `area` varchar(50) COMMENT '区域（大厅、包间等）',
  `status` varchar(20) DEFAULT '空闲' COMMENT '状态：空闲、使用中、清洁中、维修中',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_table_number` (`table_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='餐桌管理表';

-- 8. 订单表 (sf_order) - 简化结构
DROP TABLE IF EXISTS `sf_order`;
CREATE TABLE `sf_order` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sf_user_name` varchar(255) DEFAULT NULL COMMENT '用户名',
  `sf_user_id` int DEFAULT NULL COMMENT '用户ID',
  `table_id` int DEFAULT NULL COMMENT '餐桌ID',
  `table_number` varchar(20) DEFAULT NULL COMMENT '餐桌号',
  `status` varchar(20) DEFAULT '待支付' COMMENT '订单状态：待支付、已支付、制作中、待取餐、已完成、已取消、退款中、已退款',
  `sf_order_number` int DEFAULT NULL COMMENT '订单编号',
  `sf_create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `sf_remark` text COMMENT '用户备注',
  `sf_evaluation` text COMMENT '用户评价',
  `sf_total_price` decimal(10,2) DEFAULT NULL COMMENT '订单总价格',
  PRIMARY KEY (`id`),
  KEY `idx_sf_user_id` (`sf_user_id`),
  KEY `idx_sf_order_number` (`sf_order_number`),
  KEY `idx_table_id` (`table_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 9. 订单详情表 (sf_order_item) - 支持多商品订单
DROP TABLE IF EXISTS `sf_order_item`;
CREATE TABLE `sf_order_item` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` int NOT NULL COMMENT '订单ID',
  `food_id` int NOT NULL COMMENT '商品ID',
  `food_name` varchar(255) NOT NULL COMMENT '商品名称',
  `food_price` decimal(10,2) NOT NULL COMMENT '商品单价',
  `quantity` int NOT NULL COMMENT '商品数量',
  `subtotal` decimal(10,2) NOT NULL COMMENT '小计金额',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_food_id` (`food_id`),
  CONSTRAINT `fk_order_item_order` FOREIGN KEY (`order_id`) REFERENCES `sf_order` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单详情表';

-- 10. 博客表 (sf_blog)
DROP TABLE IF EXISTS `sf_blog`;
CREATE TABLE `sf_blog` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(255) NOT NULL COMMENT '博客标题',
  `content` longtext COMMENT '博客内容',
  `sf_cover_image` varchar(500) DEFAULT NULL COMMENT '博客封面图片',
  `sf_created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `status` varchar(20) DEFAULT '发布' COMMENT '博客状态',
  `sf_category_name` varchar(100) DEFAULT NULL COMMENT '分类名称',
  `sf_tags` varchar(500) DEFAULT NULL COMMENT '博客标签',
  `sf_view_count` int DEFAULT 0 COMMENT '浏览次数',
  `sf_author_id` int DEFAULT NULL COMMENT '作者ID',
  `sf_author_name` varchar(255) DEFAULT NULL COMMENT '作者名称',
  PRIMARY KEY (`id`),
  KEY `idx_sf_author_id` (`sf_author_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='博客表';

-- 11. 公告表 (sf_notice)
DROP TABLE IF EXISTS `sf_notice`;
CREATE TABLE `sf_notice` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(255) NOT NULL COMMENT '标题',
  `content` longtext COMMENT '内容',
  `time` varchar(50) DEFAULT NULL COMMENT '创建时间',
  `user` varchar(255) DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公告信息表';

-- 12. 投诉表 (sf_complaint)
DROP TABLE IF EXISTS `sf_complaint`;
CREATE TABLE `sf_complaint` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(255) NOT NULL COMMENT '标题',
  `sf_content` text COMMENT '投诉内容',
  `sf_image` varchar(500) DEFAULT NULL COMMENT '投诉图片',
  `complaint_type` varchar(50) DEFAULT NULL COMMENT '投诉类型',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `status` varchar(20) DEFAULT '待处理' COMMENT '投诉状态',
  `sf_complaint_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '投诉日期',
  `reply` text COMMENT '回复信息',
  `sf_user_id` int DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_sf_user_id` (`sf_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='投诉表';

-- 13. 留言表 (sf_message)
DROP TABLE IF EXISTS `sf_message`;
CREATE TABLE `sf_message` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sf_user_id` int DEFAULT NULL COMMENT '用户ID',
  `sf_question` text COMMENT '问题内容',
  `reply` text COMMENT '回复内容',
  `sf_image` varchar(500) DEFAULT NULL COMMENT '留言图片',
  `sf_leave_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '留言时间',
  `sf_reply_time` datetime DEFAULT NULL COMMENT '回复时间',
  PRIMARY KEY (`id`),
  KEY `idx_sf_user_id` (`sf_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='留言表';

-- 14. 评论表 (sf_comment)
DROP TABLE IF EXISTS `sf_comment`;
CREATE TABLE `sf_comment` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `content` text COMMENT '评论内容',
  `time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '评论时间',
  `sf_user_id` int DEFAULT NULL COMMENT '用户ID',
  `sf_user_name` varchar(255) DEFAULT NULL COMMENT '用户名',
  `sf_blog_id` int DEFAULT NULL COMMENT '博客ID',
  PRIMARY KEY (`id`),
  KEY `idx_sf_user_id` (`sf_user_id`),
  KEY `idx_sf_blog_id` (`sf_blog_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论表';

-- 15. 菜品评价表 (sf_food_review)
DROP TABLE IF EXISTS `sf_food_review`;
CREATE TABLE `sf_food_review` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int NOT NULL COMMENT '评价用户ID',
  `user_name` varchar(255) DEFAULT NULL COMMENT '用户昵称（冗余字段，提高查询效率）',
  `food_id` int NOT NULL COMMENT '菜品ID',
  `food_name` varchar(255) DEFAULT NULL COMMENT '菜品名称（冗余字段）',
  `order_id` int DEFAULT NULL COMMENT '关联订单ID',
  `rating` int NOT NULL COMMENT '评分（1-5星）',
  `content` text COMMENT '评价内容',
  `images` text COMMENT '评价图片（多张图片以逗号分隔）',
  `status` varchar(20) DEFAULT '正常' COMMENT '评价状态：正常、已删除、待审核',
  `reply_content` text COMMENT '商家/管理员回复内容',
  `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
  `reply_user` varchar(255) DEFAULT NULL COMMENT '回复人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '评价时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_anonymous` tinyint DEFAULT 0 COMMENT '是否匿名评价（0-否，1-是）',
  `helpful_count` int DEFAULT 0 COMMENT '有用评价数量',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_food` (`user_id`, `food_id`),
  KEY `idx_food_id` (`food_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_rating` (`rating`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_review_user` FOREIGN KEY (`user_id`) REFERENCES `sf_user` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_review_food` FOREIGN KEY (`food_id`) REFERENCES `sf_food` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜品评价表';





-- 初始化数据

-- 插入管理员数据
INSERT INTO `sf_admin` (`username`, `password`, `name`, `phone`, `email`, `avatar`, `role`) VALUES
('admin', '123456', '系统管理员', '***********', '<EMAIL>', NULL, 'ADMIN');

-- 插入用户数据
INSERT INTO `sf_user` (`username`, `password`, `name`, `phone`, `email`, `avatar`, `role`) VALUES
('user1', '123456', '用户1', '***********', '<EMAIL>', NULL, 'USER'),
('user2', '123456', '用户2', '***********', '<EMAIL>', NULL, 'USER');

-- 插入商家数据
INSERT INTO `sf_business` (`username`, `password`, `name`, `phone`, `email`, `avatar`, `role`, `sf_description`, `status`) VALUES
('business1', '123456', '商家1', '***********', '<EMAIL>', NULL, 'BUSINESS', '这是一家优质餐厅', '已审核'),
('business2', '123456', '商家2', '***********', '<EMAIL>', NULL, 'BUSINESS', '这是一家特色小吃店', '待审核');

-- 插入分类数据
INSERT INTO `sf_category` (`name`, `description`, `icon`, `sort_order`, `status`) VALUES
('热门推荐', '系统推荐的热门食物', NULL, 1, '启用'),
('川菜', '麻辣鲜香的川菜系列', NULL, 2, '启用'),
('家常菜', '传统家常菜系列', NULL, 3, '启用'),
('主食', '米饭、面条等主食类', NULL, 4, '启用'),
('饮品', '各类饮料饮品', NULL, 5, '启用'),
('甜品', '各类甜品点心', NULL, 6, '启用');

-- 插入餐桌数据
INSERT INTO `sf_table` (`table_number`, `seats`, `area`, `status`) VALUES
('A01', 2, '大厅', '空闲'),
('A02', 4, '大厅', '空闲'),
('A03', 4, '大厅', '空闲'),
('A04', 6, '大厅', '空闲'),
('B01', 8, '包间', '空闲'),
('B02', 10, '包间', '空闲'),
('C01', 2, '靠窗', '空闲'),
('C02', 2, '靠窗', '空闲');

-- 插入食品数据
INSERT INTO `sf_food` (`name`, `sf_image`, `sf_description`, `sf_category`, `category_id`, `sf_price`, `sf_stock`, `sf_shelf_status`) VALUES
('宫保鸡丁', NULL, '经典川菜，麻辣鲜香', '川菜', 2, '28.00', '100', '上架'),
('红烧肉', NULL, '传统家常菜，肥而不腻', '家常菜', 3, '35.00', '50', '上架'),
('麻婆豆腐', NULL, '四川名菜，麻辣鲜嫩', '川菜', 2, '18.00', '80', '上架'),
('糖醋排骨', NULL, '酸甜可口，老少皆宜', '家常菜', 3, '32.00', '60', '上架');

-- 插入公告数据
INSERT INTO `sf_notice` (`title`, `content`, `time`, `user`) VALUES
('系统维护通知', '系统将于本周日凌晨2:00-4:00进行维护，请合理安排时间。', '2024-01-01 10:00:00', 'admin'),
('新功能上线', '新增了在线客服功能，欢迎大家使用。', '2024-01-02 14:30:00', 'admin');

-- 插入博客数据
INSERT INTO `sf_blog` (`title`, `content`, `sf_cover_image`, `sf_created_at`, `status`, `sf_category_name`, `sf_tags`, `sf_view_count`, `sf_author_id`, `sf_author_name`) VALUES
('美食制作技巧分享', '分享一些实用的美食制作技巧...', NULL, '2024-01-01 10:00:00', '发布', '美食', '美食,技巧', 0, 1, 'user1'),
('餐厅经营心得', '作为一名餐厅老板，我想分享一些经营心得...', NULL, '2024-01-02 15:30:00', '发布', '经营', '餐厅,经营', 0, 1, 'user1');

-- 插入菜品评价测试数据
INSERT INTO `sf_food_review` (`user_id`, `user_name`, `food_id`, `food_name`, `order_id`, `rating`, `content`, `images`, `status`, `create_time`, `is_anonymous`, `helpful_count`) VALUES
(1, 'user1', 1, '宫保鸡丁', NULL, 5, '味道很棒，麻辣鲜香，分量也足够，值得推荐！', NULL, '正常', '2024-01-15 12:30:00', 0, 3),
(2, 'user2', 1, '宫保鸡丁', NULL, 4, '口感不错，就是稍微有点咸，整体还是很满意的。', NULL, '正常', '2024-01-16 19:45:00', 0, 1),
(1, 'user1', 2, '红烧肉', NULL, 5, '红烧肉做得很地道，肥而不腻，入口即化，强烈推荐！', NULL, '正常', '2024-01-17 13:20:00', 0, 5),
(2, 'user2', 3, '麻婆豆腐', NULL, 3, '豆腐很嫩，但是辣度不够，希望能做得更正宗一些。', NULL, '正常', '2024-01-18 20:15:00', 0, 0);

-- 更新菜品评价统计信息
UPDATE `sf_food` SET `average_rating` = 4.50, `review_count` = 2, `last_review_time` = '2024-01-16 19:45:00' WHERE `id` = 1;
UPDATE `sf_food` SET `average_rating` = 5.00, `review_count` = 1, `last_review_time` = '2024-01-17 13:20:00' WHERE `id` = 2;
UPDATE `sf_food` SET `average_rating` = 3.00, `review_count` = 1, `last_review_time` = '2024-01-18 20:15:00' WHERE `id` = 3;



-- 创建索引
CREATE INDEX idx_sf_user_username ON `sf_user`(`username`);
CREATE INDEX idx_sf_admin_username ON `sf_admin`(`username`);
CREATE INDEX idx_sf_business_username ON `sf_business`(`username`);
CREATE INDEX idx_sf_category_name ON `sf_category`(`name`);
CREATE INDEX idx_sf_order_status ON `sf_order`(`status`);
CREATE INDEX idx_sf_blog_title ON `sf_blog`(`title`);
CREATE INDEX idx_sf_complaint_status ON `sf_complaint`(`status`);
CREATE INDEX idx_sf_notice_title ON `sf_notice`(`title`);
CREATE INDEX idx_sf_food_review_status ON `sf_food_review`(`status`);
CREATE INDEX idx_sf_food_review_create_time ON `sf_food_review`(`create_time`);

-- 创建餐桌状态查询视图
DROP VIEW IF EXISTS `v_table_status`;
CREATE VIEW `v_table_status` AS
SELECT 
    t.id,
    t.table_number,
    t.seats,
    t.area,
    t.status as table_status,
    CASE 
        WHEN COUNT(o.id) > 0 THEN '占用中'
        ELSE '空闲'
    END as occupy_status,
    COUNT(o.id) as active_orders
FROM sf_table t
LEFT JOIN sf_order o ON t.table_number = o.table_number 
    AND o.status IN ('待支付', '已支付', '制作中', '待取餐')
GROUP BY t.id, t.table_number, t.seats, t.area, t.status;

-- 创建菜品评价统计视图
DROP VIEW IF EXISTS `v_food_review_stats`;
CREATE VIEW `v_food_review_stats` AS
SELECT 
    f.id as food_id,
    f.name as food_name,
    COALESCE(AVG(fr.rating), 0) as avg_rating,
    COUNT(fr.id) as total_reviews,
    COUNT(CASE WHEN fr.rating = 5 THEN 1 END) as five_star,
    COUNT(CASE WHEN fr.rating = 4 THEN 1 END) as four_star,
    COUNT(CASE WHEN fr.rating = 3 THEN 1 END) as three_star,
    COUNT(CASE WHEN fr.rating = 2 THEN 1 END) as two_star,
    COUNT(CASE WHEN fr.rating = 1 THEN 1 END) as one_star,
    MAX(fr.create_time) as latest_review_time
FROM sf_food f
LEFT JOIN sf_food_review fr ON f.id = fr.food_id AND fr.status = '正常'
GROUP BY f.id, f.name; 