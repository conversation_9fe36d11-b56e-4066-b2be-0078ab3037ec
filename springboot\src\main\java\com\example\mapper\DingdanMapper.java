package com.example.mapper;

import com.example.entity.Dingdan;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 操作订单表相关数据接口
 */
public interface DingdanMapper {

    /**
     * 新增
     */
    int insert(Dingdan dingdan);

    /**
     * 删除
     */
    int deleteById(Integer id);

    /**
     * 修改
     */
    int updateById(Dingdan dingdan);

    /**
     * 根据ID查询
     */
    Dingdan selectById(Integer id);

    /**
     * 查询所有
     */
    List<Dingdan> selectAll(Dingdan dingdan);


    /**
     * 统计订单状态分布
     */
    List<Map<String, Object>> countByStatus();

    /**
     * 统计每月订单量
     */
    List<Map<String, Object>> countByMonth(@Param("year") Integer year);

    /**
     * 统计每月收入
     */
    List<Map<String, Object>> revenueByMonth(@Param("year") Integer year);

    /**
     * 统计菜品销量排行
     */
    List<Map<String, Object>> getPopularFoods(@Param("limit") Integer limit);

    /**
     * 统计用户注册趋势
     */
    List<Map<String, Object>> getUserRegistrationByMonth(@Param("year") Integer year);



    /**
     * 统计分类销售占比
     */
    List<Map<String, Object>> getCategoryRevenueStats();



    /**
     * 统计用户活跃度
     */
    List<Map<String, Object>> getUserActivityStats(@Param("days") Integer days);

    /**
     * 获取综合统计数据
     */
    Map<String, Object> getOverviewStats();
}