package com.example.controller;

import com.example.common.Result;
import com.example.entity.Foods;
import com.example.service.FoodsService;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

/**
 * 商品表前端操作接口
 **/
@RestController
@RequestMapping("/foods")
public class FoodsController {

    @Resource
    private FoodsService foodsService;

    /**
     * 新增
     */
    @PostMapping("/add")
    public Result add(@RequestBody Foods foods) {
        foodsService.add(foods);
        return Result.success();
    }

    /**
     * 删除
     */
    @DeleteMapping("/delete/{id}")
    public Result deleteById(@PathVariable Integer id) {
        foodsService.deleteById(id);
        return Result.success();
    }

    /**
     * 批量删除
     */
    @DeleteMapping("/delete/batch")
    public Result deleteBatch(@RequestBody List<Integer> ids) {
        foodsService.deleteBatch(ids);
        return Result.success();
    }

    /**
     * 修改
     */
    @PutMapping("/update")
    public Result updateById(@RequestBody Foods foods) {
        foodsService.updateById(foods);
        return Result.success();
    }

    /**
     * 根据ID查询
     */
    @GetMapping("/selectById/{id}")
    public Result selectById(@PathVariable Integer id) {
        Foods foods = foodsService.selectById(id);
        return Result.success(foods);
    }

    /**
     * 查询所有
     */
    @GetMapping("/selectAll")
    public Result selectAll(Foods foods) {
        List<Foods> list = foodsService.selectAll(foods);
        return Result.success(list);
    }

    /**
     * 分页查询
     */
    @GetMapping("/selectPage")
    public Result selectPage(Foods foods,
                             @RequestParam(defaultValue = "1") Integer pageNum,
                             @RequestParam(defaultValue = "10") Integer pageSize) {
        PageInfo<Foods> page = foodsService.selectPage(foods, pageNum, pageSize);
        return Result.success(page);
    }

    /**
     * 按分类查询食物
     */
    @GetMapping("/selectByCategory/{categoryId}")
    public Result selectByCategory(@PathVariable Integer categoryId) {
        List<Foods> list = foodsService.selectByCategoryId(categoryId);
        return Result.success(list);
    }

    /**
     * 按分类分页查询食物
     */
    @GetMapping("/selectPageByCategory")
    public Result selectPageByCategory(@RequestParam Integer categoryId,
                                       @RequestParam(defaultValue = "1") Integer pageNum,
                                       @RequestParam(defaultValue = "10") Integer pageSize) {
        PageInfo<Foods> page = foodsService.selectPageByCategory(categoryId, pageNum, pageSize);
        return Result.success(page);
    }

    /**
     * 获取菜品详情（包含评价统计信息）
     */
    @GetMapping("/selectByIdWithReviews/{id}")
    public Result selectByIdWithReviews(@PathVariable Integer id) {
        Foods foods = foodsService.selectByIdWithReviews(id);
        return Result.success(foods);
    }

    /**
     * 分页查询菜品（包含评价统计信息）
     */
    @GetMapping("/selectPageWithReviews")
    public Result selectPageWithReviews(Foods foods,
                                        @RequestParam(defaultValue = "1") Integer pageNum,
                                        @RequestParam(defaultValue = "10") Integer pageSize) {
        PageInfo<Foods> page = foodsService.selectAllWithReviews(foods, pageNum, pageSize);
        return Result.success(page);
    }

    /**
     * 按分类查询食物（包含评价统计信息）
     */
    @GetMapping("/selectByCategoryWithReviews/{categoryId}")
    public Result selectByCategoryWithReviews(@PathVariable Integer categoryId) {
        List<Foods> list = foodsService.selectByCategoryIdWithReviews(categoryId);
        return Result.success(list);
    }

    /**
     * 按评分排序查询菜品
     */
    @GetMapping("/selectByRatingOrder")
    public Result selectByRatingOrder(Foods foods,
                                      @RequestParam(defaultValue = "1") Integer pageNum,
                                      @RequestParam(defaultValue = "10") Integer pageSize,
                                      @RequestParam(defaultValue = "desc") String orderType) {
        PageInfo<Foods> page = foodsService.selectByRatingOrder(foods, pageNum, pageSize, orderType);
        return Result.success(page);
    }

    /**
     * 按评分范围筛选菜品
     */
    @GetMapping("/selectByRatingRange")
    public Result selectByRatingRange(@RequestParam(required = false) Double minRating,
                                      @RequestParam(required = false) Double maxRating,
                                      @RequestParam(defaultValue = "1") Integer pageNum,
                                      @RequestParam(defaultValue = "10") Integer pageSize) {
        PageInfo<Foods> page = foodsService.selectByRatingRange(minRating, maxRating, pageNum, pageSize);
        return Result.success(page);
    }

    /**
     * 获取推荐菜品（基于评价）
     */
    @GetMapping("/recommended")
    public Result getRecommendedFoods(@RequestParam(defaultValue = "10") Integer limit) {
        List<Foods> list = foodsService.getRecommendedFoods(limit);
        return Result.success(list);
    }
}