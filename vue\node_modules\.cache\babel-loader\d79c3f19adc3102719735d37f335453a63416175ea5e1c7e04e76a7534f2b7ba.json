{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.filter.js\";\nimport StarRating from './StarRating.vue';\nexport default {\n  name: 'FoodReviewList',\n  components: {\n    StarRating\n  },\n  props: {\n    // 菜品ID\n    foodId: {\n      type: Number,\n      required: true\n    },\n    // 是否显示统计信息\n    showStats: {\n      type: Boolean,\n      default: true\n    },\n    // 是否显示筛选器\n    showFilters: {\n      type: Boolean,\n      default: true\n    },\n    // 是否显示管理员操作\n    showAdminActions: {\n      type: Boolean,\n      default: false\n    },\n    // 是否显示添加评价按钮\n    showAddButton: {\n      type: Boolean,\n      default: true\n    },\n    // 每页数量\n    pageSize: {\n      type: Number,\n      default: 10\n    }\n  },\n  data() {\n    return {\n      reviews: [],\n      stats: null,\n      loading: false,\n      hasMore: true,\n      currentPage: 1,\n      currentFilter: {\n        star: 0,\n        // 0表示全部\n        sortBy: 'time' // time, rating, helpful\n      },\n      ratingTexts: ['很差', '较差', '一般', '较好', '很好']\n    };\n  },\n  computed: {\n    displayReviews() {\n      let filtered = [...this.reviews];\n\n      // 按星级筛选\n      if (this.currentFilter.star > 0) {\n        filtered = filtered.filter(review => review.rating === this.currentFilter.star);\n      }\n\n      // 排序\n      switch (this.currentFilter.sortBy) {\n        case 'rating':\n          filtered.sort((a, b) => b.rating - a.rating);\n          break;\n        case 'helpful':\n          filtered.sort((a, b) => (b.helpfulCount || 0) - (a.helpfulCount || 0));\n          break;\n        default:\n          // time\n          filtered.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));\n      }\n      return filtered;\n    }\n  },\n  created() {\n    this.loadReviews();\n    this.loadStats();\n  },\n  methods: {\n    // 加载评价列表\n    async loadReviews(reset = true) {\n      if (this.loading) return;\n      this.loading = true;\n      try {\n        const params = {\n          pageNum: reset ? 1 : this.currentPage + 1,\n          pageSize: this.pageSize\n        };\n        const response = await this.$request.get(`/foodReview/food/${this.foodId}`, {\n          params\n        });\n        if (response.code === '200') {\n          const newReviews = response.data?.list || [];\n          if (reset) {\n            this.reviews = newReviews;\n            this.currentPage = 1;\n          } else {\n            this.reviews.push(...newReviews);\n            this.currentPage++;\n          }\n          this.hasMore = newReviews.length === this.pageSize;\n          this.$emit('reviews-loaded', {\n            reviews: this.reviews,\n            total: response.data?.total || 0\n          });\n        } else {\n          this.$message.error(response.msg || '加载评价失败');\n        }\n      } catch (error) {\n        console.error('加载评价失败:', error);\n        this.$message.error('加载评价失败');\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 加载评价统计\n    async loadStats() {\n      try {\n        const response = await this.$request.get(`/foodReview/stats/${this.foodId}`);\n        if (response.code === '200') {\n          this.stats = response.data;\n        }\n      } catch (error) {\n        console.error('加载评价统计失败:', error);\n      }\n    },\n    // 加载更多\n    loadMore() {\n      this.loadReviews(false);\n    },\n    // 按星级筛选\n    filterByStar(star) {\n      this.currentFilter.star = star;\n      this.$emit('filter-change', this.currentFilter);\n    },\n    // 排序改变\n    handleSortChange() {\n      this.$emit('sort-change', this.currentFilter);\n    },\n    // 切换有用性\n    async toggleHelpful(review) {\n      try {\n        const response = await this.$request.post('/foodReview/helpful', {\n          reviewId: review.id\n        });\n        if (response.code === '200') {\n          // 更新本地数据\n          const index = this.reviews.findIndex(r => r.id === review.id);\n          if (index > -1) {\n            this.$set(this.reviews[index], 'helpfulCount', response.data.helpfulCount);\n            this.$set(this.reviews[index], 'isHelpful', response.data.isHelpful);\n          }\n        } else {\n          this.$message.error(response.msg || '操作失败');\n        }\n      } catch (error) {\n        console.error('操作失败:', error);\n        this.$message.error('操作失败');\n      }\n    },\n    // 回复评价（管理员）\n    replyReview(review) {\n      this.$emit('reply-review', review);\n    },\n    // 删除评价（管理员）\n    deleteReview(review) {\n      this.$confirm('确定要删除这条评价吗？', '确认删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.$emit('delete-review', review);\n      });\n    },\n    // 预览图片\n    previewImage(image) {\n      // Element UI的图片组件会自动处理预览\n    },\n    // 获取图片列表\n    getImageList(images) {\n      if (!images) return [];\n      return images.split(',').filter(img => img.trim());\n    },\n    // 获取评分文字\n    getRatingText(rating) {\n      if (rating === 0) return '暂无评分';\n      const index = Math.ceil(rating) - 1;\n      return this.ratingTexts[index] || `${rating}星`;\n    },\n    // 获取星级百分比\n    getStarPercentage(star) {\n      if (!this.stats || !this.stats.totalReviews) return 0;\n      const count = this.getStarCount(star);\n      return Math.round(count / this.stats.totalReviews * 100);\n    },\n    // 获取星级数量\n    getStarCount(star) {\n      if (!this.stats) return 0;\n      const countMap = {\n        1: this.stats.oneStar || 0,\n        2: this.stats.twoStar || 0,\n        3: this.stats.threeStar || 0,\n        4: this.stats.fourStar || 0,\n        5: this.stats.fiveStar || 0\n      };\n      return countMap[star] || 0;\n    },\n    // 格式化时间\n    formatTime(time) {\n      if (!time) return '';\n      const date = new Date(time);\n      const now = new Date();\n      const diff = now - date;\n\n      // 小于1分钟\n      if (diff < 60 * 1000) {\n        return '刚刚';\n      }\n\n      // 小于1小时\n      if (diff < 60 * 60 * 1000) {\n        return `${Math.floor(diff / (60 * 1000))}分钟前`;\n      }\n\n      // 小于1天\n      if (diff < 24 * 60 * 60 * 1000) {\n        return `${Math.floor(diff / (60 * 60 * 1000))}小时前`;\n      }\n\n      // 小于7天\n      if (diff < 7 * 24 * 60 * 60 * 1000) {\n        return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`;\n      }\n\n      // 超过7天显示具体日期\n      return date.toLocaleDateString();\n    },\n    // 刷新评价列表\n    refresh() {\n      this.loadReviews(true);\n      this.loadStats();\n    }\n  }\n};", "map": {"version": 3, "names": ["StarRating", "name", "components", "props", "foodId", "type", "Number", "required", "showStats", "Boolean", "default", "showFilters", "showAdminActions", "showAddButton", "pageSize", "data", "reviews", "stats", "loading", "hasMore", "currentPage", "currentFilter", "star", "sortBy", "ratingTexts", "computed", "displayReviews", "filtered", "filter", "review", "rating", "sort", "a", "b", "helpfulCount", "Date", "createTime", "created", "loadReviews", "loadStats", "methods", "reset", "params", "pageNum", "response", "$request", "get", "code", "newReviews", "list", "push", "length", "$emit", "total", "$message", "error", "msg", "console", "loadMore", "filterByStar", "handleSortChange", "toggleHelpful", "post", "reviewId", "id", "index", "findIndex", "r", "$set", "isHelpful", "reply<PERSON><PERSON>ie<PERSON>", "deleteReview", "$confirm", "confirmButtonText", "cancelButtonText", "then", "previewImage", "image", "getImageList", "images", "split", "img", "trim", "getRatingText", "Math", "ceil", "getStarPercentage", "totalReviews", "count", "getStarCount", "round", "countMap", "oneStar", "twoStar", "threeStar", "fourStar", "fiveStar", "formatTime", "time", "date", "now", "diff", "floor", "toLocaleDateString", "refresh"], "sources": ["src/components/FoodReviewList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"food-review-list\">\r\n    <!-- 评价统计概览 -->\r\n    <div class=\"review-stats\" v-if=\"showStats && stats\">\r\n      <div class=\"stats-overview\">\r\n        <div class=\"avg-rating\">\r\n          <div class=\"rating-number\">{{ stats.avgRating || 0 }}</div>\r\n          <star-rating \r\n            :value=\"stats.avgRating || 0\" \r\n            :readonly=\"true\" \r\n            :show-text=\"false\"\r\n            size=\"medium\">\r\n          </star-rating>\r\n          <div class=\"rating-text\">{{ getRatingText(stats.avgRating || 0) }}</div>\r\n        </div>\r\n        <div class=\"stats-detail\">\r\n          <div class=\"total-reviews\">共 {{ stats.totalReviews || 0 }} 条评价</div>\r\n          <div class=\"rating-distribution\">\r\n            <div \r\n              class=\"rating-bar\" \r\n              v-for=\"star in 5\" \r\n              :key=\"star\"\r\n              @click=\"filterByStar(6 - star)\">\r\n              <span class=\"star-label\">{{ 6 - star }}星</span>\r\n              <div class=\"bar-container\">\r\n                <div \r\n                  class=\"bar-fill\" \r\n                  :style=\"{ width: getStarPercentage(6 - star) + '%' }\">\r\n                </div>\r\n              </div>\r\n              <span class=\"star-count\">{{ getStarCount(6 - star) }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 筛选和排序 -->\r\n    <div class=\"review-filters\" v-if=\"showFilters\">\r\n      <div class=\"filter-left\">\r\n        <el-button-group>\r\n          <el-button \r\n            :type=\"currentFilter.star === 0 ? 'primary' : 'default'\"\r\n            size=\"small\"\r\n            @click=\"filterByStar(0)\">\r\n            全部\r\n          </el-button>\r\n          <el-button \r\n            v-for=\"star in 5\" \r\n            :key=\"star\"\r\n            :type=\"currentFilter.star === star ? 'primary' : 'default'\"\r\n            size=\"small\"\r\n            @click=\"filterByStar(star)\">\r\n            {{ star }}星\r\n          </el-button>\r\n        </el-button-group>\r\n      </div>\r\n      <div class=\"filter-right\">\r\n        <el-select \r\n          v-model=\"currentFilter.sortBy\" \r\n          size=\"small\" \r\n          @change=\"handleSortChange\">\r\n          <el-option label=\"按时间排序\" value=\"time\"></el-option>\r\n          <el-option label=\"按评分排序\" value=\"rating\"></el-option>\r\n          <el-option label=\"按有用性排序\" value=\"helpful\"></el-option>\r\n        </el-select>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 评价列表 -->\r\n    <div class=\"review-list\">\r\n      <div \r\n        class=\"review-item\" \r\n        v-for=\"review in displayReviews\" \r\n        :key=\"review.id\">\r\n        \r\n        <!-- 用户信息 -->\r\n        <div class=\"review-header\">\r\n          <div class=\"user-info\">\r\n            <el-avatar \r\n              :size=\"40\" \r\n              :src=\"review.userAvatar\" \r\n              :alt=\"review.userName\">\r\n              <i class=\"el-icon-user-solid\"></i>\r\n            </el-avatar>\r\n            <div class=\"user-detail\">\r\n              <div class=\"user-name\">\r\n                {{ review.isAnonymous === 1 ? '匿名用户' : (review.userName || '用户') }}\r\n              </div>\r\n              <div class=\"review-time\">{{ formatTime(review.createTime) }}</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"review-rating\">\r\n            <star-rating \r\n              :value=\"review.rating\" \r\n              :readonly=\"true\" \r\n              :show-text=\"false\"\r\n              size=\"small\">\r\n            </star-rating>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 评价内容 -->\r\n        <div class=\"review-content\">\r\n          <p class=\"review-text\" v-if=\"review.content\">{{ review.content }}</p>\r\n          \r\n          <!-- 评价图片 -->\r\n          <div class=\"review-images\" v-if=\"review.images\">\r\n            <div \r\n              class=\"image-item\" \r\n              v-for=\"(image, index) in getImageList(review.images)\" \r\n              :key=\"index\"\r\n              @click=\"previewImage(image)\">\r\n              <el-image \r\n                :src=\"image\" \r\n                fit=\"cover\"\r\n                :preview-src-list=\"getImageList(review.images)\"\r\n                class=\"review-image\">\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 商家回复 -->\r\n        <div class=\"review-reply\" v-if=\"review.replyContent\">\r\n          <div class=\"reply-header\">\r\n            <i class=\"el-icon-service\"></i>\r\n            <span class=\"reply-label\">商家回复：</span>\r\n            <span class=\"reply-time\">{{ formatTime(review.replyTime) }}</span>\r\n          </div>\r\n          <div class=\"reply-content\">{{ review.replyContent }}</div>\r\n        </div>\r\n\r\n        <!-- 操作按钮 -->\r\n        <div class=\"review-actions\">\r\n          <el-button \r\n            type=\"text\" \r\n            size=\"small\"\r\n            :class=\"{ 'is-active': review.isHelpful }\"\r\n            @click=\"toggleHelpful(review)\">\r\n            <i class=\"el-icon-thumb\"></i>\r\n            有用 ({{ review.helpfulCount || 0 }})\r\n          </el-button>\r\n          \r\n          <!-- 管理员操作 -->\r\n          <template v-if=\"showAdminActions\">\r\n            <el-button \r\n              type=\"text\" \r\n              size=\"small\"\r\n              @click=\"replyReview(review)\">\r\n              <i class=\"el-icon-chat-line-round\"></i>\r\n              回复\r\n            </el-button>\r\n            <el-button \r\n              type=\"text\" \r\n              size=\"small\"\r\n              class=\"danger-text\"\r\n              @click=\"deleteReview(review)\">\r\n              <i class=\"el-icon-delete\"></i>\r\n              删除\r\n            </el-button>\r\n          </template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 加载更多 -->\r\n    <div class=\"load-more\" v-if=\"hasMore\">\r\n      <el-button \r\n        @click=\"loadMore\"\r\n        :loading=\"loading\"\r\n        type=\"text\">\r\n        {{ loading ? '加载中...' : '加载更多' }}\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 空状态 -->\r\n    <div class=\"empty-state\" v-if=\"!loading && displayReviews.length === 0\">\r\n      <el-empty description=\"暂无评价\">\r\n        <el-button type=\"primary\" @click=\"$emit('add-review')\" v-if=\"showAddButton\">\r\n          写评价\r\n        </el-button>\r\n      </el-empty>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport StarRating from './StarRating.vue'\r\n\r\nexport default {\r\n  name: 'FoodReviewList',\r\n  components: {\r\n    StarRating\r\n  },\r\n  props: {\r\n    // 菜品ID\r\n    foodId: {\r\n      type: Number,\r\n      required: true\r\n    },\r\n    // 是否显示统计信息\r\n    showStats: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 是否显示筛选器\r\n    showFilters: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 是否显示管理员操作\r\n    showAdminActions: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 是否显示添加评价按钮\r\n    showAddButton: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    // 每页数量\r\n    pageSize: {\r\n      type: Number,\r\n      default: 10\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      reviews: [],\r\n      stats: null,\r\n      loading: false,\r\n      hasMore: true,\r\n      currentPage: 1,\r\n      currentFilter: {\r\n        star: 0, // 0表示全部\r\n        sortBy: 'time' // time, rating, helpful\r\n      },\r\n      ratingTexts: ['很差', '较差', '一般', '较好', '很好']\r\n    }\r\n  },\r\n  computed: {\r\n    displayReviews() {\r\n      let filtered = [...this.reviews]\r\n      \r\n      // 按星级筛选\r\n      if (this.currentFilter.star > 0) {\r\n        filtered = filtered.filter(review => review.rating === this.currentFilter.star)\r\n      }\r\n      \r\n      // 排序\r\n      switch (this.currentFilter.sortBy) {\r\n        case 'rating':\r\n          filtered.sort((a, b) => b.rating - a.rating)\r\n          break\r\n        case 'helpful':\r\n          filtered.sort((a, b) => (b.helpfulCount || 0) - (a.helpfulCount || 0))\r\n          break\r\n        default: // time\r\n          filtered.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))\r\n      }\r\n      \r\n      return filtered\r\n    }\r\n  },\r\n  created() {\r\n    this.loadReviews()\r\n    this.loadStats()\r\n  },\r\n  methods: {\r\n    // 加载评价列表\r\n    async loadReviews(reset = true) {\r\n      if (this.loading) return\r\n      \r\n      this.loading = true\r\n      \r\n      try {\r\n        const params = {\r\n          pageNum: reset ? 1 : this.currentPage + 1,\r\n          pageSize: this.pageSize\r\n        }\r\n        \r\n        const response = await this.$request.get(`/foodReview/food/${this.foodId}`, { params })\r\n        \r\n        if (response.code === '200') {\r\n          const newReviews = response.data?.list || []\r\n          \r\n          if (reset) {\r\n            this.reviews = newReviews\r\n            this.currentPage = 1\r\n          } else {\r\n            this.reviews.push(...newReviews)\r\n            this.currentPage++\r\n          }\r\n          \r\n          this.hasMore = newReviews.length === this.pageSize\r\n          \r\n          this.$emit('reviews-loaded', {\r\n            reviews: this.reviews,\r\n            total: response.data?.total || 0\r\n          })\r\n        } else {\r\n          this.$message.error(response.msg || '加载评价失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载评价失败:', error)\r\n        this.$message.error('加载评价失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 加载评价统计\r\n    async loadStats() {\r\n      try {\r\n        const response = await this.$request.get(`/foodReview/stats/${this.foodId}`)\r\n        \r\n        if (response.code === '200') {\r\n          this.stats = response.data\r\n        }\r\n      } catch (error) {\r\n        console.error('加载评价统计失败:', error)\r\n      }\r\n    },\r\n    \r\n    // 加载更多\r\n    loadMore() {\r\n      this.loadReviews(false)\r\n    },\r\n    \r\n    // 按星级筛选\r\n    filterByStar(star) {\r\n      this.currentFilter.star = star\r\n      this.$emit('filter-change', this.currentFilter)\r\n    },\r\n    \r\n    // 排序改变\r\n    handleSortChange() {\r\n      this.$emit('sort-change', this.currentFilter)\r\n    },\r\n    \r\n    // 切换有用性\r\n    async toggleHelpful(review) {\r\n      try {\r\n        const response = await this.$request.post('/foodReview/helpful', {\r\n          reviewId: review.id\r\n        })\r\n        \r\n        if (response.code === '200') {\r\n          // 更新本地数据\r\n          const index = this.reviews.findIndex(r => r.id === review.id)\r\n          if (index > -1) {\r\n            this.$set(this.reviews[index], 'helpfulCount', response.data.helpfulCount)\r\n            this.$set(this.reviews[index], 'isHelpful', response.data.isHelpful)\r\n          }\r\n        } else {\r\n          this.$message.error(response.msg || '操作失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('操作失败:', error)\r\n        this.$message.error('操作失败')\r\n      }\r\n    },\r\n    \r\n    // 回复评价（管理员）\r\n    replyReview(review) {\r\n      this.$emit('reply-review', review)\r\n    },\r\n    \r\n    // 删除评价（管理员）\r\n    deleteReview(review) {\r\n      this.$confirm('确定要删除这条评价吗？', '确认删除', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$emit('delete-review', review)\r\n      })\r\n    },\r\n    \r\n    // 预览图片\r\n    previewImage(image) {\r\n      // Element UI的图片组件会自动处理预览\r\n    },\r\n    \r\n    // 获取图片列表\r\n    getImageList(images) {\r\n      if (!images) return []\r\n      return images.split(',').filter(img => img.trim())\r\n    },\r\n    \r\n    // 获取评分文字\r\n    getRatingText(rating) {\r\n      if (rating === 0) return '暂无评分'\r\n      const index = Math.ceil(rating) - 1\r\n      return this.ratingTexts[index] || `${rating}星`\r\n    },\r\n    \r\n    // 获取星级百分比\r\n    getStarPercentage(star) {\r\n      if (!this.stats || !this.stats.totalReviews) return 0\r\n      const count = this.getStarCount(star)\r\n      return Math.round((count / this.stats.totalReviews) * 100)\r\n    },\r\n    \r\n    // 获取星级数量\r\n    getStarCount(star) {\r\n      if (!this.stats) return 0\r\n      const countMap = {\r\n        1: this.stats.oneStar || 0,\r\n        2: this.stats.twoStar || 0,\r\n        3: this.stats.threeStar || 0,\r\n        4: this.stats.fourStar || 0,\r\n        5: this.stats.fiveStar || 0\r\n      }\r\n      return countMap[star] || 0\r\n    },\r\n    \r\n    // 格式化时间\r\n    formatTime(time) {\r\n      if (!time) return ''\r\n      \r\n      const date = new Date(time)\r\n      const now = new Date()\r\n      const diff = now - date\r\n      \r\n      // 小于1分钟\r\n      if (diff < 60 * 1000) {\r\n        return '刚刚'\r\n      }\r\n      \r\n      // 小于1小时\r\n      if (diff < 60 * 60 * 1000) {\r\n        return `${Math.floor(diff / (60 * 1000))}分钟前`\r\n      }\r\n      \r\n      // 小于1天\r\n      if (diff < 24 * 60 * 60 * 1000) {\r\n        return `${Math.floor(diff / (60 * 60 * 1000))}小时前`\r\n      }\r\n      \r\n      // 小于7天\r\n      if (diff < 7 * 24 * 60 * 60 * 1000) {\r\n        return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`\r\n      }\r\n      \r\n      // 超过7天显示具体日期\r\n      return date.toLocaleDateString()\r\n    },\r\n    \r\n    // 刷新评价列表\r\n    refresh() {\r\n      this.loadReviews(true)\r\n      this.loadStats()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.food-review-list {\r\n  width: 100%;\r\n}\r\n\r\n/* 评价统计 */\r\n.review-stats {\r\n  background: #f8f9fa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.stats-overview {\r\n  display: flex;\r\n  gap: 30px;\r\n}\r\n\r\n.avg-rating {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  min-width: 120px;\r\n}\r\n\r\n.rating-number {\r\n  font-size: 36px;\r\n  font-weight: bold;\r\n  color: #F7BA2A;\r\n  line-height: 1;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.rating-text {\r\n  font-size: 14px;\r\n  color: #666;\r\n  margin-top: 8px;\r\n}\r\n\r\n.stats-detail {\r\n  flex: 1;\r\n}\r\n\r\n.total-reviews {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 15px;\r\n  color: #333;\r\n}\r\n\r\n.rating-distribution {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.rating-bar {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n  cursor: pointer;\r\n  padding: 2px;\r\n  border-radius: 4px;\r\n  transition: background-color 0.2s;\r\n}\r\n\r\n.rating-bar:hover {\r\n  background-color: rgba(64, 158, 255, 0.1);\r\n}\r\n\r\n.star-label {\r\n  width: 30px;\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.bar-container {\r\n  flex: 1;\r\n  height: 8px;\r\n  background-color: #e4e7ed;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n.bar-fill {\r\n  height: 100%;\r\n  background-color: #F7BA2A;\r\n  transition: width 0.3s;\r\n}\r\n\r\n.star-count {\r\n  width: 30px;\r\n  text-align: right;\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n/* 筛选器 */\r\n.review-filters {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  padding: 15px;\r\n  background: white;\r\n  border-radius: 8px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.filter-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.filter-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n/* 评价列表 */\r\n.review-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.review-item {\r\n  background: white;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #f0f0f0;\r\n}\r\n\r\n.review-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.user-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.user-detail {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n}\r\n\r\n.user-name {\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.review-time {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.review-content {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.review-text {\r\n  font-size: 14px;\r\n  line-height: 1.6;\r\n  color: #333;\r\n  margin-bottom: 12px;\r\n  word-break: break-word;\r\n}\r\n\r\n.review-images {\r\n  display: flex;\r\n  gap: 8px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.image-item {\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n  cursor: pointer;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.image-item:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.review-image {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* 商家回复 */\r\n.review-reply {\r\n  background: #f8f9fa;\r\n  border-radius: 6px;\r\n  padding: 12px;\r\n  margin-bottom: 15px;\r\n  border-left: 3px solid #409eff;\r\n}\r\n\r\n.reply-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  margin-bottom: 8px;\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.reply-label {\r\n  font-weight: bold;\r\n  color: #409eff;\r\n}\r\n\r\n.reply-content {\r\n  font-size: 14px;\r\n  color: #333;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 操作按钮 */\r\n.review-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  padding-top: 15px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.review-actions .el-button--text {\r\n  color: #666;\r\n  font-size: 12px;\r\n}\r\n\r\n.review-actions .el-button--text.is-active {\r\n  color: #409eff;\r\n}\r\n\r\n.review-actions .danger-text {\r\n  color: #f56c6c;\r\n}\r\n\r\n/* 加载更多 */\r\n.load-more {\r\n  text-align: center;\r\n  margin-top: 30px;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  padding: 40px;\r\n  text-align: center;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .stats-overview {\r\n    flex-direction: column;\r\n    gap: 20px;\r\n  }\r\n  \r\n  .review-filters {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n    align-items: stretch;\r\n  }\r\n  \r\n  .filter-left,\r\n  .filter-right {\r\n    justify-content: center;\r\n  }\r\n  \r\n  .review-item {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .review-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 10px;\r\n  }\r\n  \r\n  .review-actions {\r\n    flex-wrap: wrap;\r\n    gap: 10px;\r\n  }\r\n}\r\n</style> "], "mappings": ";;;AA4LA,OAAAA,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;EACAG,KAAA;IACA;IACAC,MAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACA;IACAC,SAAA;MACAH,IAAA,EAAAI,OAAA;MACAC,OAAA;IACA;IACA;IACAC,WAAA;MACAN,IAAA,EAAAI,OAAA;MACAC,OAAA;IACA;IACA;IACAE,gBAAA;MACAP,IAAA,EAAAI,OAAA;MACAC,OAAA;IACA;IACA;IACAG,aAAA;MACAR,IAAA,EAAAI,OAAA;MACAC,OAAA;IACA;IACA;IACAI,QAAA;MACAT,IAAA,EAAAC,MAAA;MACAI,OAAA;IACA;EACA;EACAK,KAAA;IACA;MACAC,OAAA;MACAC,KAAA;MACAC,OAAA;MACAC,OAAA;MACAC,WAAA;MACAC,aAAA;QACAC,IAAA;QAAA;QACAC,MAAA;MACA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACAC,eAAA;MACA,IAAAC,QAAA,YAAAX,OAAA;;MAEA;MACA,SAAAK,aAAA,CAAAC,IAAA;QACAK,QAAA,GAAAA,QAAA,CAAAC,MAAA,CAAAC,MAAA,IAAAA,MAAA,CAAAC,MAAA,UAAAT,aAAA,CAAAC,IAAA;MACA;;MAEA;MACA,aAAAD,aAAA,CAAAE,MAAA;QACA;UACAI,QAAA,CAAAI,IAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAA,CAAA,CAAAH,MAAA,GAAAE,CAAA,CAAAF,MAAA;UACA;QACA;UACAH,QAAA,CAAAI,IAAA,EAAAC,CAAA,EAAAC,CAAA,MAAAA,CAAA,CAAAC,YAAA,UAAAF,CAAA,CAAAE,YAAA;UACA;QACA;UAAA;UACAP,QAAA,CAAAI,IAAA,EAAAC,CAAA,EAAAC,CAAA,SAAAE,IAAA,CAAAF,CAAA,CAAAG,UAAA,QAAAD,IAAA,CAAAH,CAAA,CAAAI,UAAA;MACA;MAEA,OAAAT,QAAA;IACA;EACA;EACAU,QAAA;IACA,KAAAC,WAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACA;IACA,MAAAF,YAAAG,KAAA;MACA,SAAAvB,OAAA;MAEA,KAAAA,OAAA;MAEA;QACA,MAAAwB,MAAA;UACAC,OAAA,EAAAF,KAAA,YAAArB,WAAA;UACAN,QAAA,OAAAA;QACA;QAEA,MAAA8B,QAAA,cAAAC,QAAA,CAAAC,GAAA,0BAAA1C,MAAA;UAAAsC;QAAA;QAEA,IAAAE,QAAA,CAAAG,IAAA;UACA,MAAAC,UAAA,GAAAJ,QAAA,CAAA7B,IAAA,EAAAkC,IAAA;UAEA,IAAAR,KAAA;YACA,KAAAzB,OAAA,GAAAgC,UAAA;YACA,KAAA5B,WAAA;UACA;YACA,KAAAJ,OAAA,CAAAkC,IAAA,IAAAF,UAAA;YACA,KAAA5B,WAAA;UACA;UAEA,KAAAD,OAAA,GAAA6B,UAAA,CAAAG,MAAA,UAAArC,QAAA;UAEA,KAAAsC,KAAA;YACApC,OAAA,OAAAA,OAAA;YACAqC,KAAA,EAAAT,QAAA,CAAA7B,IAAA,EAAAsC,KAAA;UACA;QACA;UACA,KAAAC,QAAA,CAAAC,KAAA,CAAAX,QAAA,CAAAY,GAAA;QACA;MACA,SAAAD,KAAA;QACAE,OAAA,CAAAF,KAAA,YAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA;MACA;QACA,KAAArC,OAAA;MACA;IACA;IAEA;IACA,MAAAqB,UAAA;MACA;QACA,MAAAK,QAAA,cAAAC,QAAA,CAAAC,GAAA,2BAAA1C,MAAA;QAEA,IAAAwC,QAAA,CAAAG,IAAA;UACA,KAAA9B,KAAA,GAAA2B,QAAA,CAAA7B,IAAA;QACA;MACA,SAAAwC,KAAA;QACAE,OAAA,CAAAF,KAAA,cAAAA,KAAA;MACA;IACA;IAEA;IACAG,SAAA;MACA,KAAApB,WAAA;IACA;IAEA;IACAqB,aAAArC,IAAA;MACA,KAAAD,aAAA,CAAAC,IAAA,GAAAA,IAAA;MACA,KAAA8B,KAAA,uBAAA/B,aAAA;IACA;IAEA;IACAuC,iBAAA;MACA,KAAAR,KAAA,qBAAA/B,aAAA;IACA;IAEA;IACA,MAAAwC,cAAAhC,MAAA;MACA;QACA,MAAAe,QAAA,cAAAC,QAAA,CAAAiB,IAAA;UACAC,QAAA,EAAAlC,MAAA,CAAAmC;QACA;QAEA,IAAApB,QAAA,CAAAG,IAAA;UACA;UACA,MAAAkB,KAAA,QAAAjD,OAAA,CAAAkD,SAAA,CAAAC,CAAA,IAAAA,CAAA,CAAAH,EAAA,KAAAnC,MAAA,CAAAmC,EAAA;UACA,IAAAC,KAAA;YACA,KAAAG,IAAA,MAAApD,OAAA,CAAAiD,KAAA,mBAAArB,QAAA,CAAA7B,IAAA,CAAAmB,YAAA;YACA,KAAAkC,IAAA,MAAApD,OAAA,CAAAiD,KAAA,gBAAArB,QAAA,CAAA7B,IAAA,CAAAsD,SAAA;UACA;QACA;UACA,KAAAf,QAAA,CAAAC,KAAA,CAAAX,QAAA,CAAAY,GAAA;QACA;MACA,SAAAD,KAAA;QACAE,OAAA,CAAAF,KAAA,UAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAe,YAAAzC,MAAA;MACA,KAAAuB,KAAA,iBAAAvB,MAAA;IACA;IAEA;IACA0C,aAAA1C,MAAA;MACA,KAAA2C,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACArE,IAAA;MACA,GAAAsE,IAAA;QACA,KAAAvB,KAAA,kBAAAvB,MAAA;MACA;IACA;IAEA;IACA+C,aAAAC,KAAA;MACA;IAAA,CACA;IAEA;IACAC,aAAAC,MAAA;MACA,KAAAA,MAAA;MACA,OAAAA,MAAA,CAAAC,KAAA,MAAApD,MAAA,CAAAqD,GAAA,IAAAA,GAAA,CAAAC,IAAA;IACA;IAEA;IACAC,cAAArD,MAAA;MACA,IAAAA,MAAA;MACA,MAAAmC,KAAA,GAAAmB,IAAA,CAAAC,IAAA,CAAAvD,MAAA;MACA,YAAAN,WAAA,CAAAyC,KAAA,QAAAnC,MAAA;IACA;IAEA;IACAwD,kBAAAhE,IAAA;MACA,UAAAL,KAAA,UAAAA,KAAA,CAAAsE,YAAA;MACA,MAAAC,KAAA,QAAAC,YAAA,CAAAnE,IAAA;MACA,OAAA8D,IAAA,CAAAM,KAAA,CAAAF,KAAA,QAAAvE,KAAA,CAAAsE,YAAA;IACA;IAEA;IACAE,aAAAnE,IAAA;MACA,UAAAL,KAAA;MACA,MAAA0E,QAAA;QACA,QAAA1E,KAAA,CAAA2E,OAAA;QACA,QAAA3E,KAAA,CAAA4E,OAAA;QACA,QAAA5E,KAAA,CAAA6E,SAAA;QACA,QAAA7E,KAAA,CAAA8E,QAAA;QACA,QAAA9E,KAAA,CAAA+E,QAAA;MACA;MACA,OAAAL,QAAA,CAAArE,IAAA;IACA;IAEA;IACA2E,WAAAC,IAAA;MACA,KAAAA,IAAA;MAEA,MAAAC,IAAA,OAAAhE,IAAA,CAAA+D,IAAA;MACA,MAAAE,GAAA,OAAAjE,IAAA;MACA,MAAAkE,IAAA,GAAAD,GAAA,GAAAD,IAAA;;MAEA;MACA,IAAAE,IAAA;QACA;MACA;;MAEA;MACA,IAAAA,IAAA;QACA,UAAAjB,IAAA,CAAAkB,KAAA,CAAAD,IAAA;MACA;;MAEA;MACA,IAAAA,IAAA;QACA,UAAAjB,IAAA,CAAAkB,KAAA,CAAAD,IAAA;MACA;;MAEA;MACA,IAAAA,IAAA;QACA,UAAAjB,IAAA,CAAAkB,KAAA,CAAAD,IAAA;MACA;;MAEA;MACA,OAAAF,IAAA,CAAAI,kBAAA;IACA;IAEA;IACAC,QAAA;MACA,KAAAlE,WAAA;MACA,KAAAC,SAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}