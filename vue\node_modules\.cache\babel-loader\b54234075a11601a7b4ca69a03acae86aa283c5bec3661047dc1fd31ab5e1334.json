{"ast": null, "code": "import \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.for-each.js\";\nimport \"core-js/modules/esnext.iterator.map.js\";\nimport * as echarts from 'echarts';\nexport default {\n  name: 'Home',\n  data() {\n    return {\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      overviewData: {},\n      charts: {\n        statusPie: null,\n        revenue: null,\n        popularFoods: null,\n        category: null,\n        userReg: null,\n        userActivity: null\n      }\n    };\n  },\n  mounted() {\n    this.initCharts();\n    this.loadAllData();\n  },\n  beforeDestroy() {\n    Object.values(this.charts).forEach(chart => {\n      if (chart) chart.dispose();\n    });\n    window.removeEventListener('resize', this.resizeCharts);\n  },\n  methods: {\n    initCharts() {\n      this.charts.statusPie = echarts.init(this.$refs.statusPieChart);\n      this.charts.revenue = echarts.init(this.$refs.revenueChart);\n      this.charts.popularFoods = echarts.init(this.$refs.popularFoodsChart);\n      this.charts.category = echarts.init(this.$refs.categoryChart);\n      this.charts.userReg = echarts.init(this.$refs.userRegChart);\n      this.charts.tableUsage = echarts.init(this.$refs.tableUsageChart);\n      this.charts.complaint = echarts.init(this.$refs.complaintChart);\n      this.charts.userActivity = echarts.init(this.$refs.userActivityChart);\n      window.addEventListener('resize', this.resizeCharts);\n    },\n    resizeCharts() {\n      Object.values(this.charts).forEach(chart => {\n        chart && chart.resize();\n      });\n    },\n    async loadAllData() {\n      try {\n        // 加载综合数据概览\n        await this.loadOverviewData();\n\n        // 并行加载所有图表数据\n        await Promise.all([this.loadOrderStatusData(), this.loadRevenueData(), this.loadPopularFoodsData(), this.loadCategoryData(), this.loadUserRegistrationData(), this.loadTableUsageData(), this.loadComplaintData(), this.loadUserActivityData()]);\n      } catch (error) {\n        console.error('加载数据失败:', error);\n        this.$message.error('数据加载失败，请刷新页面重试');\n      }\n    },\n    async loadOverviewData() {\n      const res = await this.$request.get('/dingdan/stats/overview');\n      if (res.code === '200') {\n        this.overviewData = res.data;\n      }\n    },\n    async loadOrderStatusData() {\n      const res = await this.$request.get('/dingdan/stats/status');\n      if (res.code === '200') {\n        this.renderStatusPieChart(res.data);\n      }\n    },\n    async loadRevenueData() {\n      const currentYear = new Date().getFullYear();\n      const res = await this.$request.get('/dingdan/stats/revenue', {\n        params: {\n          year: currentYear\n        }\n      });\n      if (res.code === '200') {\n        this.renderRevenueChart(res.data);\n      }\n    },\n    async loadPopularFoodsData() {\n      const res = await this.$request.get('/dingdan/stats/popularFoods', {\n        params: {\n          limit: 10\n        }\n      });\n      if (res.code === '200') {\n        this.renderPopularFoodsChart(res.data);\n      }\n    },\n    async loadCategoryData() {\n      const res = await this.$request.get('/dingdan/stats/categoryRevenue');\n      if (res.code === '200') {\n        this.renderCategoryChart(res.data);\n      }\n    },\n    async loadUserRegistrationData() {\n      const currentYear = new Date().getFullYear();\n      const res = await this.$request.get('/dingdan/stats/userRegistration', {\n        params: {\n          year: currentYear\n        }\n      });\n      if (res.code === '200') {\n        this.renderUserRegChart(res.data);\n      }\n    },\n    async loadTableUsageData() {\n      const res = await this.$request.get('/dingdan/stats/tableUsage');\n      if (res.code === '200') {\n        this.renderTableUsageChart(res.data);\n      }\n    },\n    async loadComplaintData() {\n      const res = await this.$request.get('/dingdan/stats/complaints');\n      if (res.code === '200') {\n        this.renderComplaintChart(res.data);\n      }\n    },\n    async loadUserActivityData() {\n      const res = await this.$request.get('/dingdan/stats/userActivity', {\n        params: {\n          days: 30\n        }\n      });\n      if (res.code === '200') {\n        this.renderUserActivityChart(res.data);\n      }\n    },\n    renderStatusPieChart(data) {\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          right: 10,\n          top: 'center',\n          data: data.names\n        },\n        series: [{\n          name: '订单状态',\n          type: 'pie',\n          radius: '65%',\n          center: ['40%', '50%'],\n          data: data.values,\n          emphasis: {\n            itemStyle: {\n              shadowBlur: 10,\n              shadowOffsetX: 0,\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          itemStyle: {\n            borderRadius: 5,\n            borderColor: '#fff',\n            borderWidth: 2\n          }\n        }]\n      };\n      this.charts.statusPie.setOption(option);\n    },\n    renderRevenueChart(data) {\n      const option = {\n        tooltip: {\n          trigger: 'axis',\n          formatter: '月份: {b}<br/>收入: ¥{c}'\n        },\n        grid: {\n          left: '3%',\n          right: '4%',\n          bottom: '3%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          data: data.months,\n          axisLabel: {\n            interval: 0\n          }\n        },\n        yAxis: {\n          type: 'value',\n          name: '收入 (元)',\n          axisLabel: {\n            formatter: '¥{value}'\n          }\n        },\n        series: [{\n          name: '收入',\n          type: 'bar',\n          data: data.revenues,\n          itemStyle: {\n            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\n              offset: 0,\n              color: '#83bff6'\n            }, {\n              offset: 0.5,\n              color: '#188df0'\n            }, {\n              offset: 1,\n              color: '#188df0'\n            }])\n          },\n          emphasis: {\n            itemStyle: {\n              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\n                offset: 0,\n                color: '#2378f7'\n              }, {\n                offset: 0.7,\n                color: '#2378f7'\n              }, {\n                offset: 1,\n                color: '#83bff6'\n              }])\n            }\n          }\n        }]\n      };\n      this.charts.revenue.setOption(option);\n    },\n    renderPopularFoodsChart(data) {\n      const names = data.map(item => item.name);\n      const sales = data.map(item => item.sales);\n      const option = {\n        tooltip: {\n          trigger: 'axis',\n          axisPointer: {\n            type: 'shadow'\n          },\n          formatter: function (params) {\n            const item = data[params[0].dataIndex];\n            return `菜品: ${item.name}<br/>销量: ${item.sales}<br/>收入: ¥${item.revenue}<br/>评分: ${item.avgRating.toFixed(1)}`;\n          }\n        },\n        grid: {\n          left: '3%',\n          right: '4%',\n          bottom: '3%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'value',\n          name: '销量'\n        },\n        yAxis: {\n          type: 'category',\n          data: names,\n          axisLabel: {\n            interval: 0,\n            formatter: function (value) {\n              return value.length > 6 ? value.substring(0, 6) + '...' : value;\n            }\n          }\n        },\n        series: [{\n          name: '销量',\n          type: 'bar',\n          data: sales,\n          itemStyle: {\n            color: function (params) {\n              const colors = ['#ff7f50', '#87ceeb', '#da70d6', '#32cd32', '#6495ed', '#ff69b4', '#ba55d3', '#cd5c5c', '#ffa500', '#40e0d0'];\n              return colors[params.dataIndex % colors.length];\n            }\n          }\n        }]\n      };\n      this.charts.popularFoods.setOption(option);\n    },\n    renderCategoryChart(data) {\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: ¥{c} ({d}%)'\n        },\n        legend: {\n          bottom: '5%',\n          data: data.names\n        },\n        series: [{\n          name: '分类销售',\n          type: 'pie',\n          radius: ['40%', '70%'],\n          center: ['50%', '45%'],\n          avoidLabelOverlap: false,\n          itemStyle: {\n            borderRadius: 10,\n            borderColor: '#fff',\n            borderWidth: 2\n          },\n          label: {\n            show: false,\n            position: 'center'\n          },\n          emphasis: {\n            label: {\n              show: true,\n              fontSize: '18',\n              fontWeight: 'bold'\n            }\n          },\n          labelLine: {\n            show: false\n          },\n          data: data.values\n        }]\n      };\n      this.charts.category.setOption(option);\n    },\n    renderUserRegChart(data) {\n      const option = {\n        tooltip: {\n          trigger: 'axis'\n        },\n        grid: {\n          left: '3%',\n          right: '4%',\n          bottom: '3%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          boundaryGap: false,\n          data: data.months\n        },\n        yAxis: {\n          type: 'value',\n          name: '注册用户数'\n        },\n        series: [{\n          name: '注册用户',\n          type: 'line',\n          data: data.counts,\n          smooth: true,\n          areaStyle: {\n            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{\n              offset: 0,\n              color: 'rgba(255, 158, 68, 0.8)'\n            }, {\n              offset: 1,\n              color: 'rgba(255, 158, 68, 0.1)'\n            }])\n          },\n          lineStyle: {\n            width: 3,\n            color: '#ff9e44'\n          },\n          symbolSize: 8,\n          symbol: 'circle'\n        }]\n      };\n      this.charts.userReg.setOption(option);\n    },\n    renderTableUsageChart(data) {\n      const names = data.map(item => item.name);\n      const usageRates = data.map(item => item.usageRate);\n      const option = {\n        tooltip: {\n          trigger: 'axis',\n          formatter: function (params) {\n            const item = data[params[0].dataIndex];\n            return `区域: ${item.name}<br/>总桌数: ${item.totalTables}<br/>使用中: ${item.usedTables}<br/>使用率: ${item.usageRate}%`;\n          }\n        },\n        grid: {\n          left: '3%',\n          right: '4%',\n          bottom: '3%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          data: names\n        },\n        yAxis: {\n          type: 'value',\n          name: '使用率 (%)',\n          max: 100\n        },\n        series: [{\n          name: '使用率',\n          type: 'bar',\n          data: usageRates,\n          itemStyle: {\n            color: function (params) {\n              const rate = params.value;\n              if (rate >= 80) return '#ff4757';\n              if (rate >= 60) return '#ffa502';\n              if (rate >= 40) return '#2ed573';\n              return '#70a1ff';\n            }\n          }\n        }]\n      };\n      this.charts.tableUsage.setOption(option);\n    },\n    renderComplaintChart(data) {\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\n        },\n        legend: {\n          orient: 'vertical',\n          left: 'left',\n          data: data.names\n        },\n        series: [{\n          name: '投诉状态',\n          type: 'pie',\n          radius: '50%',\n          center: ['60%', '50%'],\n          data: data.values,\n          emphasis: {\n            itemStyle: {\n              shadowBlur: 10,\n              shadowOffsetX: 0,\n              shadowColor: 'rgba(0, 0, 0, 0.5)'\n            }\n          }\n        }]\n      };\n      this.charts.complaint.setOption(option);\n    },\n    renderUserActivityChart(data) {\n      const dates = data.map(item => item.date).reverse();\n      const activeUsers = data.map(item => item.activeUsers).reverse();\n      const orderCounts = data.map(item => item.orderCount).reverse();\n      const option = {\n        tooltip: {\n          trigger: 'axis',\n          formatter: function (params) {\n            return `日期: ${params[0].axisValue}<br/>活跃用户: ${params[0].value}<br/>订单数: ${params[1].value}`;\n          }\n        },\n        legend: {\n          data: ['活跃用户', '订单数']\n        },\n        grid: {\n          left: '3%',\n          right: '4%',\n          bottom: '3%',\n          containLabel: true\n        },\n        xAxis: {\n          type: 'category',\n          data: dates,\n          axisLabel: {\n            rotate: 45,\n            formatter: function (value) {\n              return value.substring(5); // 只显示月-日\n            }\n          }\n        },\n        yAxis: [{\n          type: 'value',\n          name: '活跃用户',\n          position: 'left'\n        }, {\n          type: 'value',\n          name: '订单数',\n          position: 'right'\n        }],\n        series: [{\n          name: '活跃用户',\n          type: 'line',\n          yAxisIndex: 0,\n          data: activeUsers,\n          smooth: true,\n          lineStyle: {\n            color: '#5470c6'\n          }\n        }, {\n          name: '订单数',\n          type: 'bar',\n          yAxisIndex: 1,\n          data: orderCounts,\n          itemStyle: {\n            color: '#91cc75'\n          }\n        }]\n      };\n      this.charts.userActivity.setOption(option);\n    }\n  }\n};", "map": {"version": 3, "names": ["echarts", "name", "data", "user", "JSON", "parse", "localStorage", "getItem", "overviewData", "charts", "status<PERSON>ie", "revenue", "popularFoods", "category", "userReg", "userActivity", "mounted", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadAllData", "<PERSON><PERSON><PERSON><PERSON>", "Object", "values", "for<PERSON>ach", "chart", "dispose", "window", "removeEventListener", "resi<PERSON><PERSON><PERSON><PERSON>", "methods", "init", "$refs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "revenueChart", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "categoryChart", "userRegChart", "tableUsage", "tableUsageChart", "complaint", "complaintChart", "userActivityChart", "addEventListener", "resize", "loadOverviewData", "Promise", "all", "loadOrderStatusData", "loadRevenueData", "loadPopularFoodsData", "loadCategoryData", "loadUserRegistrationData", "loadTableUsageData", "loadComplaintData", "loadUserActivityData", "error", "console", "$message", "res", "$request", "get", "code", "renderStatusPieChart", "currentYear", "Date", "getFullYear", "params", "year", "renderRevenueChart", "limit", "renderPopularFoodsChart", "renderCategoryChart", "renderUserRegChart", "renderTableUsageChart", "renderComplaintChart", "days", "renderUserActivityChart", "option", "tooltip", "trigger", "formatter", "legend", "orient", "right", "top", "names", "series", "type", "radius", "center", "emphasis", "itemStyle", "<PERSON><PERSON><PERSON><PERSON>", "shadowOffsetX", "shadowColor", "borderRadius", "borderColor", "borderWidth", "setOption", "grid", "left", "bottom", "containLabel", "xAxis", "months", "axisLabel", "interval", "yAxis", "revenues", "color", "graphic", "LinearGradient", "offset", "map", "item", "sales", "axisPointer", "dataIndex", "avgRating", "toFixed", "value", "length", "substring", "colors", "avoidLabelOverlap", "label", "show", "position", "fontSize", "fontWeight", "labelLine", "boundaryGap", "counts", "smooth", "areaStyle", "lineStyle", "width", "symbolSize", "symbol", "usageRates", "usageRate", "totalTables", "usedTables", "max", "rate", "dates", "date", "reverse", "activeUsers", "orderCounts", "orderCount", "axisValue", "rotate", "yAxisIndex"], "sources": ["src/views/manager/Home.vue"], "sourcesContent": ["<template>\r\n    <div>\r\n        <div class=\"card\" style=\"padding: 15px; margin-bottom: 20px\">\r\n            您好，{{ user?.name }}！欢迎使用餐厅管理系统\r\n        </div>\r\n\r\n        <!-- 综合数据概览卡片 -->\r\n        <div class=\"overview-cards\">\r\n            <div class=\"overview-card\">\r\n                <div class=\"overview-icon\">👥</div>\r\n                <div class=\"overview-content\">\r\n                    <div class=\"overview-title\">总用户数</div>\r\n                    <div class=\"overview-value\">{{ overviewData.totalUsers || 0 }}</div>\r\n                </div>\r\n            </div>\r\n            <div class=\"overview-card\">\r\n                <div class=\"overview-icon\">📋</div>\r\n                <div class=\"overview-content\">\r\n                    <div class=\"overview-title\">总订单数</div>\r\n                    <div class=\"overview-value\">{{ overviewData.totalOrders || 0 }}</div>\r\n                </div>\r\n            </div>\r\n            <div class=\"overview-card\">\r\n                <div class=\"overview-icon\">💰</div>\r\n                <div class=\"overview-content\">\r\n                    <div class=\"overview-title\">总收入</div>\r\n                    <div class=\"overview-value\">¥{{ (overviewData.totalRevenue || 0).toFixed(2) }}</div>\r\n                </div>\r\n            </div>\r\n            <div class=\"overview-card\">\r\n                <div class=\"overview-icon\">⭐</div>\r\n                <div class=\"overview-content\">\r\n                    <div class=\"overview-title\">平均评分</div>\r\n                    <div class=\"overview-value\">{{ (overviewData.avgRating || 0).toFixed(1) }}</div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 第一行：订单状态分布和收入统计 -->\r\n        <div class=\"chart-row\">\r\n            <div class=\"card chart-card\">\r\n                <div class=\"chart-title\">订单状态分布</div>\r\n                <div ref=\"statusPieChart\" class=\"chart-container\"></div>\r\n            </div>\r\n            <div class=\"card chart-card\">\r\n                <div class=\"chart-title\">月度收入统计</div>\r\n                <div ref=\"revenueChart\" class=\"chart-container\"></div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 第二行：菜品销量排行和分类销售占比 -->\r\n        <div class=\"chart-row\">\r\n            <div class=\"card chart-card\">\r\n                <div class=\"chart-title\">热门菜品销量排行</div>\r\n                <div ref=\"popularFoodsChart\" class=\"chart-container\"></div>\r\n            </div>\r\n            <div class=\"card chart-card\">\r\n                <div class=\"chart-title\">分类销售占比</div>\r\n                <div ref=\"categoryChart\" class=\"chart-container\"></div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 第三行：用户注册趋势和用户活跃度 -->\r\n        <div class=\"chart-row\">\r\n            <div class=\"card chart-card\">\r\n                <div class=\"chart-title\">用户注册趋势</div>\r\n                <div ref=\"userRegChart\" class=\"chart-container\"></div>\r\n            </div>\r\n            <div class=\"card chart-card\">\r\n                <div class=\"chart-title\">近30天用户活跃度</div>\r\n                <div ref=\"userActivityChart\" class=\"chart-container\"></div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts';\r\n\r\nexport default {\r\n    name: 'Home',\r\n    data() {\r\n        return {\r\n            user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\r\n            overviewData: {},\r\n            charts: {\r\n                statusPie: null,\r\n                revenue: null,\r\n                popularFoods: null,\r\n                category: null,\r\n                userReg: null,\r\n                userActivity: null\r\n            }\r\n        }\r\n    },\r\n    mounted() {\r\n        this.initCharts();\r\n        this.loadAllData();\r\n    },\r\n    beforeDestroy() {\r\n        Object.values(this.charts).forEach(chart => {\r\n            if (chart) chart.dispose();\r\n        });\r\n        window.removeEventListener('resize', this.resizeCharts);\r\n    },\r\n    methods: {\r\n        initCharts() {\r\n            this.charts.statusPie = echarts.init(this.$refs.statusPieChart);\r\n            this.charts.revenue = echarts.init(this.$refs.revenueChart);\r\n            this.charts.popularFoods = echarts.init(this.$refs.popularFoodsChart);\r\n            this.charts.category = echarts.init(this.$refs.categoryChart);\r\n            this.charts.userReg = echarts.init(this.$refs.userRegChart);\r\n            this.charts.tableUsage = echarts.init(this.$refs.tableUsageChart);\r\n            this.charts.complaint = echarts.init(this.$refs.complaintChart);\r\n            this.charts.userActivity = echarts.init(this.$refs.userActivityChart);\r\n            \r\n            window.addEventListener('resize', this.resizeCharts);\r\n        },\r\n        resizeCharts() {\r\n            Object.values(this.charts).forEach(chart => {\r\n                chart && chart.resize();\r\n            });\r\n        },\r\n        async loadAllData() {\r\n            try {\r\n                // 加载综合数据概览\r\n                await this.loadOverviewData();\r\n                \r\n                // 并行加载所有图表数据\r\n                await Promise.all([\r\n                    this.loadOrderStatusData(),\r\n                    this.loadRevenueData(),\r\n                    this.loadPopularFoodsData(),\r\n                    this.loadCategoryData(),\r\n                    this.loadUserRegistrationData(),\r\n                    this.loadTableUsageData(),\r\n                    this.loadComplaintData(),\r\n                    this.loadUserActivityData()\r\n                ]);\r\n            } catch (error) {\r\n                console.error('加载数据失败:', error);\r\n                this.$message.error('数据加载失败，请刷新页面重试');\r\n            }\r\n        },\r\n        async loadOverviewData() {\r\n            const res = await this.$request.get('/dingdan/stats/overview');\r\n            if (res.code === '200') {\r\n                this.overviewData = res.data;\r\n            }\r\n        },\r\n        async loadOrderStatusData() {\r\n            const res = await this.$request.get('/dingdan/stats/status');\r\n            if (res.code === '200') {\r\n                this.renderStatusPieChart(res.data);\r\n            }\r\n        },\r\n        async loadRevenueData() {\r\n            const currentYear = new Date().getFullYear();\r\n            const res = await this.$request.get('/dingdan/stats/revenue', {\r\n                params: { year: currentYear }\r\n            });\r\n            if (res.code === '200') {\r\n                this.renderRevenueChart(res.data);\r\n            }\r\n        },\r\n        async loadPopularFoodsData() {\r\n            const res = await this.$request.get('/dingdan/stats/popularFoods', {\r\n                params: { limit: 10 }\r\n            });\r\n            if (res.code === '200') {\r\n                this.renderPopularFoodsChart(res.data);\r\n            }\r\n        },\r\n        async loadCategoryData() {\r\n            const res = await this.$request.get('/dingdan/stats/categoryRevenue');\r\n            if (res.code === '200') {\r\n                this.renderCategoryChart(res.data);\r\n            }\r\n        },\r\n        async loadUserRegistrationData() {\r\n            const currentYear = new Date().getFullYear();\r\n            const res = await this.$request.get('/dingdan/stats/userRegistration', {\r\n                params: { year: currentYear }\r\n            });\r\n            if (res.code === '200') {\r\n                this.renderUserRegChart(res.data);\r\n            }\r\n        },\r\n        async loadTableUsageData() {\r\n            const res = await this.$request.get('/dingdan/stats/tableUsage');\r\n            if (res.code === '200') {\r\n                this.renderTableUsageChart(res.data);\r\n            }\r\n        },\r\n        async loadComplaintData() {\r\n            const res = await this.$request.get('/dingdan/stats/complaints');\r\n            if (res.code === '200') {\r\n                this.renderComplaintChart(res.data);\r\n            }\r\n        },\r\n        async loadUserActivityData() {\r\n            const res = await this.$request.get('/dingdan/stats/userActivity', {\r\n                params: { days: 30 }\r\n            });\r\n            if (res.code === '200') {\r\n                this.renderUserActivityChart(res.data);\r\n            }\r\n        },\r\n        renderStatusPieChart(data) {\r\n            const option = {\r\n                tooltip: {\r\n                    trigger: 'item',\r\n                    formatter: '{a} <br/>{b}: {c} ({d}%)'\r\n                },\r\n                legend: {\r\n                    orient: 'vertical',\r\n                    right: 10,\r\n                    top: 'center',\r\n                    data: data.names\r\n                },\r\n                series: [\r\n                    {\r\n                        name: '订单状态',\r\n                        type: 'pie',\r\n                        radius: '65%',\r\n                        center: ['40%', '50%'],\r\n                        data: data.values,\r\n                        emphasis: {\r\n                            itemStyle: {\r\n                                shadowBlur: 10,\r\n                                shadowOffsetX: 0,\r\n                                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                            }\r\n                        },\r\n                        itemStyle: {\r\n                            borderRadius: 5,\r\n                            borderColor: '#fff',\r\n                            borderWidth: 2\r\n                        }\r\n                    }\r\n                ]\r\n            };\r\n            this.charts.statusPie.setOption(option);\r\n        },\r\n        renderRevenueChart(data) {\r\n            const option = {\r\n                tooltip: {\r\n                    trigger: 'axis',\r\n                    formatter: '月份: {b}<br/>收入: ¥{c}'\r\n                },\r\n                grid: {\r\n                    left: '3%',\r\n                    right: '4%',\r\n                    bottom: '3%',\r\n                    containLabel: true\r\n                },\r\n                xAxis: {\r\n                    type: 'category',\r\n                    data: data.months,\r\n                    axisLabel: {\r\n                        interval: 0\r\n                    }\r\n                },\r\n                yAxis: {\r\n                    type: 'value',\r\n                    name: '收入 (元)',\r\n                    axisLabel: {\r\n                        formatter: '¥{value}'\r\n                    }\r\n                },\r\n                series: [\r\n                    {\r\n                        name: '收入',\r\n                        type: 'bar',\r\n                        data: data.revenues,\r\n                        itemStyle: {\r\n                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                                { offset: 0, color: '#83bff6' },\r\n                                { offset: 0.5, color: '#188df0' },\r\n                                { offset: 1, color: '#188df0' }\r\n                            ])\r\n                        },\r\n                        emphasis: {\r\n                            itemStyle: {\r\n                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                                    { offset: 0, color: '#2378f7' },\r\n                                    { offset: 0.7, color: '#2378f7' },\r\n                                    { offset: 1, color: '#83bff6' }\r\n                                ])\r\n                            }\r\n                        }\r\n                    }\r\n                ]\r\n            };\r\n            this.charts.revenue.setOption(option);\r\n        },\r\n        renderPopularFoodsChart(data) {\r\n            const names = data.map(item => item.name);\r\n            const sales = data.map(item => item.sales);\r\n            \r\n            const option = {\r\n                tooltip: {\r\n                    trigger: 'axis',\r\n                    axisPointer: {\r\n                        type: 'shadow'\r\n                    },\r\n                    formatter: function(params) {\r\n                        const item = data[params[0].dataIndex];\r\n                        return `菜品: ${item.name}<br/>销量: ${item.sales}<br/>收入: ¥${item.revenue}<br/>评分: ${item.avgRating.toFixed(1)}`;\r\n                    }\r\n                },\r\n                grid: {\r\n                    left: '3%',\r\n                    right: '4%',\r\n                    bottom: '3%',\r\n                    containLabel: true\r\n                },\r\n                xAxis: {\r\n                    type: 'value',\r\n                    name: '销量'\r\n                },\r\n                yAxis: {\r\n                    type: 'category',\r\n                    data: names,\r\n                    axisLabel: {\r\n                        interval: 0,\r\n                        formatter: function(value) {\r\n                            return value.length > 6 ? value.substring(0, 6) + '...' : value;\r\n                        }\r\n                    }\r\n                },\r\n                series: [\r\n                    {\r\n                        name: '销量',\r\n                        type: 'bar',\r\n                        data: sales,\r\n                        itemStyle: {\r\n                            color: function(params) {\r\n                                const colors = ['#ff7f50', '#87ceeb', '#da70d6', '#32cd32', '#6495ed', '#ff69b4', '#ba55d3', '#cd5c5c', '#ffa500', '#40e0d0'];\r\n                                return colors[params.dataIndex % colors.length];\r\n                            }\r\n                        }\r\n                    }\r\n                ]\r\n            };\r\n            this.charts.popularFoods.setOption(option);\r\n        },\r\n        renderCategoryChart(data) {\r\n            const option = {\r\n                tooltip: {\r\n                    trigger: 'item',\r\n                    formatter: '{a} <br/>{b}: ¥{c} ({d}%)'\r\n                },\r\n                legend: {\r\n                    bottom: '5%',\r\n                    data: data.names\r\n                },\r\n                series: [\r\n                    {\r\n                        name: '分类销售',\r\n                        type: 'pie',\r\n                        radius: ['40%', '70%'],\r\n                        center: ['50%', '45%'],\r\n                        avoidLabelOverlap: false,\r\n                        itemStyle: {\r\n                            borderRadius: 10,\r\n                            borderColor: '#fff',\r\n                            borderWidth: 2\r\n                        },\r\n                        label: {\r\n                            show: false,\r\n                            position: 'center'\r\n                        },\r\n                        emphasis: {\r\n                            label: {\r\n                                show: true,\r\n                                fontSize: '18',\r\n                                fontWeight: 'bold'\r\n                            }\r\n                        },\r\n                        labelLine: {\r\n                            show: false\r\n                        },\r\n                        data: data.values\r\n                    }\r\n                ]\r\n            };\r\n            this.charts.category.setOption(option);\r\n        },\r\n        renderUserRegChart(data) {\r\n            const option = {\r\n                tooltip: {\r\n                    trigger: 'axis'\r\n                },\r\n                grid: {\r\n                    left: '3%',\r\n                    right: '4%',\r\n                    bottom: '3%',\r\n                    containLabel: true\r\n                },\r\n                xAxis: {\r\n                    type: 'category',\r\n                    boundaryGap: false,\r\n                    data: data.months\r\n                },\r\n                yAxis: {\r\n                    type: 'value',\r\n                    name: '注册用户数'\r\n                },\r\n                series: [\r\n                    {\r\n                        name: '注册用户',\r\n                        type: 'line',\r\n                        data: data.counts,\r\n                        smooth: true,\r\n                        areaStyle: {\r\n                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                                { offset: 0, color: 'rgba(255, 158, 68, 0.8)' },\r\n                                { offset: 1, color: 'rgba(255, 158, 68, 0.1)' }\r\n                            ])\r\n                        },\r\n                        lineStyle: {\r\n                            width: 3,\r\n                            color: '#ff9e44'\r\n                        },\r\n                        symbolSize: 8,\r\n                        symbol: 'circle'\r\n                    }\r\n                ]\r\n            };\r\n            this.charts.userReg.setOption(option);\r\n        },\r\n        renderTableUsageChart(data) {\r\n            const names = data.map(item => item.name);\r\n            const usageRates = data.map(item => item.usageRate);\r\n            \r\n            const option = {\r\n                tooltip: {\r\n                    trigger: 'axis',\r\n                    formatter: function(params) {\r\n                        const item = data[params[0].dataIndex];\r\n                        return `区域: ${item.name}<br/>总桌数: ${item.totalTables}<br/>使用中: ${item.usedTables}<br/>使用率: ${item.usageRate}%`;\r\n                    }\r\n                },\r\n                grid: {\r\n                    left: '3%',\r\n                    right: '4%',\r\n                    bottom: '3%',\r\n                    containLabel: true\r\n                },\r\n                xAxis: {\r\n                    type: 'category',\r\n                    data: names\r\n                },\r\n                yAxis: {\r\n                    type: 'value',\r\n                    name: '使用率 (%)',\r\n                    max: 100\r\n                },\r\n                series: [\r\n                    {\r\n                        name: '使用率',\r\n                        type: 'bar',\r\n                        data: usageRates,\r\n                        itemStyle: {\r\n                            color: function(params) {\r\n                                const rate = params.value;\r\n                                if (rate >= 80) return '#ff4757';\r\n                                if (rate >= 60) return '#ffa502';\r\n                                if (rate >= 40) return '#2ed573';\r\n                                return '#70a1ff';\r\n                            }\r\n                        }\r\n                    }\r\n                ]\r\n            };\r\n            this.charts.tableUsage.setOption(option);\r\n        },\r\n        renderComplaintChart(data) {\r\n            const option = {\r\n                tooltip: {\r\n                    trigger: 'item',\r\n                    formatter: '{a} <br/>{b}: {c} ({d}%)'\r\n                },\r\n                legend: {\r\n                    orient: 'vertical',\r\n                    left: 'left',\r\n                    data: data.names\r\n                },\r\n                series: [\r\n                    {\r\n                        name: '投诉状态',\r\n                        type: 'pie',\r\n                        radius: '50%',\r\n                        center: ['60%', '50%'],\r\n                        data: data.values,\r\n                        emphasis: {\r\n                            itemStyle: {\r\n                                shadowBlur: 10,\r\n                                shadowOffsetX: 0,\r\n                                shadowColor: 'rgba(0, 0, 0, 0.5)'\r\n                            }\r\n                        }\r\n                    }\r\n                ]\r\n            };\r\n            this.charts.complaint.setOption(option);\r\n        },\r\n        renderUserActivityChart(data) {\r\n            const dates = data.map(item => item.date).reverse();\r\n            const activeUsers = data.map(item => item.activeUsers).reverse();\r\n            const orderCounts = data.map(item => item.orderCount).reverse();\r\n            \r\n            const option = {\r\n                tooltip: {\r\n                    trigger: 'axis',\r\n                    formatter: function(params) {\r\n                        return `日期: ${params[0].axisValue}<br/>活跃用户: ${params[0].value}<br/>订单数: ${params[1].value}`;\r\n                    }\r\n                },\r\n                legend: {\r\n                    data: ['活跃用户', '订单数']\r\n                },\r\n                grid: {\r\n                    left: '3%',\r\n                    right: '4%',\r\n                    bottom: '3%',\r\n                    containLabel: true\r\n                },\r\n                xAxis: {\r\n                    type: 'category',\r\n                    data: dates,\r\n                    axisLabel: {\r\n                        rotate: 45,\r\n                        formatter: function(value) {\r\n                            return value.substring(5); // 只显示月-日\r\n                        }\r\n                    }\r\n                },\r\n                yAxis: [\r\n                    {\r\n                        type: 'value',\r\n                        name: '活跃用户',\r\n                        position: 'left'\r\n                    },\r\n                    {\r\n                        type: 'value',\r\n                        name: '订单数',\r\n                        position: 'right'\r\n                    }\r\n                ],\r\n                series: [\r\n                    {\r\n                        name: '活跃用户',\r\n                        type: 'line',\r\n                        yAxisIndex: 0,\r\n                        data: activeUsers,\r\n                        smooth: true,\r\n                        lineStyle: {\r\n                            color: '#5470c6'\r\n                        }\r\n                    },\r\n                    {\r\n                        name: '订单数',\r\n                        type: 'bar',\r\n                        yAxisIndex: 1,\r\n                        data: orderCounts,\r\n                        itemStyle: {\r\n                            color: '#91cc75'\r\n                        }\r\n                    }\r\n                ]\r\n            };\r\n            this.charts.userActivity.setOption(option);\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.card {\r\n    background-color: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.overview-cards {\r\n    display: flex;\r\n    gap: 20px;\r\n    margin-bottom: 20px;\r\n    flex-wrap: wrap;\r\n}\r\n\r\n.overview-card {\r\n    flex: 1;\r\n    min-width: 200px;\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    padding: 20px;\r\n    color: #333;\r\n    display: flex;\r\n    align-items: center;\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n    transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n    border: 1px solid #f0f0f0;\r\n}\r\n\r\n.overview-card:hover {\r\n    transform: translateY(-2px);\r\n    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.overview-icon {\r\n    font-size: 40px;\r\n    margin-right: 15px;\r\n    opacity: 0.8;\r\n    width: 60px;\r\n    height: 60px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background: #f8f9fa;\r\n    border-radius: 50%;\r\n}\r\n\r\n.overview-content {\r\n    flex: 1;\r\n}\r\n\r\n.overview-title {\r\n    font-size: 14px;\r\n    opacity: 0.9;\r\n    margin-bottom: 5px;\r\n}\r\n\r\n.overview-value {\r\n    font-size: 24px;\r\n    font-weight: bold;\r\n    line-height: 1;\r\n}\r\n\r\n.chart-row {\r\n    display: flex;\r\n    margin: 10px 0;\r\n    gap: 20px;\r\n}\r\n\r\n.chart-card {\r\n    flex: 1;\r\n    padding: 20px;\r\n}\r\n\r\n.chart-title {\r\n    margin-bottom: 15px;\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    color: #333;\r\n}\r\n\r\n.chart-container {\r\n    height: 300px;\r\n    min-height: 300px;\r\n}\r\n\r\n/* 响应式布局 */\r\n@media (max-width: 1200px) {\r\n    .overview-cards {\r\n        flex-wrap: wrap;\r\n    }\r\n    \r\n    .overview-card {\r\n        min-width: calc(50% - 10px);\r\n    }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n    .chart-row {\r\n        flex-direction: column;\r\n    }\r\n\r\n    .chart-card {\r\n        margin-bottom: 20px;\r\n    }\r\n    \r\n    .overview-card {\r\n        min-width: 100%;\r\n    }\r\n    \r\n    .overview-cards {\r\n        flex-direction: column;\r\n    }\r\n}\r\n</style>"], "mappings": ";;;AA6EA,YAAAA,OAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MACAC,YAAA;MACAC,MAAA;QACAC,SAAA;QACAC,OAAA;QACAC,YAAA;QACAC,QAAA;QACAC,OAAA;QACAC,YAAA;MACA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,UAAA;IACA,KAAAC,WAAA;EACA;EACAC,cAAA;IACAC,MAAA,CAAAC,MAAA,MAAAZ,MAAA,EAAAa,OAAA,CAAAC,KAAA;MACA,IAAAA,KAAA,EAAAA,KAAA,CAAAC,OAAA;IACA;IACAC,MAAA,CAAAC,mBAAA,gBAAAC,YAAA;EACA;EACAC,OAAA;IACAX,WAAA;MACA,KAAAR,MAAA,CAAAC,SAAA,GAAAV,OAAA,CAAA6B,IAAA,MAAAC,KAAA,CAAAC,cAAA;MACA,KAAAtB,MAAA,CAAAE,OAAA,GAAAX,OAAA,CAAA6B,IAAA,MAAAC,KAAA,CAAAE,YAAA;MACA,KAAAvB,MAAA,CAAAG,YAAA,GAAAZ,OAAA,CAAA6B,IAAA,MAAAC,KAAA,CAAAG,iBAAA;MACA,KAAAxB,MAAA,CAAAI,QAAA,GAAAb,OAAA,CAAA6B,IAAA,MAAAC,KAAA,CAAAI,aAAA;MACA,KAAAzB,MAAA,CAAAK,OAAA,GAAAd,OAAA,CAAA6B,IAAA,MAAAC,KAAA,CAAAK,YAAA;MACA,KAAA1B,MAAA,CAAA2B,UAAA,GAAApC,OAAA,CAAA6B,IAAA,MAAAC,KAAA,CAAAO,eAAA;MACA,KAAA5B,MAAA,CAAA6B,SAAA,GAAAtC,OAAA,CAAA6B,IAAA,MAAAC,KAAA,CAAAS,cAAA;MACA,KAAA9B,MAAA,CAAAM,YAAA,GAAAf,OAAA,CAAA6B,IAAA,MAAAC,KAAA,CAAAU,iBAAA;MAEAf,MAAA,CAAAgB,gBAAA,gBAAAd,YAAA;IACA;IACAA,aAAA;MACAP,MAAA,CAAAC,MAAA,MAAAZ,MAAA,EAAAa,OAAA,CAAAC,KAAA;QACAA,KAAA,IAAAA,KAAA,CAAAmB,MAAA;MACA;IACA;IACA,MAAAxB,YAAA;MACA;QACA;QACA,WAAAyB,gBAAA;;QAEA;QACA,MAAAC,OAAA,CAAAC,GAAA,EACA,KAAAC,mBAAA,IACA,KAAAC,eAAA,IACA,KAAAC,oBAAA,IACA,KAAAC,gBAAA,IACA,KAAAC,wBAAA,IACA,KAAAC,kBAAA,IACA,KAAAC,iBAAA,IACA,KAAAC,oBAAA,GACA;MACA,SAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA;MACA;IACA;IACA,MAAAX,iBAAA;MACA,MAAAc,GAAA,cAAAC,QAAA,CAAAC,GAAA;MACA,IAAAF,GAAA,CAAAG,IAAA;QACA,KAAApD,YAAA,GAAAiD,GAAA,CAAAvD,IAAA;MACA;IACA;IACA,MAAA4C,oBAAA;MACA,MAAAW,GAAA,cAAAC,QAAA,CAAAC,GAAA;MACA,IAAAF,GAAA,CAAAG,IAAA;QACA,KAAAC,oBAAA,CAAAJ,GAAA,CAAAvD,IAAA;MACA;IACA;IACA,MAAA6C,gBAAA;MACA,MAAAe,WAAA,OAAAC,IAAA,GAAAC,WAAA;MACA,MAAAP,GAAA,cAAAC,QAAA,CAAAC,GAAA;QACAM,MAAA;UAAAC,IAAA,EAAAJ;QAAA;MACA;MACA,IAAAL,GAAA,CAAAG,IAAA;QACA,KAAAO,kBAAA,CAAAV,GAAA,CAAAvD,IAAA;MACA;IACA;IACA,MAAA8C,qBAAA;MACA,MAAAS,GAAA,cAAAC,QAAA,CAAAC,GAAA;QACAM,MAAA;UAAAG,KAAA;QAAA;MACA;MACA,IAAAX,GAAA,CAAAG,IAAA;QACA,KAAAS,uBAAA,CAAAZ,GAAA,CAAAvD,IAAA;MACA;IACA;IACA,MAAA+C,iBAAA;MACA,MAAAQ,GAAA,cAAAC,QAAA,CAAAC,GAAA;MACA,IAAAF,GAAA,CAAAG,IAAA;QACA,KAAAU,mBAAA,CAAAb,GAAA,CAAAvD,IAAA;MACA;IACA;IACA,MAAAgD,yBAAA;MACA,MAAAY,WAAA,OAAAC,IAAA,GAAAC,WAAA;MACA,MAAAP,GAAA,cAAAC,QAAA,CAAAC,GAAA;QACAM,MAAA;UAAAC,IAAA,EAAAJ;QAAA;MACA;MACA,IAAAL,GAAA,CAAAG,IAAA;QACA,KAAAW,kBAAA,CAAAd,GAAA,CAAAvD,IAAA;MACA;IACA;IACA,MAAAiD,mBAAA;MACA,MAAAM,GAAA,cAAAC,QAAA,CAAAC,GAAA;MACA,IAAAF,GAAA,CAAAG,IAAA;QACA,KAAAY,qBAAA,CAAAf,GAAA,CAAAvD,IAAA;MACA;IACA;IACA,MAAAkD,kBAAA;MACA,MAAAK,GAAA,cAAAC,QAAA,CAAAC,GAAA;MACA,IAAAF,GAAA,CAAAG,IAAA;QACA,KAAAa,oBAAA,CAAAhB,GAAA,CAAAvD,IAAA;MACA;IACA;IACA,MAAAmD,qBAAA;MACA,MAAAI,GAAA,cAAAC,QAAA,CAAAC,GAAA;QACAM,MAAA;UAAAS,IAAA;QAAA;MACA;MACA,IAAAjB,GAAA,CAAAG,IAAA;QACA,KAAAe,uBAAA,CAAAlB,GAAA,CAAAvD,IAAA;MACA;IACA;IACA2D,qBAAA3D,IAAA;MACA,MAAA0E,MAAA;QACAC,OAAA;UACAC,OAAA;UACAC,SAAA;QACA;QACAC,MAAA;UACAC,MAAA;UACAC,KAAA;UACAC,GAAA;UACAjF,IAAA,EAAAA,IAAA,CAAAkF;QACA;QACAC,MAAA,GACA;UACApF,IAAA;UACAqF,IAAA;UACAC,MAAA;UACAC,MAAA;UACAtF,IAAA,EAAAA,IAAA,CAAAmB,MAAA;UACAoE,QAAA;YACAC,SAAA;cACAC,UAAA;cACAC,aAAA;cACAC,WAAA;YACA;UACA;UACAH,SAAA;YACAI,YAAA;YACAC,WAAA;YACAC,WAAA;UACA;QACA;MAEA;MACA,KAAAvF,MAAA,CAAAC,SAAA,CAAAuF,SAAA,CAAArB,MAAA;IACA;IACAT,mBAAAjE,IAAA;MACA,MAAA0E,MAAA;QACAC,OAAA;UACAC,OAAA;UACAC,SAAA;QACA;QACAmB,IAAA;UACAC,IAAA;UACAjB,KAAA;UACAkB,MAAA;UACAC,YAAA;QACA;QACAC,KAAA;UACAhB,IAAA;UACApF,IAAA,EAAAA,IAAA,CAAAqG,MAAA;UACAC,SAAA;YACAC,QAAA;UACA;QACA;QACAC,KAAA;UACApB,IAAA;UACArF,IAAA;UACAuG,SAAA;YACAzB,SAAA;UACA;QACA;QACAM,MAAA,GACA;UACApF,IAAA;UACAqF,IAAA;UACApF,IAAA,EAAAA,IAAA,CAAAyG,QAAA;UACAjB,SAAA;YACAkB,KAAA,MAAA5G,OAAA,CAAA6G,OAAA,CAAAC,cAAA,cACA;cAAAC,MAAA;cAAAH,KAAA;YAAA,GACA;cAAAG,MAAA;cAAAH,KAAA;YAAA,GACA;cAAAG,MAAA;cAAAH,KAAA;YAAA,EACA;UACA;UACAnB,QAAA;YACAC,SAAA;cACAkB,KAAA,MAAA5G,OAAA,CAAA6G,OAAA,CAAAC,cAAA,cACA;gBAAAC,MAAA;gBAAAH,KAAA;cAAA,GACA;gBAAAG,MAAA;gBAAAH,KAAA;cAAA,GACA;gBAAAG,MAAA;gBAAAH,KAAA;cAAA,EACA;YACA;UACA;QACA;MAEA;MACA,KAAAnG,MAAA,CAAAE,OAAA,CAAAsF,SAAA,CAAArB,MAAA;IACA;IACAP,wBAAAnE,IAAA;MACA,MAAAkF,KAAA,GAAAlF,IAAA,CAAA8G,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAhH,IAAA;MACA,MAAAiH,KAAA,GAAAhH,IAAA,CAAA8G,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAC,KAAA;MAEA,MAAAtC,MAAA;QACAC,OAAA;UACAC,OAAA;UACAqC,WAAA;YACA7B,IAAA;UACA;UACAP,SAAA,WAAAA,CAAAd,MAAA;YACA,MAAAgD,IAAA,GAAA/G,IAAA,CAAA+D,MAAA,IAAAmD,SAAA;YACA,cAAAH,IAAA,CAAAhH,IAAA,YAAAgH,IAAA,CAAAC,KAAA,aAAAD,IAAA,CAAAtG,OAAA,YAAAsG,IAAA,CAAAI,SAAA,CAAAC,OAAA;UACA;QACA;QACApB,IAAA;UACAC,IAAA;UACAjB,KAAA;UACAkB,MAAA;UACAC,YAAA;QACA;QACAC,KAAA;UACAhB,IAAA;UACArF,IAAA;QACA;QACAyG,KAAA;UACApB,IAAA;UACApF,IAAA,EAAAkF,KAAA;UACAoB,SAAA;YACAC,QAAA;YACA1B,SAAA,WAAAA,CAAAwC,KAAA;cACA,OAAAA,KAAA,CAAAC,MAAA,OAAAD,KAAA,CAAAE,SAAA,iBAAAF,KAAA;YACA;UACA;QACA;QACAlC,MAAA,GACA;UACApF,IAAA;UACAqF,IAAA;UACApF,IAAA,EAAAgH,KAAA;UACAxB,SAAA;YACAkB,KAAA,WAAAA,CAAA3C,MAAA;cACA,MAAAyD,MAAA;cACA,OAAAA,MAAA,CAAAzD,MAAA,CAAAmD,SAAA,GAAAM,MAAA,CAAAF,MAAA;YACA;UACA;QACA;MAEA;MACA,KAAA/G,MAAA,CAAAG,YAAA,CAAAqF,SAAA,CAAArB,MAAA;IACA;IACAN,oBAAApE,IAAA;MACA,MAAA0E,MAAA;QACAC,OAAA;UACAC,OAAA;UACAC,SAAA;QACA;QACAC,MAAA;UACAoB,MAAA;UACAlG,IAAA,EAAAA,IAAA,CAAAkF;QACA;QACAC,MAAA,GACA;UACApF,IAAA;UACAqF,IAAA;UACAC,MAAA;UACAC,MAAA;UACAmC,iBAAA;UACAjC,SAAA;YACAI,YAAA;YACAC,WAAA;YACAC,WAAA;UACA;UACA4B,KAAA;YACAC,IAAA;YACAC,QAAA;UACA;UACArC,QAAA;YACAmC,KAAA;cACAC,IAAA;cACAE,QAAA;cACAC,UAAA;YACA;UACA;UACAC,SAAA;YACAJ,IAAA;UACA;UACA3H,IAAA,EAAAA,IAAA,CAAAmB;QACA;MAEA;MACA,KAAAZ,MAAA,CAAAI,QAAA,CAAAoF,SAAA,CAAArB,MAAA;IACA;IACAL,mBAAArE,IAAA;MACA,MAAA0E,MAAA;QACAC,OAAA;UACAC,OAAA;QACA;QACAoB,IAAA;UACAC,IAAA;UACAjB,KAAA;UACAkB,MAAA;UACAC,YAAA;QACA;QACAC,KAAA;UACAhB,IAAA;UACA4C,WAAA;UACAhI,IAAA,EAAAA,IAAA,CAAAqG;QACA;QACAG,KAAA;UACApB,IAAA;UACArF,IAAA;QACA;QACAoF,MAAA,GACA;UACApF,IAAA;UACAqF,IAAA;UACApF,IAAA,EAAAA,IAAA,CAAAiI,MAAA;UACAC,MAAA;UACAC,SAAA;YACAzB,KAAA,MAAA5G,OAAA,CAAA6G,OAAA,CAAAC,cAAA,cACA;cAAAC,MAAA;cAAAH,KAAA;YAAA,GACA;cAAAG,MAAA;cAAAH,KAAA;YAAA,EACA;UACA;UACA0B,SAAA;YACAC,KAAA;YACA3B,KAAA;UACA;UACA4B,UAAA;UACAC,MAAA;QACA;MAEA;MACA,KAAAhI,MAAA,CAAAK,OAAA,CAAAmF,SAAA,CAAArB,MAAA;IACA;IACAJ,sBAAAtE,IAAA;MACA,MAAAkF,KAAA,GAAAlF,IAAA,CAAA8G,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAhH,IAAA;MACA,MAAAyI,UAAA,GAAAxI,IAAA,CAAA8G,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAA0B,SAAA;MAEA,MAAA/D,MAAA;QACAC,OAAA;UACAC,OAAA;UACAC,SAAA,WAAAA,CAAAd,MAAA;YACA,MAAAgD,IAAA,GAAA/G,IAAA,CAAA+D,MAAA,IAAAmD,SAAA;YACA,cAAAH,IAAA,CAAAhH,IAAA,aAAAgH,IAAA,CAAA2B,WAAA,aAAA3B,IAAA,CAAA4B,UAAA,aAAA5B,IAAA,CAAA0B,SAAA;UACA;QACA;QACAzC,IAAA;UACAC,IAAA;UACAjB,KAAA;UACAkB,MAAA;UACAC,YAAA;QACA;QACAC,KAAA;UACAhB,IAAA;UACApF,IAAA,EAAAkF;QACA;QACAsB,KAAA;UACApB,IAAA;UACArF,IAAA;UACA6I,GAAA;QACA;QACAzD,MAAA,GACA;UACApF,IAAA;UACAqF,IAAA;UACApF,IAAA,EAAAwI,UAAA;UACAhD,SAAA;YACAkB,KAAA,WAAAA,CAAA3C,MAAA;cACA,MAAA8E,IAAA,GAAA9E,MAAA,CAAAsD,KAAA;cACA,IAAAwB,IAAA;cACA,IAAAA,IAAA;cACA,IAAAA,IAAA;cACA;YACA;UACA;QACA;MAEA;MACA,KAAAtI,MAAA,CAAA2B,UAAA,CAAA6D,SAAA,CAAArB,MAAA;IACA;IACAH,qBAAAvE,IAAA;MACA,MAAA0E,MAAA;QACAC,OAAA;UACAC,OAAA;UACAC,SAAA;QACA;QACAC,MAAA;UACAC,MAAA;UACAkB,IAAA;UACAjG,IAAA,EAAAA,IAAA,CAAAkF;QACA;QACAC,MAAA,GACA;UACApF,IAAA;UACAqF,IAAA;UACAC,MAAA;UACAC,MAAA;UACAtF,IAAA,EAAAA,IAAA,CAAAmB,MAAA;UACAoE,QAAA;YACAC,SAAA;cACAC,UAAA;cACAC,aAAA;cACAC,WAAA;YACA;UACA;QACA;MAEA;MACA,KAAApF,MAAA,CAAA6B,SAAA,CAAA2D,SAAA,CAAArB,MAAA;IACA;IACAD,wBAAAzE,IAAA;MACA,MAAA8I,KAAA,GAAA9I,IAAA,CAAA8G,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAgC,IAAA,EAAAC,OAAA;MACA,MAAAC,WAAA,GAAAjJ,IAAA,CAAA8G,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAkC,WAAA,EAAAD,OAAA;MACA,MAAAE,WAAA,GAAAlJ,IAAA,CAAA8G,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAAoC,UAAA,EAAAH,OAAA;MAEA,MAAAtE,MAAA;QACAC,OAAA;UACAC,OAAA;UACAC,SAAA,WAAAA,CAAAd,MAAA;YACA,cAAAA,MAAA,IAAAqF,SAAA,cAAArF,MAAA,IAAAsD,KAAA,aAAAtD,MAAA,IAAAsD,KAAA;UACA;QACA;QACAvC,MAAA;UACA9E,IAAA;QACA;QACAgG,IAAA;UACAC,IAAA;UACAjB,KAAA;UACAkB,MAAA;UACAC,YAAA;QACA;QACAC,KAAA;UACAhB,IAAA;UACApF,IAAA,EAAA8I,KAAA;UACAxC,SAAA;YACA+C,MAAA;YACAxE,SAAA,WAAAA,CAAAwC,KAAA;cACA,OAAAA,KAAA,CAAAE,SAAA;YACA;UACA;QACA;QACAf,KAAA,GACA;UACApB,IAAA;UACArF,IAAA;UACA6H,QAAA;QACA,GACA;UACAxC,IAAA;UACArF,IAAA;UACA6H,QAAA;QACA,EACA;QACAzC,MAAA,GACA;UACApF,IAAA;UACAqF,IAAA;UACAkE,UAAA;UACAtJ,IAAA,EAAAiJ,WAAA;UACAf,MAAA;UACAE,SAAA;YACA1B,KAAA;UACA;QACA,GACA;UACA3G,IAAA;UACAqF,IAAA;UACAkE,UAAA;UACAtJ,IAAA,EAAAkJ,WAAA;UACA1D,SAAA;YACAkB,KAAA;UACA;QACA;MAEA;MACA,KAAAnG,MAAA,CAAAM,YAAA,CAAAkF,SAAA,CAAArB,MAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}