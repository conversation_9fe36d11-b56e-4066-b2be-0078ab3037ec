package com.example.mapper;

import com.example.entity.OrderItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 订单详情数据访问接口
 */
public interface OrderItemMapper {

    /**
     * 根据订单ID查询订单详情列表
     */
    List<OrderItem> selectByOrderId(@Param("orderId") Integer orderId);

    /**
     * 插入订单详情
     */
    void insert(OrderItem orderItem);

    /**
     * 批量插入订单详情
     */
    void insertBatch(@Param("list") List<OrderItem> orderItems);

    /**
     * 根据ID删除订单详情
     */
    void deleteById(@Param("id") Integer id);

    /**
     * 根据订单ID删除所有订单详情
     */
    void deleteByOrderId(@Param("orderId") Integer orderId);
} 