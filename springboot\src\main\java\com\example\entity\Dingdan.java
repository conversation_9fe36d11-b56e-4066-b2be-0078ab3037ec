package com.example.entity;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单表
 */
public class Dingdan {
    /** 主键ID */
    private Integer id;
    /** 用户名 */
    private String sfUserName;
    /** 用户ID */
    private Integer sfUserId;
    /** 餐桌ID */
    private Integer tableId;
    /** 餐桌号 */
    private String tableNumber;
    /** 订单状态 */
    private String status;
    /** 订单编号 */
    private Integer sfOrderNumber;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date sfCreateTime;
    /** 用户备注 */
    private String sfRemark;
    /** 用户评价 */
    private String sfEvaluation;
    /** 订单总价格 */
    private BigDecimal sfTotalPrice;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getSfUserName() {
        return sfUserName;
    }

    public void setSfUserName(String sfUserName) {
        this.sfUserName = sfUserName;
    }
    
    public Integer getSfUserId() {
        return sfUserId;
    }

    public void setSfUserId(Integer sfUserId) {
        this.sfUserId = sfUserId;
    }
    
    public Integer getTableId() {
        return tableId;
    }

    public void setTableId(Integer tableId) {
        this.tableId = tableId;
    }
    
    public String getTableNumber() {
        return tableNumber;
    }

    public void setTableNumber(String tableNumber) {
        this.tableNumber = tableNumber;
    }
    
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    
    public Integer getSfOrderNumber() {
        return sfOrderNumber;
    }

    public void setSfOrderNumber(Integer sfOrderNumber) {
        this.sfOrderNumber = sfOrderNumber;
    }
    
    public Date getSfCreateTime() {
        return sfCreateTime;
    }

    public void setSfCreateTime(Date sfCreateTime) {
        this.sfCreateTime = sfCreateTime;
    }
    
    public String getSfRemark() {
        return sfRemark;
    }

    public void setSfRemark(String sfRemark) {
        this.sfRemark = sfRemark;
    }
    
    public String getSfEvaluation() {
        return sfEvaluation;
    }

    public void setSfEvaluation(String sfEvaluation) {
        this.sfEvaluation = sfEvaluation;
    }
    
    public BigDecimal getSfTotalPrice() {
        return sfTotalPrice;
    }

    public void setSfTotalPrice(BigDecimal sfTotalPrice) {
        this.sfTotalPrice = sfTotalPrice;
    }
}