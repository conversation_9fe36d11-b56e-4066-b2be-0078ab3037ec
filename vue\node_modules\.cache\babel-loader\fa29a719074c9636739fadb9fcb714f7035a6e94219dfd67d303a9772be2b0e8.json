{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", [_c(\"div\", {\n    staticClass: \"card\",\n    staticStyle: {\n      padding: \"15px\",\n      \"margin-bottom\": \"20px\"\n    }\n  }, [_vm._v(\" 您好，\" + _vm._s(_vm.user?.name) + \"！欢迎使用餐厅管理系统 \")]), _c(\"div\", {\n    staticClass: \"overview-cards\"\n  }, [_c(\"div\", {\n    staticClass: \"overview-card\"\n  }, [_c(\"div\", {\n    staticClass: \"overview-icon\"\n  }, [_vm._v(\"👥\")]), _c(\"div\", {\n    staticClass: \"overview-content\"\n  }, [_c(\"div\", {\n    staticClass: \"overview-title\"\n  }, [_vm._v(\"总用户数\")]), _c(\"div\", {\n    staticClass: \"overview-value\"\n  }, [_vm._v(_vm._s(_vm.overviewData.totalUsers || 0))])])]), _c(\"div\", {\n    staticClass: \"overview-card\"\n  }, [_c(\"div\", {\n    staticClass: \"overview-icon\"\n  }, [_vm._v(\"📋\")]), _c(\"div\", {\n    staticClass: \"overview-content\"\n  }, [_c(\"div\", {\n    staticClass: \"overview-title\"\n  }, [_vm._v(\"总订单数\")]), _c(\"div\", {\n    staticClass: \"overview-value\"\n  }, [_vm._v(_vm._s(_vm.overviewData.totalOrders || 0))])])]), _c(\"div\", {\n    staticClass: \"overview-card\"\n  }, [_c(\"div\", {\n    staticClass: \"overview-icon\"\n  }, [_vm._v(\"💰\")]), _c(\"div\", {\n    staticClass: \"overview-content\"\n  }, [_c(\"div\", {\n    staticClass: \"overview-title\"\n  }, [_vm._v(\"总收入\")]), _c(\"div\", {\n    staticClass: \"overview-value\"\n  }, [_vm._v(\"¥\" + _vm._s((_vm.overviewData.totalRevenue || 0).toFixed(2)))])])]), _c(\"div\", {\n    staticClass: \"overview-card\"\n  }, [_c(\"div\", {\n    staticClass: \"overview-icon\"\n  }, [_vm._v(\"⭐\")]), _c(\"div\", {\n    staticClass: \"overview-content\"\n  }, [_c(\"div\", {\n    staticClass: \"overview-title\"\n  }, [_vm._v(\"平均评分\")]), _c(\"div\", {\n    staticClass: \"overview-value\"\n  }, [_vm._v(_vm._s((_vm.overviewData.avgRating || 0).toFixed(1)))])])])]), _c(\"div\", {\n    staticClass: \"chart-row\"\n  }, [_c(\"div\", {\n    staticClass: \"card chart-card\"\n  }, [_c(\"div\", {\n    staticClass: \"chart-title\"\n  }, [_vm._v(\"订单状态分布\")]), _c(\"div\", {\n    ref: \"statusPieChart\",\n    staticClass: \"chart-container\"\n  })]), _c(\"div\", {\n    staticClass: \"card chart-card\"\n  }, [_c(\"div\", {\n    staticClass: \"chart-title\"\n  }, [_vm._v(\"月度收入统计\")]), _c(\"div\", {\n    ref: \"revenueChart\",\n    staticClass: \"chart-container\"\n  })])]), _c(\"div\", {\n    staticClass: \"chart-row\"\n  }, [_c(\"div\", {\n    staticClass: \"card chart-card\"\n  }, [_c(\"div\", {\n    staticClass: \"chart-title\"\n  }, [_vm._v(\"热门菜品销量排行\")]), _c(\"div\", {\n    ref: \"popularFoodsChart\",\n    staticClass: \"chart-container\"\n  })]), _c(\"div\", {\n    staticClass: \"card chart-card\"\n  }, [_c(\"div\", {\n    staticClass: \"chart-title\"\n  }, [_vm._v(\"分类销售占比\")]), _c(\"div\", {\n    ref: \"categoryChart\",\n    staticClass: \"chart-container\"\n  })])]), _c(\"div\", {\n    staticClass: \"chart-row\"\n  }, [_c(\"div\", {\n    staticClass: \"card chart-card\"\n  }, [_c(\"div\", {\n    staticClass: \"chart-title\"\n  }, [_vm._v(\"用户注册趋势\")]), _c(\"div\", {\n    ref: \"userRegChart\",\n    staticClass: \"chart-container\"\n  })]), _c(\"div\", {\n    staticClass: \"card chart-card\"\n  }, [_c(\"div\", {\n    staticClass: \"chart-title\"\n  }, [_vm._v(\"餐桌使用率\")]), _c(\"div\", {\n    ref: \"tableUsageChart\",\n    staticClass: \"chart-container\"\n  })])]), _c(\"div\", {\n    staticClass: \"chart-row\"\n  }, [_c(\"div\", {\n    staticClass: \"card chart-card\"\n  }, [_c(\"div\", {\n    staticClass: \"chart-title\"\n  }, [_vm._v(\"投诉处理统计\")]), _c(\"div\", {\n    ref: \"complaintChart\",\n    staticClass: \"chart-container\"\n  })]), _c(\"div\", {\n    staticClass: \"card chart-card\"\n  }, [_c(\"div\", {\n    staticClass: \"chart-title\"\n  }, [_vm._v(\"近30天用户活跃度\")]), _c(\"div\", {\n    ref: \"userActivityChart\",\n    staticClass: \"chart-container\"\n  })])])]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "staticStyle", "padding", "_v", "_s", "user", "name", "overviewData", "totalUsers", "totalOrders", "totalRevenue", "toFixed", "avgRating", "ref", "staticRenderFns", "_withStripped"], "sources": ["C:/Users/<USER>/Desktop/danzi/qiye/bis/order/project-manager/vue/src/views/manager/Home.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", [\n    _c(\n      \"div\",\n      {\n        staticClass: \"card\",\n        staticStyle: { padding: \"15px\", \"margin-bottom\": \"20px\" },\n      },\n      [_vm._v(\" 您好，\" + _vm._s(_vm.user?.name) + \"！欢迎使用餐厅管理系统 \")]\n    ),\n    _c(\"div\", { staticClass: \"overview-cards\" }, [\n      _c(\"div\", { staticClass: \"overview-card\" }, [\n        _c(\"div\", { staticClass: \"overview-icon\" }, [_vm._v(\"👥\")]),\n        _c(\"div\", { staticClass: \"overview-content\" }, [\n          _c(\"div\", { staticClass: \"overview-title\" }, [_vm._v(\"总用户数\")]),\n          _c(\"div\", { staticClass: \"overview-value\" }, [\n            _vm._v(_vm._s(_vm.overviewData.totalUsers || 0)),\n          ]),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"overview-card\" }, [\n        _c(\"div\", { staticClass: \"overview-icon\" }, [_vm._v(\"📋\")]),\n        _c(\"div\", { staticClass: \"overview-content\" }, [\n          _c(\"div\", { staticClass: \"overview-title\" }, [_vm._v(\"总订单数\")]),\n          _c(\"div\", { staticClass: \"overview-value\" }, [\n            _vm._v(_vm._s(_vm.overviewData.totalOrders || 0)),\n          ]),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"overview-card\" }, [\n        _c(\"div\", { staticClass: \"overview-icon\" }, [_vm._v(\"💰\")]),\n        _c(\"div\", { staticClass: \"overview-content\" }, [\n          _c(\"div\", { staticClass: \"overview-title\" }, [_vm._v(\"总收入\")]),\n          _c(\"div\", { staticClass: \"overview-value\" }, [\n            _vm._v(\n              \"¥\" + _vm._s((_vm.overviewData.totalRevenue || 0).toFixed(2))\n            ),\n          ]),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"overview-card\" }, [\n        _c(\"div\", { staticClass: \"overview-icon\" }, [_vm._v(\"⭐\")]),\n        _c(\"div\", { staticClass: \"overview-content\" }, [\n          _c(\"div\", { staticClass: \"overview-title\" }, [_vm._v(\"平均评分\")]),\n          _c(\"div\", { staticClass: \"overview-value\" }, [\n            _vm._v(_vm._s((_vm.overviewData.avgRating || 0).toFixed(1))),\n          ]),\n        ]),\n      ]),\n    ]),\n    _c(\"div\", { staticClass: \"chart-row\" }, [\n      _c(\"div\", { staticClass: \"card chart-card\" }, [\n        _c(\"div\", { staticClass: \"chart-title\" }, [_vm._v(\"订单状态分布\")]),\n        _c(\"div\", { ref: \"statusPieChart\", staticClass: \"chart-container\" }),\n      ]),\n      _c(\"div\", { staticClass: \"card chart-card\" }, [\n        _c(\"div\", { staticClass: \"chart-title\" }, [_vm._v(\"月度收入统计\")]),\n        _c(\"div\", { ref: \"revenueChart\", staticClass: \"chart-container\" }),\n      ]),\n    ]),\n    _c(\"div\", { staticClass: \"chart-row\" }, [\n      _c(\"div\", { staticClass: \"card chart-card\" }, [\n        _c(\"div\", { staticClass: \"chart-title\" }, [_vm._v(\"热门菜品销量排行\")]),\n        _c(\"div\", { ref: \"popularFoodsChart\", staticClass: \"chart-container\" }),\n      ]),\n      _c(\"div\", { staticClass: \"card chart-card\" }, [\n        _c(\"div\", { staticClass: \"chart-title\" }, [_vm._v(\"分类销售占比\")]),\n        _c(\"div\", { ref: \"categoryChart\", staticClass: \"chart-container\" }),\n      ]),\n    ]),\n    _c(\"div\", { staticClass: \"chart-row\" }, [\n      _c(\"div\", { staticClass: \"card chart-card\" }, [\n        _c(\"div\", { staticClass: \"chart-title\" }, [_vm._v(\"用户注册趋势\")]),\n        _c(\"div\", { ref: \"userRegChart\", staticClass: \"chart-container\" }),\n      ]),\n      _c(\"div\", { staticClass: \"card chart-card\" }, [\n        _c(\"div\", { staticClass: \"chart-title\" }, [_vm._v(\"餐桌使用率\")]),\n        _c(\"div\", { ref: \"tableUsageChart\", staticClass: \"chart-container\" }),\n      ]),\n    ]),\n    _c(\"div\", { staticClass: \"chart-row\" }, [\n      _c(\"div\", { staticClass: \"card chart-card\" }, [\n        _c(\"div\", { staticClass: \"chart-title\" }, [_vm._v(\"投诉处理统计\")]),\n        _c(\"div\", { ref: \"complaintChart\", staticClass: \"chart-container\" }),\n      ]),\n      _c(\"div\", { staticClass: \"card chart-card\" }, [\n        _c(\"div\", { staticClass: \"chart-title\" }, [_vm._v(\"近30天用户活跃度\")]),\n        _c(\"div\", { ref: \"userActivityChart\", staticClass: \"chart-container\" }),\n      ]),\n    ]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE,CACfA,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAE,eAAe,EAAE;IAAO;EAC1D,CAAC,EACD,CAACL,GAAG,CAACM,EAAE,CAAC,MAAM,GAAGN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACQ,IAAI,EAAEC,IAAI,CAAC,GAAG,cAAc,CAAC,CAC3D,CAAC,EACDR,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAC3DL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC9DL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CH,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACU,YAAY,CAACC,UAAU,IAAI,CAAC,CAAC,CAAC,CACjD,CAAC,CACH,CAAC,CACH,CAAC,EACFV,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAC3DL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC9DL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CH,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAACP,GAAG,CAACU,YAAY,CAACE,WAAW,IAAI,CAAC,CAAC,CAAC,CAClD,CAAC,CACH,CAAC,CACH,CAAC,EACFX,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAC3DL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAC7DL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CH,GAAG,CAACM,EAAE,CACJ,GAAG,GAAGN,GAAG,CAACO,EAAE,CAAC,CAACP,GAAG,CAACU,YAAY,CAACG,YAAY,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,CAC9D,CAAC,CACF,CAAC,CACH,CAAC,CACH,CAAC,EACFb,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC1CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAC1DL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAC9DL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CH,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,EAAE,CAAC,CAACP,GAAG,CAACU,YAAY,CAACK,SAAS,IAAI,CAAC,EAAED,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAC7D,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACFb,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC7DL,EAAE,CAAC,KAAK,EAAE;IAAEe,GAAG,EAAE,gBAAgB;IAAEb,WAAW,EAAE;EAAkB,CAAC,CAAC,CACrE,CAAC,EACFF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC7DL,EAAE,CAAC,KAAK,EAAE;IAAEe,GAAG,EAAE,cAAc;IAAEb,WAAW,EAAE;EAAkB,CAAC,CAAC,CACnE,CAAC,CACH,CAAC,EACFF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC,EAC/DL,EAAE,CAAC,KAAK,EAAE;IAAEe,GAAG,EAAE,mBAAmB;IAAEb,WAAW,EAAE;EAAkB,CAAC,CAAC,CACxE,CAAC,EACFF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC7DL,EAAE,CAAC,KAAK,EAAE;IAAEe,GAAG,EAAE,eAAe;IAAEb,WAAW,EAAE;EAAkB,CAAC,CAAC,CACpE,CAAC,CACH,CAAC,EACFF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC7DL,EAAE,CAAC,KAAK,EAAE;IAAEe,GAAG,EAAE,cAAc;IAAEb,WAAW,EAAE;EAAkB,CAAC,CAAC,CACnE,CAAC,EACFF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,EAC5DL,EAAE,CAAC,KAAK,EAAE;IAAEe,GAAG,EAAE,iBAAiB;IAAEb,WAAW,EAAE;EAAkB,CAAC,CAAC,CACtE,CAAC,CACH,CAAC,EACFF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAY,CAAC,EAAE,CACtCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,EAC7DL,EAAE,CAAC,KAAK,EAAE;IAAEe,GAAG,EAAE,gBAAgB;IAAEb,WAAW,EAAE;EAAkB,CAAC,CAAC,CACrE,CAAC,EACFF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CAACH,GAAG,CAACM,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,EAChEL,EAAE,CAAC,KAAK,EAAE;IAAEe,GAAG,EAAE,mBAAmB;IAAEb,WAAW,EAAE;EAAkB,CAAC,CAAC,CACxE,CAAC,CACH,CAAC,CACH,CAAC;AACJ,CAAC;AACD,IAAIc,eAAe,GAAG,EAAE;AACxBlB,MAAM,CAACmB,aAAa,GAAG,IAAI;AAE3B,SAASnB,MAAM,EAAEkB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}