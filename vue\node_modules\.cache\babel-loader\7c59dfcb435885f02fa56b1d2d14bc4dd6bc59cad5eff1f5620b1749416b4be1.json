{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport TableSelect from '@/components/TableSelect.vue';\nimport StarRating from '@/components/StarRating.vue';\nimport FoodReviewList from '@/components/FoodReviewList.vue';\nimport FoodReviewForm from '@/components/FoodReviewForm.vue';\nexport default {\n  name: \"GoodsList\",\n  components: {\n    TableSelect,\n    StarRating,\n    FoodReviewList,\n    FoodReviewForm\n  },\n  data() {\n    return {\n      tableData: [],\n      // 商品数据\n      pageNum: 1,\n      // 当前页码\n      pageSize: 12,\n      // 每页12条\n      total: 0,\n      // 总数\n\n      detailVisible: false,\n      // 详情弹窗显示\n      currentGoods: null,\n      // 当前查看的商品\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}'),\n      // 当前登录用户\n\n      // 立即购买餐桌选择相关\n      buyTableDialogVisible: false,\n      selectedBuyTable: null,\n      buyOrderForm: {\n        goodsId: null,\n        goodsName: '',\n        goodsPrice: 0\n      },\n      buySubmitting: false,\n      // 防重复提交\n\n      // 评价相关\n      activeTab: 'detail',\n      // 详情弹窗中的活跃Tab\n      reviewDialogVisible: false,\n      // 评价提交对话框\n      checkingPermission: false // 检查评价权限中\n    };\n  },\n  created() {\n    this.load(1);\n  },\n  methods: {\n    load(pageNum) {\n      // 加载商品数据\n      if (pageNum) this.pageNum = pageNum;\n      this.$request.get('/foods/selectPage', {\n        params: {\n          pageNum: this.pageNum,\n          pageSize: this.pageSize\n        }\n      }).then(res => {\n        if (res.code === '200') {\n          this.tableData = res.data?.list;\n          this.total = res.data?.total;\n        } else {\n          this.$message.error(res.msg);\n        }\n      });\n    },\n    showDetail(item) {\n      // 显示商品详情\n      // 先显示弹窗，使用基础数据\n      this.currentGoods = item;\n      this.detailVisible = true;\n\n      // 异步加载包含评价信息的完整数据\n      this.loadGoodsWithReviews(item.id);\n    },\n    // 加载包含评价信息的菜品详情\n    async loadGoodsWithReviews(foodId) {\n      try {\n        const response = await this.$request.get(`/foods/selectByIdWithReviews/${foodId}`);\n        if (response.code === '200') {\n          // 更新当前商品数据，包含评价统计信息\n          this.currentGoods = response.data;\n        } else {\n          console.error('获取菜品详情失败:', response.msg);\n        }\n      } catch (error) {\n        console.error('获取菜品详情失败:', error);\n      }\n    },\n    // 加入购物车\n    addToCart(goodsId) {\n      this.$request.post('/cart/add', {\n        foodId: goodsId,\n        quantity: 1\n      }).then(res => {\n        if (res.code === '200') {\n          this.$message.success('商品已加入购物车！');\n        } else {\n          this.$message.error(res.msg || '操作失败');\n        }\n      }).catch(err => {\n        console.error('加入购物车失败:', err);\n        if (err.response && err.response.data && err.response.data.msg) {\n          this.$message.error(err.response.data.msg);\n        } else {\n          this.$message.error('操作失败，请重试');\n        }\n      });\n    },\n    // 显示立即购买对话框\n    showBuyDialog(goods) {\n      this.buyOrderForm = {\n        goodsId: goods.id,\n        goodsName: goods.name,\n        goodsPrice: goods.sfPrice\n      };\n      this.detailVisible = false;\n      this.selectedBuyTable = null;\n      this.buyTableDialogVisible = true;\n    },\n    // 处理立即购买的餐桌选择\n    handleBuyTableSelected(table) {\n      this.selectedBuyTable = table;\n    },\n    // 确认立即购买订单（选择餐桌后）\n    confirmBuyOrder() {\n      if (!this.selectedBuyTable) {\n        this.$message.warning('请选择餐桌');\n        return;\n      }\n\n      // 防重复提交\n      if (this.buySubmitting) {\n        this.$message.warning('正在处理中，请勿重复提交');\n        return;\n      }\n      this.$confirm(`确认要在${this.selectedBuyTable.tableNumber}号桌下单吗？总金额：¥${this.buyOrderForm.goodsPrice}`, '立即购买确认', {\n        confirmButtonText: '确定下单',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.buySubmitting = true;\n\n        // 立即购买流程：先加入购物车，再立即结算\n        this.$request.post('/cart/add', {\n          foodId: this.buyOrderForm.goodsId,\n          quantity: 1\n        }).then(() => {\n          // 加入购物车成功后，立即结算\n          return this.$request.post('/cart/checkout', {\n            tableNumber: this.selectedBuyTable.tableNumber,\n            remark: `立即购买：${this.buyOrderForm.goodsName}`\n          });\n        }).then(res => {\n          if (res.code === '200') {\n            this.$message.success(`订单创建成功！餐桌：${this.selectedBuyTable.tableNumber}号`);\n            this.buyTableDialogVisible = false;\n            this.selectedBuyTable = null;\n          } else {\n            this.$message.error(res.msg || '下单失败');\n          }\n        }).catch(err => {\n          console.error('下单失败:', err);\n          if (err.response && err.response.data && err.response.data.msg) {\n            this.$message.error(err.response.data.msg);\n          } else {\n            this.$message.error('下单失败，请重试');\n          }\n        }).finally(() => {\n          this.buySubmitting = false;\n        });\n      }).catch(() => {\n        this.$message.info('已取消下单');\n      });\n    },\n    handleCurrentChange(pageNum) {\n      // 分页变化\n      this.load(pageNum);\n    },\n    // 显示评价对话框\n    async showReviewDialog() {\n      if (!this.user || !this.user.id) {\n        this.$message.warning('请先登录后再评价');\n        this.$router.push('/login');\n        return;\n      }\n\n      // 检查是否可以评价\n      this.checkingPermission = true;\n      try {\n        const response = await this.$request.get('/foodReview/canReview', {\n          params: {\n            foodId: this.currentGoods.id\n          }\n        });\n        if (response.code === '200') {\n          if (response.data) {\n            this.reviewDialogVisible = true;\n          } else {\n            this.$message.warning('您还没有购买过此商品，无法评价');\n          }\n        } else {\n          this.$message.error(response.msg || '检查评价权限失败');\n        }\n      } catch (error) {\n        console.error('检查评价权限失败:', error);\n        this.$message.error('检查评价权限失败');\n      } finally {\n        this.checkingPermission = false;\n      }\n    },\n    // 处理评价提交成功\n    handleReviewSuccess(reviewData) {\n      this.$message.success('评价发布成功！');\n      this.reviewDialogVisible = false;\n\n      // 刷新商品信息以更新评价统计\n      this.refreshCurrentGoods();\n\n      // 切换到评价Tab\n      this.activeTab = 'reviews';\n    },\n    // 刷新当前商品信息\n    async refreshCurrentGoods() {\n      if (!this.currentGoods || !this.currentGoods.id) return;\n      try {\n        const response = await this.$request.get(`/foods/selectByIdWithReviews/${this.currentGoods.id}`);\n        if (response.code === '200') {\n          this.currentGoods = response.data;\n\n          // 同时更新列表中的商品信息\n          const index = this.tableData.findIndex(item => item.id === this.currentGoods.id);\n          if (index > -1) {\n            this.$set(this.tableData, index, {\n              ...this.tableData[index],\n              ...response.data\n            });\n          }\n        }\n      } catch (error) {\n        console.error('刷新商品信息失败:', error);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["TableSelect", "StarRating", "FoodReviewList", "FoodReviewForm", "name", "components", "data", "tableData", "pageNum", "pageSize", "total", "detailVisible", "currentGoods", "user", "JSON", "parse", "localStorage", "getItem", "buyTableDialogVisible", "selectedBuyTable", "buyOrderForm", "goodsId", "goodsName", "goodsPrice", "buySubmitting", "activeTab", "reviewDialogVisible", "checkingPermission", "created", "load", "methods", "$request", "get", "params", "then", "res", "code", "list", "$message", "error", "msg", "showDetail", "item", "loadGoodsWithReviews", "id", "foodId", "response", "console", "addToCart", "post", "quantity", "success", "catch", "err", "showBuyDialog", "goods", "sfPrice", "handleBuyTableSelected", "table", "confirmBuyOrder", "warning", "$confirm", "tableNumber", "confirmButtonText", "cancelButtonText", "type", "remark", "finally", "info", "handleCurrentChange", "showReviewDialog", "$router", "push", "handleReviewSuccess", "reviewData", "refreshCurrentGoods", "index", "findIndex", "$set"], "sources": ["src/views/front/Foods.vue"], "sourcesContent": ["<template>\r\n    <div class=\"taobao-container\">\r\n        <!-- 商品展示区 -->\r\n        <div class=\"goods-container\">\r\n            <div\r\n                class=\"goods-item\"\r\n                v-for=\"item in tableData\"\r\n                :key=\"item.id\"\r\n                @click=\"showDetail(item)\"\r\n            >\r\n                <div class=\"goods-img-container\">\r\n                    <el-image\r\n                        :src=\"item.sfImage\"\r\n                        fit=\"cover\"\r\n                        class=\"goods-img\"\r\n                    ></el-image>\r\n                </div>\r\n                <div class=\"goods-info\">\r\n                    <div class=\"goods-title\">{{ item.name }}</div>\r\n                    <div class=\"goods-price\">¥{{ item.sfPrice }}</div>\r\n                    <div class=\"goods-stock\" :class=\"{'low-stock': item.sfStock <= 10, 'out-stock': item.sfStock <= 0}\">\r\n                        库存：{{ item.sfStock }}\r\n                    </div>\r\n\r\n                    <div class=\"goods-actions\">\r\n                        <el-button\r\n                            type=\"warning\"\r\n                            size=\"mini\"\r\n                            @click.stop=\"addToCart(item.id)\"\r\n                            class=\"cart-btn\"\r\n                            :disabled=\"item.sfStock <= 0\"\r\n                        >\r\n                            <i class=\"el-icon-goods\"></i> \r\n                            {{ item.sfStock <= 0 ? '缺货' : '加购物车' }}\r\n                        </el-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-container\">\r\n            <el-pagination\r\n                background\r\n                @current-change=\"handleCurrentChange\"\r\n                :current-page=\"pageNum\"\r\n                :page-size=\"pageSize\"\r\n                layout=\"prev, pager, next\"\r\n                :total=\"total\"\r\n                :pager-count=\"5\"\r\n                prev-text=\"上一页\"\r\n                next-text=\"下一页\"\r\n            >\r\n            </el-pagination>\r\n        </div>\r\n\r\n        <!-- 商品详情弹窗 -->\r\n        <el-dialog\r\n            :visible.sync=\"detailVisible\"\r\n            width=\"80%\"\r\n            top=\"5vh\"\r\n            custom-class=\"goods-detail-dialog\"\r\n        >\r\n            <div class=\"detail-container\" v-if=\"currentGoods\">\r\n                <div class=\"detail-left\">\r\n                    <el-image\r\n                        :src=\"currentGoods.sfImage\"\r\n                        fit=\"contain\"\r\n                        class=\"detail-img\"\r\n                    ></el-image>\r\n                </div>\r\n                <div class=\"detail-right\">\r\n                    <h2 class=\"detail-title\">{{ currentGoods.name }}</h2>\r\n                    <div class=\"detail-price\">\r\n                        <span class=\"price-symbol\">¥</span>\r\n                        <span class=\"price-number\">{{ currentGoods.sfPrice }}</span>\r\n                        <!-- 评分信息 -->\r\n                        <div class=\"rating-info\" v-if=\"currentGoods.averageRating > 0\">\r\n                            <star-rating \r\n                                :value=\"currentGoods.averageRating\" \r\n                                :readonly=\"true\" \r\n                                :show-text=\"false\"\r\n                                size=\"small\">\r\n                            </star-rating>\r\n                            <span class=\"rating-text\">{{ currentGoods.averageRating }}分</span>\r\n                            <span class=\"review-count\">({{ currentGoods.reviewCount || 0 }}条评价)</span>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"detail-info\">\r\n                        <div class=\"info-item\">\r\n                            <span class=\"info-label\">商品类型:</span>\r\n                            <span class=\"info-value\">{{ currentGoods.sfCategory }}</span>\r\n                        </div>\r\n                        <div class=\"info-item\">\r\n                            <span class=\"info-label\">库存状态:</span>\r\n                            <span class=\"info-value\" :class=\"{'low-stock': currentGoods.sfStock <= 10, 'out-stock': currentGoods.sfStock <= 0}\">\r\n                                {{ currentGoods.sfStock }}件\r\n                                <span v-if=\"currentGoods.sfStock <= 0\" class=\"stock-warning\">（缺货）</span>\r\n                                <span v-else-if=\"currentGoods.sfStock <= 10\" class=\"stock-warning\">（库存紧张）</span>\r\n                            </span>\r\n                        </div>\r\n                        <div class=\"info-item\">\r\n                            <span class=\"info-label\">上架状态:</span>\r\n                            <span class=\"info-value\">{{ currentGoods.sfShelfStatus }}</span>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"detail-desc\">\r\n                        <h3>商品描述</h3>\r\n                        <p>{{ currentGoods.sfDescription }}</p>\r\n                    </div>\r\n                    <div class=\"detail-actions\">\r\n                        <el-button\r\n                            type=\"warning\"\r\n                            class=\"cart-btn\"\r\n                            @click=\"addToCart(currentGoods.id)\"\r\n                            :disabled=\"currentGoods.sfStock <= 0\"\r\n                        >\r\n                            <i class=\"el-icon-goods\"></i> \r\n                            {{ currentGoods.sfStock <= 0 ? '缺货' : '加入购物车' }}\r\n                        </el-button>\r\n                        <el-button\r\n                            type=\"danger\"\r\n                            class=\"buy-btn\"\r\n                            @click=\"showBuyDialog(currentGoods)\"\r\n                            :disabled=\"currentGoods.sfStock <= 0\"\r\n                        >\r\n                            {{ currentGoods.sfStock <= 0 ? '缺货' : '立即购买' }}\r\n                        </el-button>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            \r\n            <!-- 商品评价区域 -->\r\n            <div class=\"detail-reviews\" v-if=\"currentGoods\">\r\n                <el-tabs v-model=\"activeTab\" class=\"detail-tabs\">\r\n                    <el-tab-pane label=\"商品详情\" name=\"detail\">\r\n                        <!-- 详细商品描述可以在这里扩展 -->\r\n                        <div class=\"product-detail-content\">\r\n                            <p>{{ currentGoods.sfDescription || '暂无详细描述' }}</p>\r\n                        </div>\r\n                    </el-tab-pane>\r\n                    <el-tab-pane :label=\"`商品评价(${currentGoods.reviewCount || 0})`\" name=\"reviews\">\r\n                        <div class=\"reviews-container\">\r\n                            <!-- 写评价按钮 -->\r\n                            <div class=\"review-header\" v-if=\"user && user.id\">\r\n                                <el-button \r\n                                    type=\"primary\" \r\n                                    size=\"small\"\r\n                                    @click=\"showReviewDialog\"\r\n                                    :loading=\"checkingPermission\">\r\n                                    <i class=\"el-icon-edit\"></i>\r\n                                    写评价\r\n                                </el-button>\r\n                            </div>\r\n                            \r\n                            <!-- 评价列表 -->\r\n                            <food-review-list \r\n                                :food-id=\"currentGoods.id\"\r\n                                :show-admin-actions=\"false\"\r\n                                @add-review=\"showReviewDialog\">\r\n                            </food-review-list>\r\n                        </div>\r\n                    </el-tab-pane>\r\n                </el-tabs>\r\n            </div>\r\n        </el-dialog>\r\n\r\n        <!-- 评价提交对话框 -->\r\n        <el-dialog\r\n            title=\"发表评价\"\r\n            :visible.sync=\"reviewDialogVisible\"\r\n            width=\"600px\"\r\n            :close-on-click-modal=\"false\">\r\n            <food-review-form\r\n                v-if=\"reviewDialogVisible && currentGoods\"\r\n                :food-id=\"currentGoods.id\"\r\n                :food-name=\"currentGoods.name\"\r\n                @submit-success=\"handleReviewSuccess\"\r\n                @cancel=\"reviewDialogVisible = false\">\r\n            </food-review-form>\r\n        </el-dialog>\r\n\r\n        <!-- 餐桌选择对话框（立即购买用） -->\r\n        <el-dialog \r\n            title=\"选择餐桌\" \r\n            :visible.sync=\"buyTableDialogVisible\" \r\n            width=\"800px\"\r\n            :close-on-click-modal=\"false\">\r\n            <div class=\"table-selection-container\">\r\n                <div class=\"table-selection-header\">\r\n                    <div class=\"selection-info\">\r\n                        <p v-if=\"buyOrderForm.goodsName\">立即购买：{{ buyOrderForm.goodsName }}，总金额：<span class=\"total-price\">¥{{ buyOrderForm.goodsPrice }}</span></p>\r\n                        <p v-if=\"selectedBuyTable\">已选择：{{ selectedBuyTable.tableNumber }}号桌 ({{ selectedBuyTable.seats }}人座，{{ selectedBuyTable.area }})</p>\r\n                    </div>\r\n                </div>\r\n                \r\n                <!-- 餐桌选择组件 -->\r\n                <table-select \r\n                    v-model=\"selectedBuyTable\" \r\n                    @table-selected=\"handleBuyTableSelected\">\r\n                </table-select>\r\n            </div>\r\n            \r\n            <template slot=\"footer\">\r\n                <span class=\"dialog-footer\">\r\n                    <el-button @click=\"buyTableDialogVisible = false\">取消</el-button>\r\n                    <el-button \r\n                        type=\"primary\" \r\n                        @click=\"confirmBuyOrder\"\r\n                        :disabled=\"!selectedBuyTable\">\r\n                        确认下单\r\n                    </el-button>\r\n                </span>\r\n            </template>\r\n        </el-dialog>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport TableSelect from '@/components/TableSelect.vue'\r\nimport StarRating from '@/components/StarRating.vue'\r\nimport FoodReviewList from '@/components/FoodReviewList.vue'\r\nimport FoodReviewForm from '@/components/FoodReviewForm.vue'\r\n\r\nexport default {\r\n    name: \"GoodsList\",\r\n    components: {\r\n        TableSelect,\r\n        StarRating,\r\n        FoodReviewList,\r\n        FoodReviewForm\r\n    },\r\n            data() {\r\n        return {\r\n            tableData: [],  // 商品数据\r\n            pageNum: 1,     // 当前页码\r\n            pageSize: 12,   // 每页12条\r\n            total: 0,       // 总数\r\n\r\n            detailVisible: false, // 详情弹窗显示\r\n            currentGoods: null,   // 当前查看的商品\r\n            user: JSON.parse(localStorage.getItem('xm-user') || '{}'), // 当前登录用户\r\n\r\n            // 立即购买餐桌选择相关\r\n            buyTableDialogVisible: false,\r\n            selectedBuyTable: null,\r\n            buyOrderForm: {\r\n                goodsId: null,\r\n                goodsName: '',\r\n                goodsPrice: 0\r\n            },\r\n            buySubmitting: false, // 防重复提交\r\n\r\n            // 评价相关\r\n            activeTab: 'detail', // 详情弹窗中的活跃Tab\r\n            reviewDialogVisible: false, // 评价提交对话框\r\n            checkingPermission: false // 检查评价权限中\r\n        }\r\n    },\r\n    created() {\r\n        this.load(1)\r\n    },\r\n    methods: {\r\n        load(pageNum) {  // 加载商品数据\r\n            if (pageNum) this.pageNum = pageNum\r\n            this.$request.get('/foods/selectPage', {\r\n                params: {\r\n                    pageNum: this.pageNum,\r\n                    pageSize: this.pageSize,\r\n\r\n                }\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.tableData = res.data?.list\r\n                    this.total = res.data?.total\r\n                } else {\r\n                    this.$message.error(res.msg)\r\n                }\r\n            })\r\n        },\r\n        showDetail(item) {  // 显示商品详情\r\n            // 先显示弹窗，使用基础数据\r\n            this.currentGoods = item\r\n            this.detailVisible = true\r\n            \r\n            // 异步加载包含评价信息的完整数据\r\n            this.loadGoodsWithReviews(item.id)\r\n        },\r\n        \r\n        // 加载包含评价信息的菜品详情\r\n        async loadGoodsWithReviews(foodId) {\r\n            try {\r\n                const response = await this.$request.get(`/foods/selectByIdWithReviews/${foodId}`)\r\n                if (response.code === '200') {\r\n                    // 更新当前商品数据，包含评价统计信息\r\n                    this.currentGoods = response.data\r\n                } else {\r\n                    console.error('获取菜品详情失败:', response.msg)\r\n                }\r\n            } catch (error) {\r\n                console.error('获取菜品详情失败:', error)\r\n            }\r\n        },\r\n        // 加入购物车\r\n        addToCart(goodsId) {\r\n            this.$request.post('/cart/add', {\r\n                foodId: goodsId,\r\n                quantity: 1\r\n            }).then(res => {\r\n                if (res.code === '200') {\r\n                    this.$message.success('商品已加入购物车！')\r\n                } else {\r\n                    this.$message.error(res.msg || '操作失败')\r\n                }\r\n            }).catch(err => {\r\n                console.error('加入购物车失败:', err)\r\n                if (err.response && err.response.data && err.response.data.msg) {\r\n                    this.$message.error(err.response.data.msg)\r\n                } else {\r\n                    this.$message.error('操作失败，请重试')\r\n                }\r\n            })\r\n        },\r\n        // 显示立即购买对话框\r\n        showBuyDialog(goods) {\r\n            this.buyOrderForm = {\r\n                goodsId: goods.id,\r\n                goodsName: goods.name,\r\n                goodsPrice: goods.sfPrice\r\n            }\r\n            this.detailVisible = false\r\n            this.selectedBuyTable = null\r\n            this.buyTableDialogVisible = true\r\n        },\r\n\r\n        // 处理立即购买的餐桌选择\r\n        handleBuyTableSelected(table) {\r\n            this.selectedBuyTable = table\r\n        },\r\n\r\n        // 确认立即购买订单（选择餐桌后）\r\n        confirmBuyOrder() {\r\n            if (!this.selectedBuyTable) {\r\n                this.$message.warning('请选择餐桌')\r\n                return\r\n            }\r\n\r\n            // 防重复提交\r\n            if (this.buySubmitting) {\r\n                this.$message.warning('正在处理中，请勿重复提交')\r\n                return\r\n            }\r\n\r\n            this.$confirm(`确认要在${this.selectedBuyTable.tableNumber}号桌下单吗？总金额：¥${this.buyOrderForm.goodsPrice}`, '立即购买确认', {\r\n                confirmButtonText: '确定下单',\r\n                cancelButtonText: '取消',\r\n                type: 'warning'\r\n            }).then(() => {\r\n                this.buySubmitting = true\r\n                \r\n                // 立即购买流程：先加入购物车，再立即结算\r\n                this.$request.post('/cart/add', {\r\n                    foodId: this.buyOrderForm.goodsId,\r\n                    quantity: 1\r\n                }).then(() => {\r\n                    // 加入购物车成功后，立即结算\r\n                    return this.$request.post('/cart/checkout', {\r\n                        tableNumber: this.selectedBuyTable.tableNumber,\r\n                        remark: `立即购买：${this.buyOrderForm.goodsName}`\r\n                    })\r\n                }).then(res => {\r\n                    if (res.code === '200') {\r\n                        this.$message.success(`订单创建成功！餐桌：${this.selectedBuyTable.tableNumber}号`)\r\n                        this.buyTableDialogVisible = false\r\n                        this.selectedBuyTable = null\r\n                    } else {\r\n                        this.$message.error(res.msg || '下单失败')\r\n                    }\r\n                }).catch(err => {\r\n                    console.error('下单失败:', err)\r\n                    if (err.response && err.response.data && err.response.data.msg) {\r\n                        this.$message.error(err.response.data.msg)\r\n                    } else {\r\n                        this.$message.error('下单失败，请重试')\r\n                    }\r\n                }).finally(() => {\r\n                    this.buySubmitting = false\r\n                })\r\n            }).catch(() => {\r\n                this.$message.info('已取消下单')\r\n            })\r\n        },\r\n        handleCurrentChange(pageNum) {  // 分页变化\r\n            this.load(pageNum)\r\n        },\r\n\r\n        // 显示评价对话框\r\n        async showReviewDialog() {\r\n            if (!this.user || !this.user.id) {\r\n                this.$message.warning('请先登录后再评价')\r\n                this.$router.push('/login')\r\n                return\r\n            }\r\n\r\n            // 检查是否可以评价\r\n            this.checkingPermission = true\r\n            try {\r\n                const response = await this.$request.get('/foodReview/canReview', {\r\n                    params: {\r\n                        foodId: this.currentGoods.id\r\n                    }\r\n                })\r\n\r\n                if (response.code === '200') {\r\n                    if (response.data) {\r\n                        this.reviewDialogVisible = true\r\n                    } else {\r\n                        this.$message.warning('您还没有购买过此商品，无法评价')\r\n                    }\r\n                } else {\r\n                    this.$message.error(response.msg || '检查评价权限失败')\r\n                }\r\n            } catch (error) {\r\n                console.error('检查评价权限失败:', error)\r\n                this.$message.error('检查评价权限失败')\r\n            } finally {\r\n                this.checkingPermission = false\r\n            }\r\n        },\r\n\r\n        // 处理评价提交成功\r\n        handleReviewSuccess(reviewData) {\r\n            this.$message.success('评价发布成功！')\r\n            this.reviewDialogVisible = false\r\n            \r\n            // 刷新商品信息以更新评价统计\r\n            this.refreshCurrentGoods()\r\n            \r\n            // 切换到评价Tab\r\n            this.activeTab = 'reviews'\r\n        },\r\n\r\n        // 刷新当前商品信息\r\n        async refreshCurrentGoods() {\r\n            if (!this.currentGoods || !this.currentGoods.id) return\r\n            \r\n            try {\r\n                const response = await this.$request.get(`/foods/selectByIdWithReviews/${this.currentGoods.id}`)\r\n                if (response.code === '200') {\r\n                    this.currentGoods = response.data\r\n                    \r\n                    // 同时更新列表中的商品信息\r\n                    const index = this.tableData.findIndex(item => item.id === this.currentGoods.id)\r\n                    if (index > -1) {\r\n                        this.$set(this.tableData, index, { ...this.tableData[index], ...response.data })\r\n                    }\r\n                }\r\n            } catch (error) {\r\n                console.error('刷新商品信息失败:', error)\r\n            }\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 样式部分保持不变 */\r\n.taobao-container {\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 20px;\r\n}\r\n\r\n\r\n\r\n.goods-container {\r\n    display: grid;\r\n    grid-template-columns: repeat(4, 1fr);\r\n    gap: 20px;\r\n    margin-bottom: 30px;\r\n}\r\n\r\n.goods-item {\r\n    background: white;\r\n    border-radius: 8px;\r\n    overflow: hidden;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n    transition: transform 0.3s, box-shadow 0.3s;\r\n    cursor: pointer;\r\n}\r\n\r\n.goods-item:hover {\r\n    transform: translateY(-5px);\r\n    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.goods-img-container {\r\n    height: 200px;\r\n    overflow: hidden;\r\n}\r\n\r\n.goods-img {\r\n    width: 100%;\r\n    height: 100%;\r\n}\r\n\r\n.goods-info {\r\n    padding: 15px;\r\n}\r\n\r\n.goods-stock {\r\n    font-size: 12px;\r\n    color: #666;\r\n    margin: 5px 0;\r\n}\r\n\r\n.goods-stock.low-stock {\r\n    color: #E6A23C;\r\n    font-weight: bold;\r\n}\r\n\r\n.goods-stock.out-stock {\r\n    color: #F56C6C;\r\n    font-weight: bold;\r\n}\r\n\r\n.goods-title {\r\n    font-size: 14px;\r\n    color: #333;\r\n    margin-bottom: 10px;\r\n    height: 40px;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n}\r\n\r\n.goods-price {\r\n    color: #FF0036;\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n    margin-bottom: 5px;\r\n}\r\n\r\n.goods-sales {\r\n    font-size: 12px;\r\n    color: #999;\r\n}\r\n\r\n.goods-actions {\r\n    margin-top: 10px;\r\n}\r\n\r\n.pagination-container {\r\n    display: flex;\r\n    justify-content: center;\r\n    margin-top: 30px;\r\n}\r\n\r\n.detail-container {\r\n    display: flex;\r\n}\r\n\r\n.detail-left {\r\n    flex: 1;\r\n    padding: 20px;\r\n}\r\n\r\n.detail-right {\r\n    flex: 1;\r\n    padding: 20px;\r\n}\r\n\r\n.detail-img {\r\n    width: 100%;\r\n    height: 400px;\r\n}\r\n\r\n.detail-title {\r\n    font-size: 24px;\r\n    color: #333;\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.detail-price {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.price-symbol {\r\n    color: #FF0036;\r\n    font-size: 20px;\r\n}\r\n\r\n.price-number {\r\n    color: #FF0036;\r\n    font-size: 28px;\r\n    font-weight: bold;\r\n}\r\n\r\n.detail-info {\r\n    margin-bottom: 20px;\r\n}\r\n\r\n.info-item {\r\n    margin-bottom: 10px;\r\n    font-size: 14px;\r\n}\r\n\r\n.info-label {\r\n    color: #999;\r\n    margin-right: 10px;\r\n}\r\n\r\n.info-value {\r\n    color: #333;\r\n}\r\n\r\n.detail-desc {\r\n    margin-top: 30px;\r\n    border-top: 1px solid #eee;\r\n    padding-top: 20px;\r\n}\r\n\r\n.detail-desc h3 {\r\n    font-size: 16px;\r\n    color: #333;\r\n    margin-bottom: 10px;\r\n}\r\n\r\n.detail-desc p {\r\n    font-size: 14px;\r\n    color: #666;\r\n    line-height: 1.6;\r\n}\r\n\r\n.info-value.low-stock {\r\n    color: #E6A23C;\r\n}\r\n\r\n.info-value.out-stock {\r\n    color: #F56C6C;\r\n}\r\n\r\n.stock-warning {\r\n    font-weight: bold;\r\n}\r\n\r\n.detail-actions {\r\n    margin-top: 30px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n}\r\n\r\n.cart-btn {\r\n    background-color: #FF9500;\r\n    border-color: #FF9500;\r\n    color: white;\r\n    width: 48%;\r\n    height: 50px;\r\n    font-size: 18px;\r\n}\r\n\r\n.cart-btn:hover {\r\n    background-color: #FFAA33;\r\n    border-color: #FFAA33;\r\n}\r\n\r\n.buy-btn {\r\n    width: 48%;\r\n    height: 50px;\r\n    font-size: 18px;\r\n    background: linear-gradient(to right, #FF0036, #FF0036);\r\n    border: none;\r\n}\r\n\r\n/deep/ .goods-detail-dialog {\r\n    border-radius: 8px;\r\n}\r\n\r\n/deep/ .goods-detail-dialog .el-dialog__header {\r\n    border-bottom: 1px solid #eee;\r\n}\r\n\r\n/deep/ .goods-detail-dialog .el-dialog__body {\r\n    padding: 0;\r\n}\r\n\r\n/* 评价相关样式 */\r\n.rating-info {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n    margin-top: 8px;\r\n}\r\n\r\n.rating-text {\r\n    font-size: 14px;\r\n    color: #F7BA2A;\r\n    font-weight: bold;\r\n}\r\n\r\n.review-count {\r\n    font-size: 12px;\r\n    color: #999;\r\n}\r\n\r\n.detail-reviews {\r\n    margin-top: 20px;\r\n    border-top: 1px solid #eee;\r\n    padding-top: 20px;\r\n}\r\n\r\n.detail-tabs {\r\n    min-height: 300px;\r\n}\r\n\r\n.product-detail-content {\r\n    padding: 20px;\r\n    font-size: 14px;\r\n    line-height: 1.6;\r\n    color: #666;\r\n}\r\n\r\n.reviews-container {\r\n    padding: 20px 0;\r\n}\r\n\r\n.review-header {\r\n    margin-bottom: 20px;\r\n    text-align: center;\r\n}\r\n\r\n/* 响应式适配 */\r\n@media (max-width: 768px) {\r\n    /deep/ .goods-detail-dialog {\r\n        width: 95% !important;\r\n        margin: 5px auto !important;\r\n    }\r\n    \r\n    .detail-container {\r\n        flex-direction: column;\r\n    }\r\n    \r\n    .detail-left,\r\n    .detail-right {\r\n        flex: none;\r\n        width: 100%;\r\n    }\r\n    \r\n    .detail-img {\r\n        height: 250px;\r\n    }\r\n    \r\n    .detail-actions {\r\n        flex-direction: column;\r\n        gap: 10px;\r\n    }\r\n    \r\n    .cart-btn,\r\n    .buy-btn {\r\n        width: 100%;\r\n    }\r\n}\r\n</style>"], "mappings": ";AA2NA,OAAAA,WAAA;AACA,OAAAC,UAAA;AACA,OAAAC,cAAA;AACA,OAAAC,cAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAL,WAAA;IACAC,UAAA;IACAC,cAAA;IACAC;EACA;EACAG,KAAA;IACA;MACAC,SAAA;MAAA;MACAC,OAAA;MAAA;MACAC,QAAA;MAAA;MACAC,KAAA;MAAA;;MAEAC,aAAA;MAAA;MACAC,YAAA;MAAA;MACAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;MAAA;;MAEA;MACAC,qBAAA;MACAC,gBAAA;MACAC,YAAA;QACAC,OAAA;QACAC,SAAA;QACAC,UAAA;MACA;MACAC,aAAA;MAAA;;MAEA;MACAC,SAAA;MAAA;MACAC,mBAAA;MAAA;MACAC,kBAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAD,KAAArB,OAAA;MAAA;MACA,IAAAA,OAAA,OAAAA,OAAA,GAAAA,OAAA;MACA,KAAAuB,QAAA,CAAAC,GAAA;QACAC,MAAA;UACAzB,OAAA,OAAAA,OAAA;UACAC,QAAA,OAAAA;QAEA;MACA,GAAAyB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAA7B,SAAA,GAAA4B,GAAA,CAAA7B,IAAA,EAAA+B,IAAA;UACA,KAAA3B,KAAA,GAAAyB,GAAA,CAAA7B,IAAA,EAAAI,KAAA;QACA;UACA,KAAA4B,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA;IACA;IACAC,WAAAC,IAAA;MAAA;MACA;MACA,KAAA9B,YAAA,GAAA8B,IAAA;MACA,KAAA/B,aAAA;;MAEA;MACA,KAAAgC,oBAAA,CAAAD,IAAA,CAAAE,EAAA;IACA;IAEA;IACA,MAAAD,qBAAAE,MAAA;MACA;QACA,MAAAC,QAAA,cAAAf,QAAA,CAAAC,GAAA,iCAAAa,MAAA;QACA,IAAAC,QAAA,CAAAV,IAAA;UACA;UACA,KAAAxB,YAAA,GAAAkC,QAAA,CAAAxC,IAAA;QACA;UACAyC,OAAA,CAAAR,KAAA,cAAAO,QAAA,CAAAN,GAAA;QACA;MACA,SAAAD,KAAA;QACAQ,OAAA,CAAAR,KAAA,cAAAA,KAAA;MACA;IACA;IACA;IACAS,UAAA3B,OAAA;MACA,KAAAU,QAAA,CAAAkB,IAAA;QACAJ,MAAA,EAAAxB,OAAA;QACA6B,QAAA;MACA,GAAAhB,IAAA,CAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACA,KAAAE,QAAA,CAAAa,OAAA;QACA;UACA,KAAAb,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;QACA;MACA,GAAAY,KAAA,CAAAC,GAAA;QACAN,OAAA,CAAAR,KAAA,aAAAc,GAAA;QACA,IAAAA,GAAA,CAAAP,QAAA,IAAAO,GAAA,CAAAP,QAAA,CAAAxC,IAAA,IAAA+C,GAAA,CAAAP,QAAA,CAAAxC,IAAA,CAAAkC,GAAA;UACA,KAAAF,QAAA,CAAAC,KAAA,CAAAc,GAAA,CAAAP,QAAA,CAAAxC,IAAA,CAAAkC,GAAA;QACA;UACA,KAAAF,QAAA,CAAAC,KAAA;QACA;MACA;IACA;IACA;IACAe,cAAAC,KAAA;MACA,KAAAnC,YAAA;QACAC,OAAA,EAAAkC,KAAA,CAAAX,EAAA;QACAtB,SAAA,EAAAiC,KAAA,CAAAnD,IAAA;QACAmB,UAAA,EAAAgC,KAAA,CAAAC;MACA;MACA,KAAA7C,aAAA;MACA,KAAAQ,gBAAA;MACA,KAAAD,qBAAA;IACA;IAEA;IACAuC,uBAAAC,KAAA;MACA,KAAAvC,gBAAA,GAAAuC,KAAA;IACA;IAEA;IACAC,gBAAA;MACA,UAAAxC,gBAAA;QACA,KAAAmB,QAAA,CAAAsB,OAAA;QACA;MACA;;MAEA;MACA,SAAApC,aAAA;QACA,KAAAc,QAAA,CAAAsB,OAAA;QACA;MACA;MAEA,KAAAC,QAAA,aAAA1C,gBAAA,CAAA2C,WAAA,mBAAA1C,YAAA,CAAAG,UAAA;QACAwC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAA/B,IAAA;QACA,KAAAV,aAAA;;QAEA;QACA,KAAAO,QAAA,CAAAkB,IAAA;UACAJ,MAAA,OAAAzB,YAAA,CAAAC,OAAA;UACA6B,QAAA;QACA,GAAAhB,IAAA;UACA;UACA,YAAAH,QAAA,CAAAkB,IAAA;YACAa,WAAA,OAAA3C,gBAAA,CAAA2C,WAAA;YACAI,MAAA,eAAA9C,YAAA,CAAAE,SAAA;UACA;QACA,GAAAY,IAAA,CAAAC,GAAA;UACA,IAAAA,GAAA,CAAAC,IAAA;YACA,KAAAE,QAAA,CAAAa,OAAA,mBAAAhC,gBAAA,CAAA2C,WAAA;YACA,KAAA5C,qBAAA;YACA,KAAAC,gBAAA;UACA;YACA,KAAAmB,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAAK,GAAA;UACA;QACA,GAAAY,KAAA,CAAAC,GAAA;UACAN,OAAA,CAAAR,KAAA,UAAAc,GAAA;UACA,IAAAA,GAAA,CAAAP,QAAA,IAAAO,GAAA,CAAAP,QAAA,CAAAxC,IAAA,IAAA+C,GAAA,CAAAP,QAAA,CAAAxC,IAAA,CAAAkC,GAAA;YACA,KAAAF,QAAA,CAAAC,KAAA,CAAAc,GAAA,CAAAP,QAAA,CAAAxC,IAAA,CAAAkC,GAAA;UACA;YACA,KAAAF,QAAA,CAAAC,KAAA;UACA;QACA,GAAA4B,OAAA;UACA,KAAA3C,aAAA;QACA;MACA,GAAA4B,KAAA;QACA,KAAAd,QAAA,CAAA8B,IAAA;MACA;IACA;IACAC,oBAAA7D,OAAA;MAAA;MACA,KAAAqB,IAAA,CAAArB,OAAA;IACA;IAEA;IACA,MAAA8D,iBAAA;MACA,UAAAzD,IAAA,UAAAA,IAAA,CAAA+B,EAAA;QACA,KAAAN,QAAA,CAAAsB,OAAA;QACA,KAAAW,OAAA,CAAAC,IAAA;QACA;MACA;;MAEA;MACA,KAAA7C,kBAAA;MACA;QACA,MAAAmB,QAAA,cAAAf,QAAA,CAAAC,GAAA;UACAC,MAAA;YACAY,MAAA,OAAAjC,YAAA,CAAAgC;UACA;QACA;QAEA,IAAAE,QAAA,CAAAV,IAAA;UACA,IAAAU,QAAA,CAAAxC,IAAA;YACA,KAAAoB,mBAAA;UACA;YACA,KAAAY,QAAA,CAAAsB,OAAA;UACA;QACA;UACA,KAAAtB,QAAA,CAAAC,KAAA,CAAAO,QAAA,CAAAN,GAAA;QACA;MACA,SAAAD,KAAA;QACAQ,OAAA,CAAAR,KAAA,cAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA;MACA;QACA,KAAAZ,kBAAA;MACA;IACA;IAEA;IACA8C,oBAAAC,UAAA;MACA,KAAApC,QAAA,CAAAa,OAAA;MACA,KAAAzB,mBAAA;;MAEA;MACA,KAAAiD,mBAAA;;MAEA;MACA,KAAAlD,SAAA;IACA;IAEA;IACA,MAAAkD,oBAAA;MACA,UAAA/D,YAAA,UAAAA,YAAA,CAAAgC,EAAA;MAEA;QACA,MAAAE,QAAA,cAAAf,QAAA,CAAAC,GAAA,sCAAApB,YAAA,CAAAgC,EAAA;QACA,IAAAE,QAAA,CAAAV,IAAA;UACA,KAAAxB,YAAA,GAAAkC,QAAA,CAAAxC,IAAA;;UAEA;UACA,MAAAsE,KAAA,QAAArE,SAAA,CAAAsE,SAAA,CAAAnC,IAAA,IAAAA,IAAA,CAAAE,EAAA,UAAAhC,YAAA,CAAAgC,EAAA;UACA,IAAAgC,KAAA;YACA,KAAAE,IAAA,MAAAvE,SAAA,EAAAqE,KAAA;cAAA,QAAArE,SAAA,CAAAqE,KAAA;cAAA,GAAA9B,QAAA,CAAAxC;YAAA;UACA;QACA;MACA,SAAAiC,KAAA;QACAQ,OAAA,CAAAR,KAAA,cAAAA,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}