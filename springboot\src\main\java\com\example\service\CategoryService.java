package com.example.service;

import com.example.common.constants.RedisKeyConstants;
import com.example.common.enums.ResultCodeEnum;
import com.example.common.service.CacheService;
import com.example.entity.Category;
import com.example.exception.CustomException;
import com.example.mapper.CategoryMapper;
import com.example.mapper.FoodsMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

/**
 * 分类业务处理
 */
@Service
public class CategoryService {

    @Resource
    private CategoryMapper categoryMapper;

    @Resource
    private FoodsMapper foodsMapper;

    @Resource
    private CacheService cacheService;

    /**
     * 新增分类
     */
    public void add(Category category) {
        // 检查分类名称是否重复
        int count = categoryMapper.countByName(category.getName());
        if (count > 0) {
            throw new CustomException(ResultCodeEnum.CATEGORY_NAME_EXIST_ERROR);
        }
        
        // 设置默认值
        if (category.getStatus() == null) {
            category.setStatus("启用");
        }
        if (category.getSortOrder() == null) {
            category.setSortOrder(0);
        }
        
        categoryMapper.insert(category);
        
        // 清除缓存
        clearCache();
    }

    /**
     * 删除分类
     */
    public void deleteById(Integer id) {
        // 检查是否有食物使用该分类
        int foodCount = foodsMapper.countByCategoryId(id);
        if (foodCount > 0) {
            throw new CustomException("5009", "该分类下还有 " + foodCount + " 个食物，无法删除");
        }
        
        categoryMapper.deleteById(id);
        clearCache();
    }

    /**
     * 批量删除
     */
    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            deleteById(id);
        }
    }

    /**
     * 修改分类
     */
    public void updateById(Category category) {
        // 检查分类名称是否重复（排除自己）
        Category existing = categoryMapper.selectById(category.getId());
        if (existing != null && !existing.getName().equals(category.getName())) {
            int count = categoryMapper.countByName(category.getName());
            if (count > 0) {
                throw new CustomException(ResultCodeEnum.CATEGORY_NAME_EXIST_ERROR);
            }
        }
        
        categoryMapper.updateById(category);
        clearCache();
    }

    /**
     * 根据ID查询
     */
    public Category selectById(Integer id) {
        return categoryMapper.selectById(id);
    }

    /**
     * 查询所有分类
     */
    public List<Category> selectAll(Category category) {
        return categoryMapper.selectAll(category);
    }

    /**
     * 查询启用的分类（带缓存）
     */
    @SuppressWarnings("unchecked")
    public List<Category> selectEnabled() {
        String cacheKey = "category:enabled";
        try {
            Object cached = cacheService.get(cacheKey);
            if (cached instanceof List) {
                return (List<Category>) cached;
            }
        } catch (Exception e) {
            // 缓存获取失败，继续从数据库查询
        }
        
        List<Category> categories = categoryMapper.selectEnabled();
        if (categories != null && !categories.isEmpty()) {
            cacheService.set(cacheKey, categories, 3600); // 缓存1小时
        }
        return categories;
    }

    /**
     * 分页查询
     */
    public PageInfo<Category> selectPage(Category category, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Category> list = categoryMapper.selectAll(category);
        return PageInfo.of(list);
    }

    /**
     * 清除相关缓存
     */
    private void clearCache() {
        cacheService.delete("category:enabled");
    }
} 