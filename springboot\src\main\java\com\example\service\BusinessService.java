package com.example.service;

import cn.hutool.core.util.ObjectUtil;
import com.example.common.Constants;
import com.example.common.constants.RedisKeyConstants;
import com.example.common.enums.ResultCodeEnum;
import com.example.common.enums.RoleEnum;
import com.example.common.enums.StatusEnum;
import com.example.common.service.CacheService;
import com.example.entity.Account;
import com.example.entity.Business;
import com.example.exception.CustomException;
import com.example.mapper.BusinessMapper;
import com.example.utils.TokenUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商家业务处理
 **/
@Service
public class BusinessService {

    @Resource
    private BusinessMapper businessMapper;

    @Resource
    private CacheService cacheService;

    /**
     * 新增
     */
    public void add(Business business) {
        Business dbBusiness = businessMapper.selectByUsername(business.getUsername());
        if (ObjectUtil.isNotNull(dbBusiness)) {
            throw new CustomException(ResultCodeEnum.USER_EXIST_ERROR);
        }
        if (ObjectUtil.isEmpty(business.getPassword())) {
            business.setPassword(Constants.USER_DEFAULT_PASSWORD);
        }
        if (ObjectUtil.isEmpty(business.getName())) {
            business.setName(business.getUsername());
        }
        if (ObjectUtil.isEmpty(business.getStatus())) {
            business.setStatus(StatusEnum.CHECKING.status);
        }
        business.setRole(RoleEnum.BUSINESS.name());
        businessMapper.insert(business);
    }

    /**
     * 删除
     */
    public void deleteById(Integer id) {
        businessMapper.deleteById(id);
        // 删除缓存
        cacheService.delete(RedisKeyConstants.BUSINESS_INFO_PREFIX + id);
    }

    /**
     * 批量删除
     */
    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            deleteById(id);
        }
    }

    /**
     * 修改
     */
    public void updateById(Business business) {
        businessMapper.updateById(business);
        // 更新缓存
        if (business.getId() != null) {
            cacheService.set(RedisKeyConstants.BUSINESS_INFO_PREFIX + business.getId(), business, RedisKeyConstants.ExpireTime.USER_INFO);
            // 如果用户名发生变化，删除用户名缓存
            if (business.getUsername() != null) {
                cacheService.delete(RedisKeyConstants.BUSINESS_BY_USERNAME_PREFIX + business.getUsername());
            }
        }
    }

    /**
     * 根据ID查询（带缓存）
     */
    public Business selectById(Integer id) {
        if (id == null) {
            return null;
        }
        
        // 先从缓存获取
        String cacheKey = RedisKeyConstants.BUSINESS_INFO_PREFIX + id;
        Business business = cacheService.get(cacheKey, Business.class);
        if (business != null) {
            return business;
        }
        
        // 缓存未命中，从数据库查询
        business = businessMapper.selectById(id);
        if (business != null) {
            // 写入缓存
            cacheService.set(cacheKey, business, RedisKeyConstants.ExpireTime.USER_INFO);
        }
        return business;
    }

    /**
     * 查询所有
     */
    public List<Business> selectAll(Business business) {
        return businessMapper.selectAll(business);
    }

    /**
     * 分页查询
     */
    public PageInfo<Business> selectPage(Business business, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Business> list = businessMapper.selectAll(business);
        return PageInfo.of(list);
    }

    /**
     * 登录
     */
    public Account login(Account account) {
        Account dbBusiness = businessMapper.selectByUsername(account.getUsername());
        if (ObjectUtil.isNull(dbBusiness)) {
            throw new CustomException(ResultCodeEnum.USER_NOT_EXIST_ERROR);
        }
        if (!account.getPassword().equals(dbBusiness.getPassword())) {
            throw new CustomException(ResultCodeEnum.USER_ACCOUNT_ERROR);
        }
        // 生成token
        String tokenData = dbBusiness.getId() + "-" + RoleEnum.BUSINESS.name();
        String token = TokenUtils.createToken(tokenData, dbBusiness.getPassword());
        dbBusiness.setToken(token);
        
        // 缓存商家信息
        cacheService.set(RedisKeyConstants.BUSINESS_INFO_PREFIX + dbBusiness.getId(), dbBusiness, RedisKeyConstants.ExpireTime.USER_INFO);
        
        return dbBusiness;
    }

    /**
     * 注册
     */
    public void register(Account account) {
        Business business = new Business();
        BeanUtils.copyProperties(account, business);
        add(business);
    }

    /**
     * 修改密码
     */
    public void updatePassword(Account account) {
        Business dbBusiness = businessMapper.selectByUsername(account.getUsername());
        if (ObjectUtil.isNull(dbBusiness)) {
            throw new CustomException(ResultCodeEnum.USER_NOT_EXIST_ERROR);
        }
        if (!account.getPassword().equals(dbBusiness.getPassword())) {
            throw new CustomException(ResultCodeEnum.PARAM_PASSWORD_ERROR);
        }
        dbBusiness.setPassword(account.getNewPassword());
        businessMapper.updateById(dbBusiness);
        
        // 清除缓存，强制重新加载
        cacheService.delete(RedisKeyConstants.BUSINESS_INFO_PREFIX + dbBusiness.getId());
        cacheService.delete(RedisKeyConstants.BUSINESS_BY_USERNAME_PREFIX + dbBusiness.getUsername());
    }

    /**
     * 根据用户名查询（带缓存）
     */
    public Business selectByUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            return null;
        }
        
        // 先从缓存获取
        String cacheKey = RedisKeyConstants.BUSINESS_BY_USERNAME_PREFIX + username;
        Business business = cacheService.get(cacheKey, Business.class);
        if (business != null) {
            return business;
        }
        
        // 缓存未命中，从数据库查询
        business = businessMapper.selectByUsername(username);
        if (business != null) {
            // 写入缓存
            cacheService.set(cacheKey, business, RedisKeyConstants.ExpireTime.USERNAME_QUERY);
        }
        return business;
    }

    /**
     * 根据手机号查询
     */
    public Business selectByPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return null;
        }
        return businessMapper.selectByPhone(phone);
    }

    /**
     * 手机号登录
     */
    public Account loginByPhone(String phone) {
        Business dbBusiness = selectByPhone(phone);
        if (ObjectUtil.isNull(dbBusiness)) {
            throw new CustomException(ResultCodeEnum.USER_NOT_EXIST_ERROR);
        }
        
        // 生成token
        String tokenData = dbBusiness.getId() + "-" + RoleEnum.BUSINESS.name();
        String token = TokenUtils.createToken(tokenData, dbBusiness.getPassword());
        dbBusiness.setToken(token);
        
        // 缓存商家信息
        cacheService.set(RedisKeyConstants.BUSINESS_INFO_PREFIX + dbBusiness.getId(), dbBusiness, RedisKeyConstants.ExpireTime.USER_INFO);
        
        return dbBusiness;
    }

    /**
     * 通过手机号修改密码
     */
    public void updatePasswordByPhone(Account account) {
        Business dbBusiness = selectByPhone(account.getPhone());
        if (ObjectUtil.isNull(dbBusiness)) {
            throw new CustomException(ResultCodeEnum.USER_NOT_EXIST_ERROR);
        }
        
        // 验证用户名是否匹配
        if (!account.getUsername().equals(dbBusiness.getUsername())) {
            throw new CustomException("400", "用户名与手机号不匹配");
        }
        
        dbBusiness.setPassword(account.getNewPassword());
        businessMapper.updateById(dbBusiness);
        
        // 清除缓存，强制重新加载
        cacheService.delete(RedisKeyConstants.BUSINESS_INFO_PREFIX + dbBusiness.getId());
        cacheService.delete(RedisKeyConstants.BUSINESS_BY_USERNAME_PREFIX + dbBusiness.getUsername());
    }

}