package com.example.service;

import com.example.common.Result;
import com.example.common.enums.ResultCodeEnum;
import com.example.entity.FoodReview;
import com.example.entity.FoodReviewStats;
import com.example.entity.Foods;
import com.example.entity.User;
import com.example.exception.CustomException;
import com.example.mapper.FoodReviewMapper;
import com.example.mapper.FoodsMapper;
import com.example.mapper.UserMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 菜品评价业务处理
 */
@Service
public class FoodReviewService {

    @Resource
    private FoodReviewMapper foodReviewMapper;
    
    @Resource
    private FoodsMapper foodsMapper;
    
    @Resource
    private UserMapper userMapper;

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 评价权限验证
     */
    public boolean canUserReviewFood(Integer userId, Integer foodId) {
        // 检查用户是否购买过该菜品
        int purchaseCount = foodReviewMapper.checkUserPurchased(userId, foodId);
        if (purchaseCount == 0) {
            return false;
        }
        
        // 检查用户是否已经评价过该菜品
        int reviewCount = foodReviewMapper.checkUserReviewExists(userId, foodId);
        return reviewCount == 0;
    }

    /**
     * 添加评价
     */
    @Transactional(rollbackFor = Exception.class)
    public Result addReview(FoodReview review) {
        try {
            // 验证用户是否有评价权限
            if (!canUserReviewFood(review.getUserId(), review.getFoodId())) {
                return Result.error(ResultCodeEnum.PARAM_ERROR.code, "您还未购买该菜品或已经评价过了");
            }

            // 验证评分范围
            if (review.getRating() == null || review.getRating() < 1 || review.getRating() > 5) {
                return Result.error(ResultCodeEnum.PARAM_ERROR.code, "评分必须在1-5星之间");
            }

            // 获取用户信息
            User user = userMapper.selectById(review.getUserId());
            if (user != null) {
                review.setUserName(user.getName());
            }

            // 获取菜品信息
            Foods food = foodsMapper.selectById(review.getFoodId());
            if (food != null) {
                review.setFoodName(food.getName());
            }

            // 设置默认值
            if (review.getStatus() == null) {
                review.setStatus("正常");
            }
            if (review.getIsAnonymous() == null) {
                review.setIsAnonymous(0);
            }
            if (review.getHelpfulCount() == null) {
                review.setHelpfulCount(0);
            }
            review.setCreateTime(dateFormat.format(new Date()));

            // 插入评价
            foodReviewMapper.insert(review);

            // 更新菜品评价统计信息
            updateFoodReviewStats(review.getFoodId());

            return Result.success("评价提交成功");
        } catch (Exception e) {
            throw new CustomException(ResultCodeEnum.SYSTEM_ERROR.code, "评价提交失败：" + e.getMessage());
        }
    }

    /**
     * 获取菜品评价列表（分页）
     */
    public Result getReviewsByFoodId(Integer foodId, Integer pageNum, Integer pageSize) {
        return getReviewsByFoodId(foodId, pageNum, pageSize, null);
    }

    /**
     * 获取菜品评价列表（分页，支持评分筛选）
     */
    public Result getReviewsByFoodId(Integer foodId, Integer pageNum, Integer pageSize, Integer rating) {
        if (pageNum == null) pageNum = 1;
        if (pageSize == null) pageSize = 10;

        Map<String, Object> params = new HashMap<>();
        params.put("foodId", foodId);
        params.put("rating", rating);
        params.put("limit", pageSize);
        params.put("offset", (pageNum - 1) * pageSize);

        List<FoodReview> reviewList = foodReviewMapper.selectByFoodId(params);
        
        // 获取总数用于分页信息
        int total = foodReviewMapper.countByFoodId(foodId);
        
        // 构造分页信息
        PageInfo<FoodReview> pageInfo = new PageInfo<>();
        pageInfo.setList(reviewList);
        pageInfo.setTotal(total);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPages((total + pageSize - 1) / pageSize);

        return Result.success(pageInfo);
    }

    /**
     * 获取用户评价列表
     */
    public Result getReviewsByUserId(Integer userId, Integer pageNum, Integer pageSize) {
        if (pageNum == null) pageNum = 1;
        if (pageSize == null) pageSize = 10;

        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("limit", pageSize);
        params.put("offset", (pageNum - 1) * pageSize);

        List<FoodReview> reviewList = foodReviewMapper.selectByUserId(params);
        
        // 获取总数用于分页信息
        int total = foodReviewMapper.countByUserId(userId);
        
        // 构造分页信息
        PageInfo<FoodReview> pageInfo = new PageInfo<>();
        pageInfo.setList(reviewList);
        pageInfo.setTotal(total);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPages((total + pageSize - 1) / pageSize);

        return Result.success(pageInfo);
    }

    /**
     * 获取当前用户的评价列表（支持筛选）
     */
    public Result getMyReviews(Integer userId, Integer pageNum, Integer pageSize, String rating, String status, String foodName) {
        if (pageNum == null) pageNum = 1;
        if (pageSize == null) pageSize = 10;

        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("limit", pageSize);
        params.put("offset", (pageNum - 1) * pageSize);
        
        // 添加筛选条件
        if (rating != null && !rating.isEmpty()) {
            params.put("rating", Integer.parseInt(rating));
        }
        if (status != null && !status.isEmpty()) {
            params.put("status", status);
        }
        if (foodName != null && !foodName.isEmpty()) {
            params.put("foodName", foodName);
        }

        List<FoodReview> reviewList = foodReviewMapper.selectByUserId(params);
        
        // 获取总数用于分页信息（同样需要应用筛选条件）
        int total = foodReviewMapper.countByUserIdWithFilter(params);
        
        // 构造分页信息
        PageInfo<FoodReview> pageInfo = new PageInfo<>();
        pageInfo.setList(reviewList);
        pageInfo.setTotal(total);
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPages((total + pageSize - 1) / pageSize);

        return Result.success(pageInfo);
    }

    /**
     * 删除评价（管理员）
     */
    @Transactional(rollbackFor = Exception.class)
    public Result deleteReview(Integer reviewId) {
        try {
            FoodReview review = foodReviewMapper.selectById(reviewId);
            if (review == null) {
                return Result.error(ResultCodeEnum.PARAM_ERROR.code, "评价不存在");
            }

            Integer foodId = review.getFoodId();
            foodReviewMapper.deleteById(reviewId);

            // 更新菜品评价统计信息
            updateFoodReviewStats(foodId);

            return Result.success("删除成功");
        } catch (Exception e) {
            throw new CustomException(ResultCodeEnum.SYSTEM_ERROR.code, "删除失败：" + e.getMessage());
        }
    }

    /**
     * 更新评价状态
     */
    public Result updateReviewStatus(Integer reviewId, String status) {
        try {
            FoodReview review = foodReviewMapper.selectById(reviewId);
            if (review == null) {
                return Result.error(ResultCodeEnum.PARAM_ERROR.code, "评价不存在");
            }

            foodReviewMapper.updateStatus(reviewId, status);

            // 如果状态变为正常或已删除，需要更新统计信息
            if ("正常".equals(status) || "已删除".equals(status)) {
                updateFoodReviewStats(review.getFoodId());
            }

            return Result.success("状态更新成功");
        } catch (Exception e) {
            throw new CustomException(ResultCodeEnum.SYSTEM_ERROR.code, "状态更新失败：" + e.getMessage());
        }
    }

    /**
     * 获取评价统计信息
     */
    public Result getReviewStats(Integer foodId) {
        try {
            FoodReviewStats stats = foodReviewMapper.getReviewStats(foodId);
            return Result.success(stats);
        } catch (Exception e) {
            throw new CustomException(ResultCodeEnum.SYSTEM_ERROR.code, "获取统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 管理员查看所有评价
     */
    public Result getAllReviews(Integer pageNum, Integer pageSize, String status, String foodName, String userName, Integer rating) {
        if (pageNum == null) pageNum = 1;
        if (pageSize == null) pageSize = 10;

        PageHelper.startPage(pageNum, pageSize);
        
        FoodReview queryParam = new FoodReview();
        queryParam.setStatus(status);
        queryParam.setFoodName(foodName);
        queryParam.setUserName(userName);
        queryParam.setRating(rating);
        
        List<FoodReview> reviewList = foodReviewMapper.selectAll(queryParam);
        PageInfo<FoodReview> pageInfo = new PageInfo<>(reviewList);

        return Result.success(pageInfo);
    }

    /**
     * 管理员回复评价
     */
    @Transactional(rollbackFor = Exception.class)
    public Result replyReview(Integer reviewId, String replyContent, String replyUser) {
        try {
            FoodReview review = foodReviewMapper.selectById(reviewId);
            if (review == null) {
                return Result.error(ResultCodeEnum.PARAM_ERROR.code, "评价不存在");
            }

            review.setReplyContent(replyContent);
            review.setReplyTime(dateFormat.format(new Date()));
            review.setReplyUser(replyUser);

            foodReviewMapper.updateById(review);

            return Result.success("回复成功");
        } catch (Exception e) {
            throw new CustomException(ResultCodeEnum.SYSTEM_ERROR.code, "回复失败：" + e.getMessage());
        }
    }

    /**
     * 检查评价权限
     */
    public Result checkReviewPermission(Integer userId, Integer foodId) {
        boolean canReview = canUserReviewFood(userId, foodId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("canReview", canReview);
        
        if (!canReview) {
            // 检查具体原因
            int purchaseCount = foodReviewMapper.checkUserPurchased(userId, foodId);
            if (purchaseCount == 0) {
                result.put("reason", "您还未购买该菜品");
            } else {
                result.put("reason", "您已经评价过该菜品");
            }
        }
        
        return Result.success(result);
    }

    /**
     * 获取用户对菜品的评价
     */
    public Result getUserFoodReview(Integer userId, Integer foodId) {
        FoodReview review = foodReviewMapper.getUserFoodReview(userId, foodId);
        return Result.success(review);
    }

    /**
     * 批量删除评价
     */
    @Transactional(rollbackFor = Exception.class)
    public Result deleteBatch(List<Integer> ids) {
        try {
            // 先获取这些评价关联的菜品ID，用于后续更新统计
            Map<Integer, Boolean> foodIdsToUpdate = new HashMap<>();
            for (Integer id : ids) {
                FoodReview review = foodReviewMapper.selectById(id);
                if (review != null) {
                    foodIdsToUpdate.put(review.getFoodId(), true);
                }
            }

            // 批量删除
            foodReviewMapper.deleteByIds(ids);

            // 更新相关菜品的统计信息
            for (Integer foodId : foodIdsToUpdate.keySet()) {
                updateFoodReviewStats(foodId);
            }

            return Result.success("批量删除成功");
        } catch (Exception e) {
            throw new CustomException(ResultCodeEnum.SYSTEM_ERROR.code, "批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询评价
     */
    public FoodReview selectById(Integer id) {
        return foodReviewMapper.selectById(id);
    }

    /**
     * 修改评价
     */
    @Transactional(rollbackFor = Exception.class)
    public Result updateReview(FoodReview foodReview) {
        try {
            FoodReview existingReview = foodReviewMapper.selectById(foodReview.getId());
            if (existingReview == null) {
                return Result.error(ResultCodeEnum.PARAM_ERROR.code, "评价不存在");
            }

            // 更新评价
            foodReview.setUpdateTime(dateFormat.format(new Date()));
            foodReviewMapper.updateById(foodReview);

            // 如果评分发生变化，需要更新统计信息
            if (foodReview.getRating() != null && !foodReview.getRating().equals(existingReview.getRating())) {
                updateFoodReviewStats(existingReview.getFoodId());
            }

            return Result.success("修改成功");
        } catch (Exception e) {
            throw new CustomException(ResultCodeEnum.SYSTEM_ERROR.code, "修改失败：" + e.getMessage());
        }
    }

    /**
     * 更新菜品评价统计信息（内部方法）
     */
    private void updateFoodReviewStats(Integer foodId) {
        try {
            FoodReviewStats stats = foodReviewMapper.getReviewStats(foodId);
            if (stats != null) {
                Double avgRating = stats.getAvgRating();
                Integer reviewCount = stats.getTotalReviews();
                String lastReviewTime = stats.getLatestReviewTime();
                
                // 更新菜品表中的统计信息
                foodReviewMapper.updateFoodReviewStats(foodId, avgRating, reviewCount, lastReviewTime);
            }
        } catch (Exception e) {
            // 记录日志，但不影响主流程
            System.err.println("更新菜品评价统计信息失败：" + e.getMessage());
        }
    }
} 