package com.example.service;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.example.common.config.SmsConfig;
import com.example.common.constants.RedisKeyConstants;
import com.example.common.service.CacheService;
import com.example.exception.CustomException;
import com.example.utils.HttpUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.http.HttpResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 短信服务类
 */
@Service
public class SmsService {

    private static final Logger log = LoggerFactory.getLogger(SmsService.class);

    @Resource
    private SmsConfig smsConfig;

    @Resource
    private CacheService cacheService;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 发送验证码短信
     */
    public boolean sendVerifyCode(String phoneNumber) {
        // 检查发送间隔
        String intervalKey = RedisKeyConstants.SMS_SEND_INTERVAL_PREFIX + phoneNumber;
        if (cacheService.hasKey(intervalKey)) {
            throw new CustomException("400", "发送过于频繁，请稍后再试");
        }

        try {
            // 生成验证码
            String verifyCode = RandomUtil.randomNumbers(smsConfig.getCodeLength());
            
            // 调用短信API
            boolean success = callSmsApi(phoneNumber, verifyCode);
            
            if (success) {
                // 存储验证码到Redis
                String codeKey = RedisKeyConstants.SMS_VERIFY_CODE_PREFIX + phoneNumber;
                cacheService.set(codeKey, verifyCode, smsConfig.getCodeExpireTime());
                
                // 设置发送间隔限制
                cacheService.set(intervalKey, "1", smsConfig.getSendInterval());
                
                log.info("短信验证码发送成功，手机号：{}", phoneNumber);
                return true;
            } else {
                log.error("短信验证码发送失败，手机号：{}", phoneNumber);
                return false;
            }
        } catch (Exception e) {
            log.error("发送短信验证码异常，手机号：{}", phoneNumber, e);
            throw new CustomException("500", "短信发送失败，请稍后重试");
        }
    }

    /**
     * 验证验证码
     */
    public boolean verifyCode(String phoneNumber, String code) {
        if (StrUtil.isBlank(phoneNumber) || StrUtil.isBlank(code)) {
            return false;
        }

        try {
            String codeKey = RedisKeyConstants.SMS_VERIFY_CODE_PREFIX + phoneNumber;
            String cachedCode = cacheService.get(codeKey, String.class);
            
            if (StrUtil.isBlank(cachedCode)) {
                return false; // 验证码已过期或不存在
            }

            boolean isValid = code.equals(cachedCode);
            
            if (isValid) {
                // 验证成功，删除验证码
                cacheService.delete(codeKey);
                log.info("验证码验证成功，手机号：{}", phoneNumber);
            } else {
                log.warn("验证码验证失败，手机号：{}，输入的验证码：{}", phoneNumber, code);
            }
            
            return isValid;
        } catch (Exception e) {
            log.error("验证码验证异常，手机号：{}", phoneNumber, e);
            return false;
        }
    }

    /**
     * 调用短信API
     */
    private boolean callSmsApi(String phoneNumber, String verifyCode) throws Exception {
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "APPCODE " + smsConfig.getAppCode());
        headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");

        Map<String, String> bodys = new HashMap<>();
        bodys.put("content", "code:" + verifyCode);
        bodys.put("template_id", smsConfig.getTemplateId());
        bodys.put("phone_number", phoneNumber);

        Map<String, String> querys = new HashMap<>();

        try {
            HttpResponse response = HttpUtils.doPost(smsConfig.getHost(), smsConfig.getPath(), 
                    "POST", headers, querys, bodys);
            
            String result = HttpUtils.getResponseContent(response);
            log.info("短信API响应：{}", result);

            // 解析响应结果
            if (StrUtil.isNotBlank(result)) {
                JsonNode jsonNode = objectMapper.readTree(result);
                String status = jsonNode.has("status") ? jsonNode.get("status").asText() : "";
                return "OK".equals(status);
            }
            
            return false;
        } catch (Exception e) {
            log.error("调用短信API异常", e);
            throw e;
        }
    }

    /**
     * 获取验证码剩余有效时间（秒）
     */
    public long getCodeRemainTime(String phoneNumber) {
        String codeKey = RedisKeyConstants.SMS_VERIFY_CODE_PREFIX + phoneNumber;
        return cacheService.getExpire(codeKey);
    }

    /**
     * 获取发送间隔剩余时间（秒）
     */
    public long getSendIntervalRemainTime(String phoneNumber) {
        String intervalKey = RedisKeyConstants.SMS_SEND_INTERVAL_PREFIX + phoneNumber;
        return cacheService.getExpire(intervalKey);
    }
} 