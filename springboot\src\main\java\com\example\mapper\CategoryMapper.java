package com.example.mapper;

import com.example.entity.Category;
import java.util.List;

public interface CategoryMapper {
    
    /**
     * 新增分类
     */
    int insert(Category category);
    
    /**
     * 根据ID删除分类
     */
    int deleteById(Integer id);
    
    /**
     * 根据ID更新分类
     */
    int updateById(Category category);
    
    /**
     * 根据ID查询分类
     */
    Category selectById(Integer id);
    
    /**
     * 查询所有分类
     */
    List<Category> selectAll(Category category);
    
    /**
     * 查询启用状态的分类
     */
    List<Category> selectEnabled();
    
    /**
     * 根据状态查询分类
     */
    List<Category> selectByStatus(String status);
    
    /**
     * 检查分类名称是否重复
     */
    int countByName(String name);
} 