package com.example.entity;

/**
 * 食品表
 */
public class Foods {
    /** 主键ID */
    private Integer id;
    /** 食品名称 */
    private String name;
    /** 食品图片 */
    private String sfImage;
    /** 食品描述 */
    private String sfDescription;
    /** 食品类型（保留兼容性） */
    private String sfCategory;
    /** 分类ID */
    private Integer categoryId;
    /** 分类对象（用于关联查询） */
    private Category category;
    /** 分类名称（用于显示） */
    private String categoryName;
    /** 食品价格 */
    private String sfPrice;
    /** 库存数量 */
    private String sfStock;
    /** 上架状态 */
    private String sfShelfStatus;
    /** 平均评分 */
    private Double averageRating;
    /** 评价总数 */
    private Integer reviewCount;
    /** 最新评价时间 */
    private String lastReviewTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    
    public String getSfImage() {
        return sfImage;
    }

    public void setSfImage(String sfImage) {
        this.sfImage = sfImage;
    }
    
    public String getSfDescription() {
        return sfDescription;
    }

    public void setSfDescription(String sfDescription) {
        this.sfDescription = sfDescription;
    }
    
    public String getSfCategory() {
        return sfCategory;
    }

    public void setSfCategory(String sfCategory) {
        this.sfCategory = sfCategory;
    }
    
    public String getSfPrice() {
        return sfPrice;
    }

    public void setSfPrice(String sfPrice) {
        this.sfPrice = sfPrice;
    }
    
    public String getSfStock() {
        return sfStock;
    }

    public void setSfStock(String sfStock) {
        this.sfStock = sfStock;
    }
    
    public String getSfShelfStatus() {
        return sfShelfStatus;
    }

    public void setSfShelfStatus(String sfShelfStatus) {
        this.sfShelfStatus = sfShelfStatus;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public Category getCategory() {
        return category;
    }

    public void setCategory(Category category) {
        this.category = category;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Double getAverageRating() {
        return averageRating;
    }

    public void setAverageRating(Double averageRating) {
        this.averageRating = averageRating;
    }

    public Integer getReviewCount() {
        return reviewCount;
    }

    public void setReviewCount(Integer reviewCount) {
        this.reviewCount = reviewCount;
    }

    public String getLastReviewTime() {
        return lastReviewTime;
    }

    public void setLastReviewTime(String lastReviewTime) {
        this.lastReviewTime = lastReviewTime;
    }
}