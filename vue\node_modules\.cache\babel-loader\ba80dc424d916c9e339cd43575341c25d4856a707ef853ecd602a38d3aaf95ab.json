{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/esnext.iterator.constructor.js\";\nimport \"core-js/modules/esnext.iterator.filter.js\";\nimport StarRating from '@/components/StarRating.vue';\nimport FoodReviewForm from '@/components/FoodReviewForm.vue';\nimport FoodReviewList from '@/components/FoodReviewList.vue';\nexport default {\n  name: 'MyReviews',\n  components: {\n    StarRating,\n    FoodReviewForm,\n    FoodReviewList\n  },\n  data() {\n    return {\n      reviewList: [],\n      loading: false,\n      currentPage: 1,\n      pageSize: 10,\n      totalCount: 0,\n      totalReviews: 0,\n      // 搜索表单\n      searchForm: {\n        rating: '',\n        status: '',\n        foodName: ''\n      },\n      // 编辑相关\n      editDialogVisible: false,\n      editingReview: null,\n      // 商品详情弹窗相关\n      detailVisible: false,\n      currentGoods: null,\n      activeTab: 'detail',\n      reviewDialogVisible: false,\n      checkingPermission: false,\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}')\n    };\n  },\n  created() {\n    this.loadReviews();\n  },\n  methods: {\n    // 加载评价列表\n    async loadReviews() {\n      if (this.loading) return;\n      this.loading = true;\n      try {\n        const params = {\n          pageNum: this.currentPage,\n          pageSize: this.pageSize,\n          ...this.searchForm\n        };\n        const response = await this.$request.get('/foodReview/my', {\n          params\n        });\n        if (response.code === '200') {\n          this.reviewList = response.data?.list || [];\n          this.totalCount = response.data?.total || 0;\n          this.totalReviews = response.data?.total || 0;\n        } else {\n          this.$message.error(response.msg || '加载评价列表失败');\n        }\n      } catch (error) {\n        console.error('加载评价列表失败:', error);\n        this.$message.error('加载评价列表失败');\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 搜索\n    handleSearch() {\n      this.currentPage = 1;\n      this.loadReviews();\n    },\n    // 重置搜索\n    resetSearch() {\n      this.searchForm = {\n        rating: '',\n        status: '',\n        foodName: ''\n      };\n      this.handleSearch();\n    },\n    // 分页变化\n    handlePageChange(page) {\n      this.currentPage = page;\n      this.loadReviews();\n    },\n    // 查看商品\n    viewFood(foodId) {\n      this.loadGoodsDetail(foodId);\n    },\n    // 加载商品详情\n    async loadGoodsDetail(foodId) {\n      try {\n        const response = await this.$request.get(`/foods/selectByIdWithReviews/${foodId}`);\n        if (response.code === '200') {\n          this.currentGoods = response.data;\n          this.detailVisible = true;\n          this.activeTab = 'detail';\n        } else {\n          this.$message.error(response.msg || '获取商品详情失败');\n        }\n      } catch (error) {\n        console.error('获取商品详情失败:', error);\n        this.$message.error('获取商品详情失败');\n      }\n    },\n    // 显示评价对话框\n    async showReviewDialog() {\n      if (!this.user || !this.user.id) {\n        this.$message.warning('请先登录后再评价');\n        this.$router.push('/login');\n        return;\n      }\n\n      // 检查是否可以评价\n      this.checkingPermission = true;\n      try {\n        const response = await this.$request.get('/foodReview/canReview', {\n          params: {\n            foodId: this.currentGoods.id\n          }\n        });\n        if (response.code === '200') {\n          if (response.data) {\n            this.reviewDialogVisible = true;\n          } else {\n            this.$message.warning('您还没有购买过此商品，无法评价');\n          }\n        } else {\n          this.$message.error(response.msg || '检查评价权限失败');\n        }\n      } catch (error) {\n        console.error('检查评价权限失败:', error);\n        this.$message.error('检查评价权限失败');\n      } finally {\n        this.checkingPermission = false;\n      }\n    },\n    // 处理评价提交成功\n    handleReviewSuccess(reviewData) {\n      this.$message.success('评价发布成功！');\n      this.reviewDialogVisible = false;\n\n      // 刷新商品信息以更新评价统计\n      this.refreshCurrentGoods();\n\n      // 切换到评价Tab\n      this.activeTab = 'reviews';\n\n      // 刷新我的评价列表\n      this.loadReviews();\n    },\n    // 刷新当前商品信息\n    async refreshCurrentGoods() {\n      if (!this.currentGoods || !this.currentGoods.id) return;\n      try {\n        const response = await this.$request.get(`/foods/selectByIdWithReviews/${this.currentGoods.id}`);\n        if (response.code === '200') {\n          this.currentGoods = response.data;\n        }\n      } catch (error) {\n        console.error('刷新商品信息失败:', error);\n      }\n    },\n    // 处理Tab切换\n    handleTabChange(tab) {\n      const tabName = tab.name || tab;\n      if (tabName === 'reviews' && this.currentGoods && this.currentGoods.id) {\n        // 切换到评价Tab时，确保评价列表正确加载\n        this.$nextTick(() => {\n          if (this.$refs.reviewList) {\n            this.$refs.reviewList.refresh();\n          }\n        });\n      }\n    },\n    // 编辑评价\n    editReview(review) {\n      this.editingReview = review;\n      this.editDialogVisible = true;\n\n      // 等待对话框打开后设置表单数据\n      this.$nextTick(() => {\n        if (this.$refs.editForm) {\n          this.$refs.editForm.setFormData({\n            rating: review.rating,\n            content: review.content,\n            images: review.images,\n            isAnonymous: review.isAnonymous\n          });\n        }\n      });\n    },\n    // 编辑成功\n    handleEditSuccess() {\n      this.$message.success('评价修改成功！');\n      this.editDialogVisible = false;\n      this.loadReviews(); // 重新加载列表\n    },\n    // 删除评价\n    deleteReview(review) {\n      this.$confirm(`确定要删除对\"${review.foodName}\"的评价吗？`, '确认删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async () => {\n        try {\n          const response = await this.$request.delete(`/foodReview/${review.id}`);\n          if (response.code === '200') {\n            this.$message.success('评价删除成功！');\n            this.loadReviews(); // 重新加载列表\n          } else {\n            this.$message.error(response.msg || '删除失败');\n          }\n        } catch (error) {\n          console.error('删除评价失败:', error);\n          this.$message.error('删除失败，请稍后重试');\n        }\n      });\n    },\n    // 去购买商品\n    goToFoods() {\n      this.$router.push('/front/foods');\n    },\n    // 判断是否可以编辑\n    canEdit(review) {\n      // 只有正常状态的评价可以编辑，且在发布后24小时内\n      if (review.status !== '正常') return false;\n      const createTime = new Date(review.createTime);\n      const now = new Date();\n      const hoursDiff = (now - createTime) / (1000 * 60 * 60);\n      return hoursDiff <= 24; // 24小时内可以编辑\n    },\n    // 判断是否可以删除\n    canDelete(review) {\n      // 用户可以删除自己的评价（除了已删除状态）\n      return review.status !== '已删除';\n    },\n    // 获取状态类型\n    getStatusType(status) {\n      const typeMap = {\n        '正常': 'success',\n        '待审核': 'warning',\n        '已删除': 'danger'\n      };\n      return typeMap[status] || 'info';\n    },\n    // 获取图片列表\n    getImageList(images) {\n      if (!images) return [];\n      return images.split(',').filter(img => img.trim());\n    },\n    // 格式化时间\n    formatTime(time) {\n      if (!time) return '';\n      const date = new Date(time);\n      const now = new Date();\n      const diff = now - date;\n\n      // 小于1分钟\n      if (diff < 60 * 1000) {\n        return '刚刚';\n      }\n\n      // 小于1小时\n      if (diff < 60 * 60 * 1000) {\n        return `${Math.floor(diff / (60 * 1000))}分钟前`;\n      }\n\n      // 小于1天\n      if (diff < 24 * 60 * 60 * 1000) {\n        return `${Math.floor(diff / (60 * 60 * 1000))}小时前`;\n      }\n\n      // 小于7天\n      if (diff < 7 * 24 * 60 * 60 * 1000) {\n        return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`;\n      }\n\n      // 超过7天显示具体日期\n      return date.toLocaleDateString();\n    },\n    // 格式化库存显示\n    formatStock(stock) {\n      if (!stock && stock !== 0) {\n        return '未知';\n      }\n      const stockNum = parseInt(stock) || 0;\n      if (stockNum <= 0) {\n        return '缺货';\n      } else if (stockNum <= 10) {\n        return `剩余 ${stockNum} 件`;\n      } else {\n        return `库存充足 (${stockNum}件)`;\n      }\n    },\n    // 获取库存状态样式类\n    getStockClass(stock) {\n      const stockNum = parseInt(stock) || 0;\n      if (stockNum <= 0) {\n        return 'stock-out';\n      } else if (stockNum <= 10) {\n        return 'stock-low';\n      } else {\n        return 'stock-normal';\n      }\n    },\n    // 格式化上架状态显示\n    formatShelfStatus(status) {\n      if (!status) {\n        return '未知';\n      }\n      // 根据常见的状态值进行格式化\n      switch (status.toString().toLowerCase()) {\n        case '1':\n        case 'on':\n        case 'online':\n        case '上架':\n        case 'enabled':\n          return '已上架';\n        case '0':\n        case 'off':\n        case 'offline':\n        case '下架':\n        case 'disabled':\n          return '已下架';\n        default:\n          return status;\n      }\n    },\n    // 获取上架状态样式类\n    getStatusClass(status) {\n      if (!status) {\n        return 'status-unknown';\n      }\n      switch (status.toString().toLowerCase()) {\n        case '1':\n        case 'on':\n        case 'online':\n        case '上架':\n        case 'enabled':\n          return 'status-online';\n        case '0':\n        case 'off':\n        case 'offline':\n        case '下架':\n        case 'disabled':\n          return 'status-offline';\n        default:\n          return 'status-unknown';\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["StarRating", "FoodReviewForm", "FoodReviewList", "name", "components", "data", "reviewList", "loading", "currentPage", "pageSize", "totalCount", "totalReviews", "searchForm", "rating", "status", "foodName", "editDialogVisible", "editingReview", "detailVisible", "currentGoods", "activeTab", "reviewDialogVisible", "checkingPermission", "user", "JSON", "parse", "localStorage", "getItem", "created", "loadReviews", "methods", "params", "pageNum", "response", "$request", "get", "code", "list", "total", "$message", "error", "msg", "console", "handleSearch", "resetSearch", "handlePageChange", "page", "viewFood", "foodId", "loadGoodsDetail", "showReviewDialog", "id", "warning", "$router", "push", "handleReviewSuccess", "reviewData", "success", "refreshCurrentGoods", "handleTabChange", "tab", "tabName", "$nextTick", "$refs", "refresh", "edit<PERSON><PERSON>ie<PERSON>", "review", "editForm", "setFormData", "content", "images", "isAnonymous", "handleEditSuccess", "deleteReview", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "delete", "goToFoods", "canEdit", "createTime", "Date", "now", "hoursDiff", "canDelete", "getStatusType", "typeMap", "getImageList", "split", "filter", "img", "trim", "formatTime", "time", "date", "diff", "Math", "floor", "toLocaleDateString", "formatStock", "stock", "stockNum", "parseInt", "getStockClass", "formatShelfStatus", "toString", "toLowerCase", "getStatusClass"], "sources": ["src/views/front/MyReviews.vue"], "sourcesContent": ["<template>\r\n  <div class=\"my-reviews\">\r\n    <div class=\"page-header\">\r\n      <h2 class=\"page-title\">我的评价</h2>\r\n      <div class=\"header-stats\">\r\n        <el-statistic title=\"总评价数\" :value=\"totalReviews\" suffix=\"条\"></el-statistic>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 筛选器 -->\r\n    <div class=\"filter-bar\">\r\n      <el-form :model=\"searchForm\" inline>\r\n        <el-form-item label=\"评分筛选\">\r\n          <el-select v-model=\"searchForm.rating\" placeholder=\"全部评分\" @change=\"handleSearch\">\r\n            <el-option label=\"全部评分\" value=\"\"></el-option>\r\n            <el-option label=\"5星\" value=\"5\"></el-option>\r\n            <el-option label=\"4星\" value=\"4\"></el-option>\r\n            <el-option label=\"3星\" value=\"3\"></el-option>\r\n            <el-option label=\"2星\" value=\"2\"></el-option>\r\n            <el-option label=\"1星\" value=\"1\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"评价状态\">\r\n          <el-select v-model=\"searchForm.status\" placeholder=\"全部状态\" @change=\"handleSearch\">\r\n            <el-option label=\"全部状态\" value=\"\"></el-option>\r\n            <el-option label=\"正常\" value=\"正常\"></el-option>\r\n            <el-option label=\"待审核\" value=\"待审核\"></el-option>\r\n            <el-option label=\"已删除\" value=\"已删除\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"商品名称\">\r\n          <el-input \r\n            v-model=\"searchForm.foodName\" \r\n            placeholder=\"请输入商品名称\"\r\n            @keyup.enter=\"handleSearch\"\r\n            clearable>\r\n          </el-input>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\r\n          <el-button @click=\"resetSearch\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n\r\n    <!-- 评价列表 -->\r\n    <div class=\"reviews-list\">\r\n      <div class=\"review-card\" v-for=\"review in reviewList\" :key=\"review.id\">\r\n        <!-- 商品信息 -->\r\n        <div class=\"review-header\">\r\n          <div class=\"food-info\">\r\n            <el-image \r\n              :src=\"review.foodImage\" \r\n              fit=\"cover\" \r\n              class=\"food-image\">\r\n              <div slot=\"error\" class=\"image-slot\">\r\n                <i class=\"el-icon-picture-outline\"></i>\r\n              </div>\r\n            </el-image>\r\n            <div class=\"food-detail\">\r\n              <h4 class=\"food-name\">{{ review.foodName }}</h4>\r\n              <div class=\"review-meta\">\r\n                <star-rating \r\n                  :value=\"review.rating\" \r\n                  :readonly=\"true\" \r\n                  :show-text=\"false\"\r\n                  size=\"small\">\r\n                </star-rating>\r\n                <span class=\"review-time\">{{ formatTime(review.createTime) }}</span>\r\n                <el-tag \r\n                  :type=\"getStatusType(review.status)\" \r\n                  size=\"mini\">\r\n                  {{ review.status }}\r\n                </el-tag>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"review-actions\">\r\n            <el-button \r\n              type=\"text\" \r\n              size=\"small\"\r\n              @click=\"viewFood(review.foodId)\"\r\n              v-if=\"review.status === '正常'\">\r\n              查看商品\r\n            </el-button>\r\n            <el-button \r\n              type=\"text\" \r\n              size=\"small\"\r\n              @click=\"editReview(review)\"\r\n              v-if=\"canEdit(review)\">\r\n              编辑\r\n            </el-button>\r\n            <el-button \r\n              type=\"text\" \r\n              size=\"small\"\r\n              class=\"danger-text\"\r\n              @click=\"deleteReview(review)\"\r\n              v-if=\"canDelete(review)\">\r\n              删除\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 评价内容 -->\r\n        <div class=\"review-content\">\r\n          <p class=\"review-text\" v-if=\"review.content\">{{ review.content }}</p>\r\n          \r\n          <!-- 评价图片 -->\r\n          <div class=\"review-images\" v-if=\"review.images\">\r\n            <div \r\n              class=\"image-item\" \r\n              v-for=\"(image, index) in getImageList(review.images)\" \r\n              :key=\"index\">\r\n              <el-image \r\n                :src=\"image\" \r\n                fit=\"cover\"\r\n                :preview-src-list=\"getImageList(review.images)\"\r\n                class=\"review-image\">\r\n              </el-image>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 商家回复 -->\r\n        <div class=\"merchant-reply\" v-if=\"review.replyContent\">\r\n          <div class=\"reply-header\">\r\n            <i class=\"el-icon-service\"></i>\r\n            <span class=\"reply-label\">商家回复：</span>\r\n            <span class=\"reply-time\">{{ formatTime(review.replyTime) }}</span>\r\n          </div>\r\n          <div class=\"reply-content\">{{ review.replyContent }}</div>\r\n        </div>\r\n\r\n        <!-- 有用性统计 -->\r\n        <div class=\"review-stats\" v-if=\"review.helpfulCount > 0\">\r\n          <span class=\"helpful-count\">\r\n            <i class=\"el-icon-thumb\"></i>\r\n            {{ review.helpfulCount }} 人觉得有用\r\n          </span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 分页 -->\r\n    <div class=\"pagination-container\">\r\n      <el-pagination\r\n        background\r\n        @current-change=\"handlePageChange\"\r\n        :current-page=\"currentPage\"\r\n        :page-size=\"pageSize\"\r\n        layout=\"prev, pager, next, total\"\r\n        :total=\"totalCount\"\r\n        :pager-count=\"5\">\r\n      </el-pagination>\r\n    </div>\r\n\r\n    <!-- 空状态 -->\r\n    <div class=\"empty-state\" v-if=\"!loading && reviewList.length === 0\">\r\n      <el-empty description=\"您还没有发表过评价\">\r\n        <el-button type=\"primary\" @click=\"goToFoods\">去购买商品</el-button>\r\n      </el-empty>\r\n    </div>\r\n\r\n    <!-- 编辑评价对话框 -->\r\n    <el-dialog\r\n      title=\"编辑评价\"\r\n      :visible.sync=\"editDialogVisible\"\r\n      width=\"600px\"\r\n      :close-on-click-modal=\"false\">\r\n      <food-review-form\r\n        v-if=\"editDialogVisible && editingReview\"\r\n        :food-id=\"editingReview.foodId\"\r\n        :food-name=\"editingReview.foodName\"\r\n        :show-cancel=\"true\"\r\n        ref=\"editForm\"\r\n        @submit-success=\"handleEditSuccess\"\r\n        @cancel=\"editDialogVisible = false\">\r\n      </food-review-form>\r\n    </el-dialog>\r\n\r\n    <!-- 商品详情弹窗 -->\r\n    <el-dialog\r\n      :visible.sync=\"detailVisible\"\r\n      width=\"70%\"\r\n      top=\"5vh\"\r\n      custom-class=\"detail-dialog\"\r\n      :close-on-click-modal=\"false\"\r\n      :show-close=\"false\">\r\n      <!-- 自定义关闭按钮 -->\r\n      <div class=\"custom-close-btn\" @click=\"detailVisible = false\">\r\n        <i class=\"el-icon-close\"></i>\r\n      </div>\r\n      \r\n      <div class=\"detail-container\" v-if=\"currentGoods\">\r\n        <div class=\"detail-left\">\r\n          <div class=\"detail-image-container\">\r\n            <el-image\r\n              :src=\"currentGoods.sfImage\"\r\n              fit=\"contain\"\r\n              class=\"detail-image\">\r\n              <div slot=\"error\" class=\"image-error\">\r\n                <i class=\"el-icon-picture-outline\"></i>\r\n                <span>图片加载失败</span>\r\n              </div>\r\n            </el-image>\r\n          </div>\r\n        </div>\r\n        <div class=\"detail-right\">\r\n          <div class=\"detail-header\">\r\n            <h2 class=\"detail-title\">{{ currentGoods.name }}</h2>\r\n            <div class=\"detail-price\">\r\n              <span class=\"price-symbol\">¥</span>\r\n              <span class=\"price-number\">{{ currentGoods.sfPrice }}</span>\r\n              <!-- 评分信息 -->\r\n              <div class=\"rating-info\" v-if=\"currentGoods.averageRating > 0\">\r\n                <star-rating \r\n                  :value=\"currentGoods.averageRating\" \r\n                  :readonly=\"true\" \r\n                  :show-text=\"false\"\r\n                  size=\"small\">\r\n                </star-rating>\r\n                <span class=\"rating-text\">{{ currentGoods.averageRating }}分</span>\r\n                <span class=\"review-count\">({{ currentGoods.reviewCount || 0 }}条评价)</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"detail-info\">\r\n            <div class=\"info-card\">\r\n              <div class=\"info-item\">\r\n                <i class=\"el-icon-goods info-icon\"></i>\r\n                <div class=\"info-content\">\r\n                  <span class=\"info-label\">商品类型</span>\r\n                  <span class=\"info-value\">{{ currentGoods.categoryName || currentGoods.sfCategory || '未分类' }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <i class=\"el-icon-box info-icon\"></i>\r\n                <div class=\"info-content\">\r\n                  <span class=\"info-label\">库存状态</span>\r\n                  <span class=\"info-value\" :class=\"getStockClass(currentGoods.sfStock)\">\r\n                    {{ formatStock(currentGoods.sfStock) }}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n              <div class=\"info-item\">\r\n                <i class=\"el-icon-check info-icon\"></i>\r\n                <div class=\"info-content\">\r\n                  <span class=\"info-label\">上架状态</span>\r\n                  <span class=\"info-value\" :class=\"getStatusClass(currentGoods.sfShelfStatus)\">\r\n                    {{ formatShelfStatus(currentGoods.sfShelfStatus) }}\r\n                  </span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <div class=\"detail-description\">\r\n            <h3 class=\"desc-title\">商品描述</h3>\r\n            <p class=\"desc-content\">{{ currentGoods.sfDescription }}</p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <!-- 商品评价区域 -->\r\n      <div class=\"detail-reviews\" v-if=\"currentGoods\">\r\n        <el-tabs v-model=\"activeTab\" class=\"detail-tabs\" @tab-click=\"handleTabChange\">\r\n          <el-tab-pane label=\"商品详情\" name=\"detail\">\r\n            <!-- 详细商品描述 -->\r\n            <div class=\"product-detail-content\">\r\n              <h3>商品详情</h3>\r\n              <p>{{ currentGoods.sfDescription || '暂无详细描述' }}</p>\r\n            </div>\r\n          </el-tab-pane>\r\n          <el-tab-pane :label=\"`商品评价(${currentGoods.reviewCount || 0})`\" name=\"reviews\">\r\n            <div class=\"reviews-container\">\r\n              <!-- 写评价按钮 -->\r\n              <div class=\"review-header\" v-if=\"user && user.id\">\r\n                <el-button \r\n                  type=\"primary\" \r\n                  size=\"small\"\r\n                  @click=\"showReviewDialog\"\r\n                  :loading=\"checkingPermission\">\r\n                  <i class=\"el-icon-edit\"></i>\r\n                  写评价\r\n                </el-button>\r\n              </div>\r\n              \r\n              <!-- 评价列表 -->\r\n              <food-review-list \r\n                v-if=\"currentGoods && currentGoods.id\"\r\n                :key=\"`review-list-${currentGoods.id}`\"\r\n                :food-id=\"currentGoods.id\"\r\n                :show-admin-actions=\"false\"\r\n                @add-review=\"showReviewDialog\"\r\n                ref=\"reviewList\">\r\n              </food-review-list>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 评价提交对话框 -->\r\n    <el-dialog\r\n      title=\"发表评价\"\r\n      :visible.sync=\"reviewDialogVisible\"\r\n      width=\"600px\"\r\n      :close-on-click-modal=\"false\">\r\n      <food-review-form\r\n        v-if=\"reviewDialogVisible && currentGoods\"\r\n        :food-id=\"currentGoods.id\"\r\n        :food-name=\"currentGoods.name\"\r\n        @submit-success=\"handleReviewSuccess\"\r\n        @cancel=\"reviewDialogVisible = false\">\r\n      </food-review-form>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport StarRating from '@/components/StarRating.vue'\r\nimport FoodReviewForm from '@/components/FoodReviewForm.vue'\r\nimport FoodReviewList from '@/components/FoodReviewList.vue'\r\n\r\nexport default {\r\n  name: 'MyReviews',\r\n  components: {\r\n    StarRating,\r\n    FoodReviewForm,\r\n    FoodReviewList\r\n  },\r\n  data() {\r\n    return {\r\n      reviewList: [],\r\n      loading: false,\r\n      currentPage: 1,\r\n      pageSize: 10,\r\n      totalCount: 0,\r\n      totalReviews: 0,\r\n      \r\n      // 搜索表单\r\n      searchForm: {\r\n        rating: '',\r\n        status: '',\r\n        foodName: ''\r\n      },\r\n      \r\n      // 编辑相关\r\n      editDialogVisible: false,\r\n      editingReview: null,\r\n      \r\n      // 商品详情弹窗相关\r\n      detailVisible: false,\r\n      currentGoods: null,\r\n      activeTab: 'detail',\r\n      reviewDialogVisible: false,\r\n      checkingPermission: false,\r\n      \r\n      user: JSON.parse(localStorage.getItem('xm-user') || '{}')\r\n    }\r\n  },\r\n  created() {\r\n    this.loadReviews()\r\n  },\r\n  methods: {\r\n    // 加载评价列表\r\n    async loadReviews() {\r\n      if (this.loading) return\r\n      \r\n      this.loading = true\r\n      \r\n      try {\r\n        const params = {\r\n          pageNum: this.currentPage,\r\n          pageSize: this.pageSize,\r\n          ...this.searchForm\r\n        }\r\n        \r\n        const response = await this.$request.get('/foodReview/my', { params })\r\n        \r\n        if (response.code === '200') {\r\n          this.reviewList = response.data?.list || []\r\n          this.totalCount = response.data?.total || 0\r\n          this.totalReviews = response.data?.total || 0\r\n        } else {\r\n          this.$message.error(response.msg || '加载评价列表失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('加载评价列表失败:', error)\r\n        this.$message.error('加载评价列表失败')\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n    \r\n    // 搜索\r\n    handleSearch() {\r\n      this.currentPage = 1\r\n      this.loadReviews()\r\n    },\r\n    \r\n    // 重置搜索\r\n    resetSearch() {\r\n      this.searchForm = {\r\n        rating: '',\r\n        status: '',\r\n        foodName: ''\r\n      }\r\n      this.handleSearch()\r\n    },\r\n    \r\n    // 分页变化\r\n    handlePageChange(page) {\r\n      this.currentPage = page\r\n      this.loadReviews()\r\n    },\r\n    \r\n    // 查看商品\r\n    viewFood(foodId) {\r\n      this.loadGoodsDetail(foodId)\r\n    },\r\n    \r\n    // 加载商品详情\r\n    async loadGoodsDetail(foodId) {\r\n      try {\r\n        const response = await this.$request.get(`/foods/selectByIdWithReviews/${foodId}`)\r\n        if (response.code === '200') {\r\n          this.currentGoods = response.data\r\n          this.detailVisible = true\r\n          this.activeTab = 'detail'\r\n        } else {\r\n          this.$message.error(response.msg || '获取商品详情失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取商品详情失败:', error)\r\n        this.$message.error('获取商品详情失败')\r\n      }\r\n    },\r\n\r\n    // 显示评价对话框\r\n    async showReviewDialog() {\r\n      if (!this.user || !this.user.id) {\r\n        this.$message.warning('请先登录后再评价')\r\n        this.$router.push('/login')\r\n        return\r\n      }\r\n\r\n      // 检查是否可以评价\r\n      this.checkingPermission = true\r\n      try {\r\n        const response = await this.$request.get('/foodReview/canReview', {\r\n          params: {\r\n            foodId: this.currentGoods.id\r\n          }\r\n        })\r\n\r\n        if (response.code === '200') {\r\n          if (response.data) {\r\n            this.reviewDialogVisible = true\r\n          } else {\r\n            this.$message.warning('您还没有购买过此商品，无法评价')\r\n          }\r\n        } else {\r\n          this.$message.error(response.msg || '检查评价权限失败')\r\n        }\r\n      } catch (error) {\r\n        console.error('检查评价权限失败:', error)\r\n        this.$message.error('检查评价权限失败')\r\n      } finally {\r\n        this.checkingPermission = false\r\n      }\r\n    },\r\n\r\n    // 处理评价提交成功\r\n    handleReviewSuccess(reviewData) {\r\n      this.$message.success('评价发布成功！')\r\n      this.reviewDialogVisible = false\r\n      \r\n      // 刷新商品信息以更新评价统计\r\n      this.refreshCurrentGoods()\r\n      \r\n      // 切换到评价Tab\r\n      this.activeTab = 'reviews'\r\n      \r\n      // 刷新我的评价列表\r\n      this.loadReviews()\r\n    },\r\n\r\n    // 刷新当前商品信息\r\n    async refreshCurrentGoods() {\r\n      if (!this.currentGoods || !this.currentGoods.id) return\r\n      \r\n      try {\r\n        const response = await this.$request.get(`/foods/selectByIdWithReviews/${this.currentGoods.id}`)\r\n        if (response.code === '200') {\r\n          this.currentGoods = response.data\r\n        }\r\n      } catch (error) {\r\n        console.error('刷新商品信息失败:', error)\r\n      }\r\n    },\r\n\r\n    // 处理Tab切换\r\n    handleTabChange(tab) {\r\n      const tabName = tab.name || tab\r\n      if (tabName === 'reviews' && this.currentGoods && this.currentGoods.id) {\r\n        // 切换到评价Tab时，确保评价列表正确加载\r\n        this.$nextTick(() => {\r\n          if (this.$refs.reviewList) {\r\n            this.$refs.reviewList.refresh()\r\n          }\r\n        })\r\n      }\r\n    },\r\n    \r\n    // 编辑评价\r\n    editReview(review) {\r\n      this.editingReview = review\r\n      this.editDialogVisible = true\r\n      \r\n      // 等待对话框打开后设置表单数据\r\n      this.$nextTick(() => {\r\n        if (this.$refs.editForm) {\r\n          this.$refs.editForm.setFormData({\r\n            rating: review.rating,\r\n            content: review.content,\r\n            images: review.images,\r\n            isAnonymous: review.isAnonymous\r\n          })\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 编辑成功\r\n    handleEditSuccess() {\r\n      this.$message.success('评价修改成功！')\r\n      this.editDialogVisible = false\r\n      this.loadReviews() // 重新加载列表\r\n    },\r\n    \r\n    // 删除评价\r\n    deleteReview(review) {\r\n      this.$confirm(`确定要删除对\"${review.foodName}\"的评价吗？`, '确认删除', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(async () => {\r\n        try {\r\n          const response = await this.$request.delete(`/foodReview/${review.id}`)\r\n          \r\n          if (response.code === '200') {\r\n            this.$message.success('评价删除成功！')\r\n            this.loadReviews() // 重新加载列表\r\n          } else {\r\n            this.$message.error(response.msg || '删除失败')\r\n          }\r\n        } catch (error) {\r\n          console.error('删除评价失败:', error)\r\n          this.$message.error('删除失败，请稍后重试')\r\n        }\r\n      })\r\n    },\r\n    \r\n    // 去购买商品\r\n    goToFoods() {\r\n      this.$router.push('/front/foods')\r\n    },\r\n    \r\n    // 判断是否可以编辑\r\n    canEdit(review) {\r\n      // 只有正常状态的评价可以编辑，且在发布后24小时内\r\n      if (review.status !== '正常') return false\r\n      \r\n      const createTime = new Date(review.createTime)\r\n      const now = new Date()\r\n      const hoursDiff = (now - createTime) / (1000 * 60 * 60)\r\n      \r\n      return hoursDiff <= 24 // 24小时内可以编辑\r\n    },\r\n    \r\n    // 判断是否可以删除\r\n    canDelete(review) {\r\n      // 用户可以删除自己的评价（除了已删除状态）\r\n      return review.status !== '已删除'\r\n    },\r\n    \r\n    // 获取状态类型\r\n    getStatusType(status) {\r\n      const typeMap = {\r\n        '正常': 'success',\r\n        '待审核': 'warning',\r\n        '已删除': 'danger'\r\n      }\r\n      return typeMap[status] || 'info'\r\n    },\r\n    \r\n    // 获取图片列表\r\n    getImageList(images) {\r\n      if (!images) return []\r\n      return images.split(',').filter(img => img.trim())\r\n    },\r\n    \r\n    // 格式化时间\r\n    formatTime(time) {\r\n      if (!time) return ''\r\n      \r\n      const date = new Date(time)\r\n      const now = new Date()\r\n      const diff = now - date\r\n      \r\n      // 小于1分钟\r\n      if (diff < 60 * 1000) {\r\n        return '刚刚'\r\n      }\r\n      \r\n      // 小于1小时\r\n      if (diff < 60 * 60 * 1000) {\r\n        return `${Math.floor(diff / (60 * 1000))}分钟前`\r\n      }\r\n      \r\n      // 小于1天\r\n      if (diff < 24 * 60 * 60 * 1000) {\r\n        return `${Math.floor(diff / (60 * 60 * 1000))}小时前`\r\n      }\r\n      \r\n      // 小于7天\r\n      if (diff < 7 * 24 * 60 * 60 * 1000) {\r\n        return `${Math.floor(diff / (24 * 60 * 60 * 1000))}天前`\r\n      }\r\n      \r\n      // 超过7天显示具体日期\r\n      return date.toLocaleDateString()\r\n    },\r\n\r\n    // 格式化库存显示\r\n    formatStock(stock) {\r\n      if (!stock && stock !== 0) {\r\n        return '未知'\r\n      }\r\n      const stockNum = parseInt(stock) || 0\r\n      if (stockNum <= 0) {\r\n        return '缺货'\r\n      } else if (stockNum <= 10) {\r\n        return `剩余 ${stockNum} 件`\r\n      } else {\r\n        return `库存充足 (${stockNum}件)`\r\n      }\r\n    },\r\n\r\n    // 获取库存状态样式类\r\n    getStockClass(stock) {\r\n      const stockNum = parseInt(stock) || 0\r\n      if (stockNum <= 0) {\r\n        return 'stock-out'\r\n      } else if (stockNum <= 10) {\r\n        return 'stock-low'\r\n      } else {\r\n        return 'stock-normal'\r\n      }\r\n    },\r\n\r\n    // 格式化上架状态显示\r\n    formatShelfStatus(status) {\r\n      if (!status) {\r\n        return '未知'\r\n      }\r\n      // 根据常见的状态值进行格式化\r\n      switch (status.toString().toLowerCase()) {\r\n        case '1':\r\n        case 'on':\r\n        case 'online':\r\n        case '上架':\r\n        case 'enabled':\r\n          return '已上架'\r\n        case '0':\r\n        case 'off':\r\n        case 'offline':\r\n        case '下架':\r\n        case 'disabled':\r\n          return '已下架'\r\n        default:\r\n          return status\r\n      }\r\n    },\r\n\r\n    // 获取上架状态样式类\r\n    getStatusClass(status) {\r\n      if (!status) {\r\n        return 'status-unknown'\r\n      }\r\n      switch (status.toString().toLowerCase()) {\r\n        case '1':\r\n        case 'on':\r\n        case 'online':\r\n        case '上架':\r\n        case 'enabled':\r\n          return 'status-online'\r\n        case '0':\r\n        case 'off':\r\n        case 'offline':\r\n        case '下架':\r\n        case 'disabled':\r\n          return 'status-offline'\r\n        default:\r\n          return 'status-unknown'\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.my-reviews {\r\n  max-width: 1000px;\r\n  margin: 0 auto;\r\n  padding: 20px;\r\n}\r\n\r\n.page-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n  padding-bottom: 20px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin: 0;\r\n}\r\n\r\n.header-stats {\r\n  display: flex;\r\n  gap: 30px;\r\n}\r\n\r\n/* 筛选器 */\r\n.filter-bar {\r\n  background: white;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n/* 评价列表 */\r\n.reviews-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.review-card {\r\n  background: white;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\r\n  border: 1px solid #f0f0f0;\r\n  transition: box-shadow 0.3s;\r\n}\r\n\r\n.review-card:hover {\r\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.review-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.food-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 15px;\r\n  flex: 1;\r\n}\r\n\r\n.food-image {\r\n  width: 60px;\r\n  height: 60px;\r\n  border-radius: 6px;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.image-slot {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 100%;\r\n  background: #f5f7fa;\r\n  color: #909399;\r\n}\r\n\r\n.food-detail {\r\n  flex: 1;\r\n}\r\n\r\n.food-name {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin: 0 0 8px 0;\r\n}\r\n\r\n.review-meta {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.review-time {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.review-actions {\r\n  display: flex;\r\n  gap: 8px;\r\n}\r\n\r\n.review-actions .danger-text {\r\n  color: #f56c6c;\r\n}\r\n\r\n/* 评价内容 */\r\n.review-content {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.review-text {\r\n  font-size: 14px;\r\n  line-height: 1.6;\r\n  color: #333;\r\n  margin-bottom: 12px;\r\n  word-break: break-word;\r\n}\r\n\r\n.review-images {\r\n  display: flex;\r\n  gap: 8px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.image-item {\r\n  width: 80px;\r\n  height: 80px;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n  cursor: pointer;\r\n  transition: transform 0.2s;\r\n}\r\n\r\n.image-item:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.review-image {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n/* 商家回复 */\r\n.merchant-reply {\r\n  background: #f8f9fa;\r\n  border-radius: 6px;\r\n  padding: 12px;\r\n  margin-bottom: 15px;\r\n  border-left: 3px solid #409eff;\r\n}\r\n\r\n.reply-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  margin-bottom: 8px;\r\n  font-size: 12px;\r\n  color: #666;\r\n}\r\n\r\n.reply-label {\r\n  font-weight: bold;\r\n  color: #409eff;\r\n}\r\n\r\n.reply-content {\r\n  font-size: 14px;\r\n  color: #333;\r\n  line-height: 1.5;\r\n}\r\n\r\n/* 有用性统计 */\r\n.review-stats {\r\n  padding-top: 12px;\r\n  border-top: 1px solid #f0f0f0;\r\n}\r\n\r\n.helpful-count {\r\n  font-size: 12px;\r\n  color: #666;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n}\r\n\r\n/* 分页 */\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 30px;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-state {\r\n  padding: 60px;\r\n  text-align: center;\r\n}\r\n\r\n/* 商品详情弹窗样式 */\r\n/deep/ .detail-dialog {\r\n  border-radius: 16px;\r\n  overflow: hidden;\r\n}\r\n\r\n/deep/ .detail-dialog .el-dialog__header {\r\n  display: none;\r\n}\r\n\r\n/deep/ .detail-dialog .el-dialog__body {\r\n  padding: 0;\r\n  position: relative;\r\n}\r\n\r\n/* 自定义关闭按钮 */\r\n.custom-close-btn {\r\n  position: absolute;\r\n  top: 20px;\r\n  right: 20px;\r\n  width: 40px;\r\n  height: 40px;\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n  z-index: 1000;\r\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.custom-close-btn:hover {\r\n  background: white;\r\n  transform: scale(1.1);\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\r\n}\r\n\r\n.custom-close-btn i {\r\n  font-size: 18px;\r\n  color: #666;\r\n  transition: color 0.3s ease;\r\n}\r\n\r\n.custom-close-btn:hover i {\r\n  color: #333;\r\n}\r\n\r\n.detail-container {\r\n  display: flex;\r\n  min-height: 500px;\r\n}\r\n\r\n.detail-left {\r\n  flex: 1;\r\n  background: #f8fafc;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 40px;\r\n}\r\n\r\n.detail-image-container {\r\n  width: 100%;\r\n  max-width: 400px;\r\n}\r\n\r\n.detail-image {\r\n  width: 100%;\r\n  height: 400px;\r\n  border-radius: 12px;\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.image-error {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  height: 100%;\r\n  background: #f5f5f5;\r\n  color: #999;\r\n}\r\n\r\n.image-error i {\r\n  font-size: 32px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.image-error span {\r\n  font-size: 12px;\r\n}\r\n\r\n.detail-right {\r\n  flex: 1;\r\n  padding: 40px;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.detail-header {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.detail-title {\r\n  font-size: 28px;\r\n  font-weight: 700;\r\n  color: #1e293b;\r\n  margin-bottom: 16px;\r\n  line-height: 1.3;\r\n}\r\n\r\n.detail-price {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.detail-price .price-symbol {\r\n  color: #3b82f6;\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n}\r\n\r\n.detail-price .price-number {\r\n  color: #3b82f6;\r\n  font-size: 36px;\r\n  font-weight: 700;\r\n}\r\n\r\n.rating-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-top: 8px;\r\n}\r\n\r\n.rating-text {\r\n  font-size: 14px;\r\n  color: #F7BA2A;\r\n  font-weight: bold;\r\n}\r\n\r\n.review-count {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.detail-info {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.info-card {\r\n  background: #f8fafc;\r\n  border-radius: 12px;\r\n  padding: 20px;\r\n  border: 1px solid #e2e8f0;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.info-item:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.info-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  background: #3b82f6;\r\n  color: white;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 16px;\r\n  font-size: 16px;\r\n}\r\n\r\n.info-content {\r\n  flex: 1;\r\n}\r\n\r\n.info-label {\r\n  display: block;\r\n  font-size: 14px;\r\n  color: #64748b;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.info-value {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #1e293b;\r\n}\r\n\r\n/* 库存状态样式 */\r\n.info-value.stock-normal {\r\n  color: #10b981;\r\n}\r\n\r\n.info-value.stock-low {\r\n  color: #f59e0b;\r\n}\r\n\r\n.info-value.stock-out {\r\n  color: #ef4444;\r\n}\r\n\r\n/* 上架状态样式 */\r\n.info-value.status-online {\r\n  color: #10b981;\r\n}\r\n\r\n.info-value.status-offline {\r\n  color: #ef4444;\r\n}\r\n\r\n.info-value.status-unknown {\r\n  color: #6b7280;\r\n}\r\n\r\n.detail-description {\r\n  margin-bottom: 40px;\r\n  flex: 1;\r\n}\r\n\r\n.desc-title {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: #1e293b;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.desc-content {\r\n  font-size: 15px;\r\n  color: #475569;\r\n  line-height: 1.7;\r\n  margin: 0;\r\n}\r\n\r\n.detail-reviews {\r\n  margin-top: 20px;\r\n  border-top: 1px solid #e2e8f0;\r\n  padding-top: 20px;\r\n}\r\n\r\n.detail-tabs {\r\n  min-height: 300px;\r\n}\r\n\r\n.product-detail-content {\r\n  padding: 20px;\r\n  font-size: 14px;\r\n  line-height: 1.6;\r\n  color: #666;\r\n}\r\n\r\n.product-detail-content h3 {\r\n  font-size: 16px;\r\n  color: #1e293b;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n.reviews-container {\r\n  padding: 20px 0;\r\n}\r\n\r\n.review-header {\r\n  margin-bottom: 20px;\r\n  text-align: center;\r\n}\r\n\r\n/* Tab样式定制 */\r\n.detail-tabs >>> .el-tabs__header {\r\n  margin: 0 0 20px 0;\r\n  border-bottom: 2px solid #e2e8f0;\r\n}\r\n\r\n.detail-tabs >>> .el-tabs__nav-wrap::after {\r\n  display: none;\r\n}\r\n\r\n.detail-tabs >>> .el-tabs__item {\r\n  padding: 0 20px;\r\n  height: 40px;\r\n  line-height: 40px;\r\n  font-weight: 500;\r\n  color: #64748b;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.detail-tabs >>> .el-tabs__item:hover {\r\n  color: #3b82f6;\r\n}\r\n\r\n.detail-tabs >>> .el-tabs__item.is-active {\r\n  color: #3b82f6;\r\n  font-weight: 600;\r\n}\r\n\r\n.detail-tabs >>> .el-tabs__active-bar {\r\n  background-color: #3b82f6;\r\n  height: 3px;\r\n  border-radius: 2px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .my-reviews {\r\n    padding: 15px;\r\n  }\r\n  \r\n  .page-header {\r\n    flex-direction: column;\r\n    gap: 15px;\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .filter-bar .el-form {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .filter-bar .el-form-item {\r\n    margin-right: 0;\r\n    margin-bottom: 15px;\r\n  }\r\n  \r\n  .review-header {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 15px;\r\n  }\r\n  \r\n  .food-info {\r\n    width: 100%;\r\n  }\r\n  \r\n  .review-actions {\r\n    width: 100%;\r\n    justify-content: flex-end;\r\n  }\r\n  \r\n  .review-card {\r\n    padding: 15px;\r\n  }\r\n\r\n  /deep/ .detail-dialog {\r\n    width: 95% !important;\r\n    margin: 5px auto !important;\r\n  }\r\n  \r\n  .detail-container {\r\n    flex-direction: column;\r\n  }\r\n  \r\n  .detail-left,\r\n  .detail-right {\r\n    flex: none;\r\n    width: 100%;\r\n  }\r\n  \r\n  .detail-left {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .detail-right {\r\n    padding: 20px;\r\n  }\r\n  \r\n  .detail-image {\r\n    height: 250px;\r\n  }\r\n  \r\n  .detail-reviews {\r\n    padding-top: 15px;\r\n  }\r\n  \r\n  .detail-tabs {\r\n    min-height: 200px;\r\n  }\r\n  \r\n  .product-detail-content {\r\n    padding: 15px;\r\n  }\r\n\r\n  .custom-close-btn {\r\n    top: 10px;\r\n    right: 10px;\r\n    width: 36px;\r\n    height: 36px;\r\n  }\r\n\r\n  .custom-close-btn i {\r\n    font-size: 16px;\r\n  }\r\n}\r\n</style> "], "mappings": ";;;AAiUA,OAAAA,UAAA;AACA,OAAAC,cAAA;AACA,OAAAC,cAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAJ,UAAA;IACAC,cAAA;IACAC;EACA;EACAG,KAAA;IACA;MACAC,UAAA;MACAC,OAAA;MACAC,WAAA;MACAC,QAAA;MACAC,UAAA;MACAC,YAAA;MAEA;MACAC,UAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;MACA;MAEA;MACAC,iBAAA;MACAC,aAAA;MAEA;MACAC,aAAA;MACAC,YAAA;MACAC,SAAA;MACAC,mBAAA;MACAC,kBAAA;MAEAC,IAAA,EAAAC,IAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,OAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA;IACA,MAAAD,YAAA;MACA,SAAAtB,OAAA;MAEA,KAAAA,OAAA;MAEA;QACA,MAAAwB,MAAA;UACAC,OAAA,OAAAxB,WAAA;UACAC,QAAA,OAAAA,QAAA;UACA,QAAAG;QACA;QAEA,MAAAqB,QAAA,cAAAC,QAAA,CAAAC,GAAA;UAAAJ;QAAA;QAEA,IAAAE,QAAA,CAAAG,IAAA;UACA,KAAA9B,UAAA,GAAA2B,QAAA,CAAA5B,IAAA,EAAAgC,IAAA;UACA,KAAA3B,UAAA,GAAAuB,QAAA,CAAA5B,IAAA,EAAAiC,KAAA;UACA,KAAA3B,YAAA,GAAAsB,QAAA,CAAA5B,IAAA,EAAAiC,KAAA;QACA;UACA,KAAAC,QAAA,CAAAC,KAAA,CAAAP,QAAA,CAAAQ,GAAA;QACA;MACA,SAAAD,KAAA;QACAE,OAAA,CAAAF,KAAA,cAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA;MACA;QACA,KAAAjC,OAAA;MACA;IACA;IAEA;IACAoC,aAAA;MACA,KAAAnC,WAAA;MACA,KAAAqB,WAAA;IACA;IAEA;IACAe,YAAA;MACA,KAAAhC,UAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;MACA;MACA,KAAA4B,YAAA;IACA;IAEA;IACAE,iBAAAC,IAAA;MACA,KAAAtC,WAAA,GAAAsC,IAAA;MACA,KAAAjB,WAAA;IACA;IAEA;IACAkB,SAAAC,MAAA;MACA,KAAAC,eAAA,CAAAD,MAAA;IACA;IAEA;IACA,MAAAC,gBAAAD,MAAA;MACA;QACA,MAAAf,QAAA,cAAAC,QAAA,CAAAC,GAAA,iCAAAa,MAAA;QACA,IAAAf,QAAA,CAAAG,IAAA;UACA,KAAAjB,YAAA,GAAAc,QAAA,CAAA5B,IAAA;UACA,KAAAa,aAAA;UACA,KAAAE,SAAA;QACA;UACA,KAAAmB,QAAA,CAAAC,KAAA,CAAAP,QAAA,CAAAQ,GAAA;QACA;MACA,SAAAD,KAAA;QACAE,OAAA,CAAAF,KAAA,cAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACA,MAAAU,iBAAA;MACA,UAAA3B,IAAA,UAAAA,IAAA,CAAA4B,EAAA;QACA,KAAAZ,QAAA,CAAAa,OAAA;QACA,KAAAC,OAAA,CAAAC,IAAA;QACA;MACA;;MAEA;MACA,KAAAhC,kBAAA;MACA;QACA,MAAAW,QAAA,cAAAC,QAAA,CAAAC,GAAA;UACAJ,MAAA;YACAiB,MAAA,OAAA7B,YAAA,CAAAgC;UACA;QACA;QAEA,IAAAlB,QAAA,CAAAG,IAAA;UACA,IAAAH,QAAA,CAAA5B,IAAA;YACA,KAAAgB,mBAAA;UACA;YACA,KAAAkB,QAAA,CAAAa,OAAA;UACA;QACA;UACA,KAAAb,QAAA,CAAAC,KAAA,CAAAP,QAAA,CAAAQ,GAAA;QACA;MACA,SAAAD,KAAA;QACAE,OAAA,CAAAF,KAAA,cAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA;MACA;QACA,KAAAlB,kBAAA;MACA;IACA;IAEA;IACAiC,oBAAAC,UAAA;MACA,KAAAjB,QAAA,CAAAkB,OAAA;MACA,KAAApC,mBAAA;;MAEA;MACA,KAAAqC,mBAAA;;MAEA;MACA,KAAAtC,SAAA;;MAEA;MACA,KAAAS,WAAA;IACA;IAEA;IACA,MAAA6B,oBAAA;MACA,UAAAvC,YAAA,UAAAA,YAAA,CAAAgC,EAAA;MAEA;QACA,MAAAlB,QAAA,cAAAC,QAAA,CAAAC,GAAA,sCAAAhB,YAAA,CAAAgC,EAAA;QACA,IAAAlB,QAAA,CAAAG,IAAA;UACA,KAAAjB,YAAA,GAAAc,QAAA,CAAA5B,IAAA;QACA;MACA,SAAAmC,KAAA;QACAE,OAAA,CAAAF,KAAA,cAAAA,KAAA;MACA;IACA;IAEA;IACAmB,gBAAAC,GAAA;MACA,MAAAC,OAAA,GAAAD,GAAA,CAAAzD,IAAA,IAAAyD,GAAA;MACA,IAAAC,OAAA,uBAAA1C,YAAA,SAAAA,YAAA,CAAAgC,EAAA;QACA;QACA,KAAAW,SAAA;UACA,SAAAC,KAAA,CAAAzD,UAAA;YACA,KAAAyD,KAAA,CAAAzD,UAAA,CAAA0D,OAAA;UACA;QACA;MACA;IACA;IAEA;IACAC,WAAAC,MAAA;MACA,KAAAjD,aAAA,GAAAiD,MAAA;MACA,KAAAlD,iBAAA;;MAEA;MACA,KAAA8C,SAAA;QACA,SAAAC,KAAA,CAAAI,QAAA;UACA,KAAAJ,KAAA,CAAAI,QAAA,CAAAC,WAAA;YACAvD,MAAA,EAAAqD,MAAA,CAAArD,MAAA;YACAwD,OAAA,EAAAH,MAAA,CAAAG,OAAA;YACAC,MAAA,EAAAJ,MAAA,CAAAI,MAAA;YACAC,WAAA,EAAAL,MAAA,CAAAK;UACA;QACA;MACA;IACA;IAEA;IACAC,kBAAA;MACA,KAAAjC,QAAA,CAAAkB,OAAA;MACA,KAAAzC,iBAAA;MACA,KAAAa,WAAA;IACA;IAEA;IACA4C,aAAAP,MAAA;MACA,KAAAQ,QAAA,WAAAR,MAAA,CAAAnD,QAAA;QACA4D,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAC,IAAA;QACA;UACA,MAAA7C,QAAA,cAAAC,QAAA,CAAA6C,MAAA,gBAAAb,MAAA,CAAAf,EAAA;UAEA,IAAAlB,QAAA,CAAAG,IAAA;YACA,KAAAG,QAAA,CAAAkB,OAAA;YACA,KAAA5B,WAAA;UACA;YACA,KAAAU,QAAA,CAAAC,KAAA,CAAAP,QAAA,CAAAQ,GAAA;UACA;QACA,SAAAD,KAAA;UACAE,OAAA,CAAAF,KAAA,YAAAA,KAAA;UACA,KAAAD,QAAA,CAAAC,KAAA;QACA;MACA;IACA;IAEA;IACAwC,UAAA;MACA,KAAA3B,OAAA,CAAAC,IAAA;IACA;IAEA;IACA2B,QAAAf,MAAA;MACA;MACA,IAAAA,MAAA,CAAApD,MAAA;MAEA,MAAAoE,UAAA,OAAAC,IAAA,CAAAjB,MAAA,CAAAgB,UAAA;MACA,MAAAE,GAAA,OAAAD,IAAA;MACA,MAAAE,SAAA,IAAAD,GAAA,GAAAF,UAAA;MAEA,OAAAG,SAAA;IACA;IAEA;IACAC,UAAApB,MAAA;MACA;MACA,OAAAA,MAAA,CAAApD,MAAA;IACA;IAEA;IACAyE,cAAAzE,MAAA;MACA,MAAA0E,OAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAA1E,MAAA;IACA;IAEA;IACA2E,aAAAnB,MAAA;MACA,KAAAA,MAAA;MACA,OAAAA,MAAA,CAAAoB,KAAA,MAAAC,MAAA,CAAAC,GAAA,IAAAA,GAAA,CAAAC,IAAA;IACA;IAEA;IACAC,WAAAC,IAAA;MACA,KAAAA,IAAA;MAEA,MAAAC,IAAA,OAAAb,IAAA,CAAAY,IAAA;MACA,MAAAX,GAAA,OAAAD,IAAA;MACA,MAAAc,IAAA,GAAAb,GAAA,GAAAY,IAAA;;MAEA;MACA,IAAAC,IAAA;QACA;MACA;;MAEA;MACA,IAAAA,IAAA;QACA,UAAAC,IAAA,CAAAC,KAAA,CAAAF,IAAA;MACA;;MAEA;MACA,IAAAA,IAAA;QACA,UAAAC,IAAA,CAAAC,KAAA,CAAAF,IAAA;MACA;;MAEA;MACA,IAAAA,IAAA;QACA,UAAAC,IAAA,CAAAC,KAAA,CAAAF,IAAA;MACA;;MAEA;MACA,OAAAD,IAAA,CAAAI,kBAAA;IACA;IAEA;IACAC,YAAAC,KAAA;MACA,KAAAA,KAAA,IAAAA,KAAA;QACA;MACA;MACA,MAAAC,QAAA,GAAAC,QAAA,CAAAF,KAAA;MACA,IAAAC,QAAA;QACA;MACA,WAAAA,QAAA;QACA,aAAAA,QAAA;MACA;QACA,gBAAAA,QAAA;MACA;IACA;IAEA;IACAE,cAAAH,KAAA;MACA,MAAAC,QAAA,GAAAC,QAAA,CAAAF,KAAA;MACA,IAAAC,QAAA;QACA;MACA,WAAAA,QAAA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAG,kBAAA5F,MAAA;MACA,KAAAA,MAAA;QACA;MACA;MACA;MACA,QAAAA,MAAA,CAAA6F,QAAA,GAAAC,WAAA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;UACA,OAAA9F,MAAA;MACA;IACA;IAEA;IACA+F,eAAA/F,MAAA;MACA,KAAAA,MAAA;QACA;MACA;MACA,QAAAA,MAAA,CAAA6F,QAAA,GAAAC,WAAA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;UACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}