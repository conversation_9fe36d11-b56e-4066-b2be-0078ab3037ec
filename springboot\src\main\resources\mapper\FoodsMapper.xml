<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.FoodsMapper">

    <sql id="Base_Column_List">
        id, name, sf_image, sf_description, sf_category, category_id, sf_price, sf_stock, sf_shelf_status, 
        average_rating, review_count, last_review_time
    </sql>

    <select id="selectAll" resultType="com.example.entity.Foods">
        select
        f.id, f.name, f.sf_image, f.sf_description, f.sf_category, f.category_id, 
        f.sf_price, f.sf_stock, f.sf_shelf_status, f.average_rating, f.review_count, f.last_review_time,
        c.name as categoryName
        from sf_food f
        left join sf_category c on f.category_id = c.id
        <where>
            <if test="id != null"> and f.id = #{id}</if>
            <if test="name != null and name != ''"> and f.name like concat('%', #{name}, '%')</if>
            <if test="categoryId != null"> and f.category_id = #{categoryId}</if>
            <if test="sfShelfStatus != null and sfShelfStatus != ''"> and f.sf_shelf_status = #{sfShelfStatus}</if>
        </where>
        order by f.id desc
    </select>

    <select id="selectById" resultType="com.example.entity.Foods">
        select
        <include refid="Base_Column_List" />
        from sf_food
        where id = #{id}
    </select>

    <delete id="deleteById">
        delete from sf_food
        where id = #{id}
    </delete>

    <insert id="insert" parameterType="com.example.entity.Foods" useGeneratedKeys="true" keyProperty="id">
        insert into sf_food
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="sfImage != null">sf_image,</if>
            <if test="sfDescription != null">sf_description,</if>
            <if test="sfCategory != null">sf_category,</if>
            <if test="categoryId != null">category_id,</if>
            <if test="sfPrice != null">sf_price,</if>
            <if test="sfStock != null">sf_stock,</if>
            <if test="sfShelfStatus != null">sf_shelf_status,</if>
            <if test="averageRating != null">average_rating,</if>
            <if test="reviewCount != null">review_count,</if>
            <if test="lastReviewTime != null">last_review_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="sfImage != null">#{sfImage},</if>
            <if test="sfDescription != null">#{sfDescription},</if>
            <if test="sfCategory != null">#{sfCategory},</if>
            <if test="categoryId != null">#{categoryId},</if>
            <if test="sfPrice != null">#{sfPrice},</if>
            <if test="sfStock != null">#{sfStock},</if>
            <if test="sfShelfStatus != null">#{sfShelfStatus},</if>
            <if test="averageRating != null">#{averageRating},</if>
            <if test="reviewCount != null">#{reviewCount},</if>
            <if test="lastReviewTime != null">#{lastReviewTime},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.example.entity.Foods">
        update sf_food
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="sfImage != null">
                sf_image = #{sfImage},
            </if>
            <if test="sfDescription != null">
                sf_description = #{sfDescription},
            </if>
            <if test="sfCategory != null">
                sf_category = #{sfCategory},
            </if>
            <if test="categoryId != null">
                category_id = #{categoryId},
            </if>
            <if test="sfPrice != null">
                sf_price = #{sfPrice},
            </if>
            <if test="sfStock != null">
                sf_stock = #{sfStock},
            </if>
            <if test="sfShelfStatus != null">
                sf_shelf_status = #{sfShelfStatus},
            </if>
            <if test="averageRating != null">
                average_rating = #{averageRating},
            </if>
            <if test="reviewCount != null">
                review_count = #{reviewCount},
            </if>
            <if test="lastReviewTime != null">
                last_review_time = #{lastReviewTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="selectByCategoryId" resultType="com.example.entity.Foods">
        select
        f.id, f.name, f.sf_image, f.sf_description, f.sf_category, f.category_id, 
        f.sf_price, f.sf_stock, f.sf_shelf_status, f.average_rating, f.review_count, f.last_review_time,
        c.name as categoryName
        from sf_food f
        left join sf_category c on f.category_id = c.id
        where f.category_id = #{categoryId} and f.sf_shelf_status = '上架'
        order by f.id desc
    </select>

    <select id="countByCategoryId" resultType="int">
        select count(1) from sf_food where category_id = #{categoryId}
    </select>

    <!-- 库存管理相关SQL -->
    <!-- 检查商品库存是否充足：返回1表示充足，0表示不足 -->
    <select id="checkStock" resultType="int">
        SELECT CASE 
            WHEN CAST(sf_stock AS UNSIGNED) >= #{requiredQuantity} THEN 1 
            ELSE 0 
        END
        FROM sf_food 
        WHERE id = #{foodId} AND sf_shelf_status = '上架'
    </select>

    <!-- 扣减商品库存 -->
    <update id="reduceStock">
        UPDATE sf_food 
        SET sf_stock = CAST(sf_stock AS UNSIGNED) - #{quantity}
        WHERE id = #{foodId} 
        AND CAST(sf_stock AS UNSIGNED) >= #{quantity}
        AND sf_shelf_status = '上架'
    </update>

    <!-- 恢复商品库存 -->
    <update id="restoreStock">
        UPDATE sf_food 
        SET sf_stock = CAST(sf_stock AS UNSIGNED) + #{quantity}
        WHERE id = #{foodId}
        AND sf_shelf_status = '上架'
    </update>

    <!-- 获取商品当前库存 -->
    <select id="getCurrentStock" resultType="java.lang.Integer">
        SELECT CAST(sf_stock AS UNSIGNED) 
        FROM sf_food 
        WHERE id = #{foodId}
    </select>
</mapper>