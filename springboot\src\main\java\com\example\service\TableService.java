package com.example.service;

import com.example.common.Result;
import com.example.entity.Table;
import com.example.mapper.TableMapper;
import com.example.common.enums.ResultCodeEnum;
import com.example.exception.CustomException;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 餐桌业务服务层
 */
@Service
public class TableService {

    @Autowired
    private TableMapper tableMapper;

    /**
     * 分页查询餐桌
     */
    public PageInfo<Table> selectPage(Table table, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Table> list = tableMapper.selectAll(table);
        return PageInfo.of(list);
    }

    /**
     * 查询所有餐桌
     */
    public List<Table> selectAll(Table table) {
        return tableMapper.selectAll(table);
    }

    /**
     * 查询所有可用餐桌（排除有未完成订单的餐桌）
     */
    public List<Table> selectAvailable() {
        return tableMapper.selectAvailable();
    }

    /**
     * 根据ID查询餐桌
     */
    public Table selectById(Integer id) {
        return tableMapper.selectById(id);
    }

    /**
     * 根据餐桌号查询餐桌
     */
    public Table selectByTableNumber(String tableNumber) {
        return tableMapper.selectByTableNumber(tableNumber);
    }

    /**
     * 新增餐桌
     */
    @Transactional
    public void add(Table table) {
        // 检查餐桌号是否已存在
        Table existingTable = tableMapper.selectByTableNumber(table.getTableNumber());
        if (existingTable != null) {
            throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, "餐桌号已存在");
        }

        // 设置默认值
        if (table.getStatus() == null || table.getStatus().isEmpty()) {
            table.setStatus("空闲");
        }
        if (table.getSeats() == null) {
            table.setSeats(4);
        }

        tableMapper.insert(table);
    }

    /**
     * 更新餐桌
     */
    @Transactional
    public void updateById(Table table) {
        Table existingTable = tableMapper.selectById(table.getId());
        if (existingTable == null) {
            throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, "餐桌不存在");
        }

        // 如果修改了餐桌号，检查新餐桌号是否已存在
        if (table.getTableNumber() != null && 
            !table.getTableNumber().equals(existingTable.getTableNumber())) {
            Table duplicateTable = tableMapper.selectByTableNumber(table.getTableNumber());
            if (duplicateTable != null) {
                throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, "餐桌号已存在");
            }
        }

        tableMapper.updateById(table);
    }

    /**
     * 删除餐桌
     */
    @Transactional
    public void deleteById(Integer id) {
        Table table = tableMapper.selectById(id);
        if (table == null) {
            throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, "餐桌不存在");
        }

        // 检查餐桌是否被占用
        int occupiedCount = tableMapper.checkTableOccupied(table.getTableNumber());
        if (occupiedCount > 0) {
            throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, "该餐桌有未完成订单，无法删除");
        }

        tableMapper.deleteById(id);
    }

    /**
     * 检查餐桌是否可用（无未完成订单）
     */
    public boolean isTableAvailable(String tableNumber) {
        Table table = tableMapper.selectByTableNumber(tableNumber);
        if (table == null) {
            return false;
        }

        // 检查餐桌状态
        if (!"空闲".equals(table.getStatus())) {
            return false;
        }

        // 检查是否有未完成订单
        int occupiedCount = tableMapper.checkTableOccupied(tableNumber);
        return occupiedCount == 0;
    }

    /**
     * 查询餐桌状态统计
     */
    public List<Map<String, Object>> getTableStatusStats() {
        return tableMapper.selectTableStatusStats();
    }

    /**
     * 查询餐桌详细状态（包含占用信息）
     */
    public List<Map<String, Object>> getTableStatusDetail() {
        return tableMapper.selectTableStatusDetail();
    }

    /**
     * 更新餐桌状态
     */
    @Transactional
    public void updateTableStatus(Integer id, String status) {
        Table table = new Table();
        table.setId(id);
        table.setStatus(status);
        updateById(table);
    }

    /**
     * 检查餐桌占用冲突（同一餐桌不能有多个未完成订单）
     */
    public void checkTableConflict(String tableNumber) {
        int occupiedCount = tableMapper.checkTableOccupied(tableNumber);
        if (occupiedCount > 0) {
            throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, "该餐桌已有未完成订单，请选择其他餐桌");
        }
    }

    /**
     * 同步所有餐桌状态（根据订单数据更新餐桌状态）
     */
    @Transactional
    public int syncTableStatus() {
        int updatedCount = 0;
        
        // 获取所有餐桌
        List<Table> allTables = tableMapper.selectAll(new Table());
        
        for (Table table : allTables) {
            // 检查该餐桌是否有未完成订单
            int occupiedCount = tableMapper.checkTableOccupied(table.getTableNumber());
            
            String expectedStatus;
            if (occupiedCount > 0) {
                expectedStatus = "使用中";
            } else {
                expectedStatus = "空闲";
            }
            
            // 如果状态不一致，则更新
            if (!expectedStatus.equals(table.getStatus())) {
                updateTableStatus(table.getId(), expectedStatus);
                updatedCount++;
            }
        }
        
        return updatedCount;
    }
} 