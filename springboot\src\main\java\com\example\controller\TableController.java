package com.example.controller;

import com.example.common.Result;
import com.example.entity.Table;
import com.example.service.TableService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 餐桌管理控制器
 */
@RestController
@RequestMapping("/table")
public class TableController {

    @Autowired
    private TableService tableService;

    /**
     * 分页查询餐桌
     */
    @GetMapping("/selectPage")
    public Result selectPage(Table table,
                           @RequestParam(defaultValue = "1") Integer pageNum,
                           @RequestParam(defaultValue = "10") Integer pageSize) {
        PageInfo<Table> page = tableService.selectPage(table, pageNum, pageSize);
        return Result.success(page);
    }

    /**
     * 查询所有餐桌
     */
    @GetMapping("/selectAll")
    public Result selectAll(Table table) {
        List<Table> list = tableService.selectAll(table);
        return Result.success(list);
    }

    /**
     * 查询所有可下单的餐桌（排除有未完成订单的餐桌）
     */
    @GetMapping("/selectAvailable")
    public Result selectAvailable() {
        List<Table> list = tableService.selectAvailable();
        return Result.success(list);
    }

    /**
     * 根据ID查询餐桌
     */
    @GetMapping("/selectById/{id}")
    public Result selectById(@PathVariable Integer id) {
        Table table = tableService.selectById(id);
        return Result.success(table);
    }

    /**
     * 根据餐桌号查询餐桌
     */
    @GetMapping("/selectByTableNumber")
    public Result selectByTableNumber(@RequestParam String tableNumber) {
        Table table = tableService.selectByTableNumber(tableNumber);
        return Result.success(table);
    }

    /**
     * 新增餐桌
     */
    @PostMapping("/add")
    public Result add(@RequestBody Table table) {
        tableService.add(table);
        return Result.success();
    }

    /**
     * 更新餐桌
     */
    @PutMapping("/update")
    public Result updateById(@RequestBody Table table) {
        tableService.updateById(table);
        return Result.success();
    }

    /**
     * 删除餐桌
     */
    @DeleteMapping("/delete/{id}")
    public Result deleteById(@PathVariable Integer id) {
        tableService.deleteById(id);
        return Result.success();
    }

    /**
     * 检查餐桌是否可用
     */
    @GetMapping("/checkAvailable")
    public Result checkAvailable(@RequestParam String tableNumber) {
        boolean available = tableService.isTableAvailable(tableNumber);
        return Result.success(available);
    }

    /**
     * 查询餐桌状态统计
     */
    @GetMapping("/statusStats")
    public Result getTableStatusStats() {
        List<Map<String, Object>> stats = tableService.getTableStatusStats();
        return Result.success(stats);
    }

    /**
     * 查询餐桌详细状态（包含占用信息）
     */
    @GetMapping("/statusDetail")
    public Result getTableStatusDetail() {
        List<Map<String, Object>> detail = tableService.getTableStatusDetail();
        return Result.success(detail);
    }

    /**
     * 更新餐桌状态
     */
    @PutMapping("/updateStatus")
    public Result updateTableStatus(@RequestParam Integer id, @RequestParam String status) {
        tableService.updateTableStatus(id, status);
        return Result.success();
    }

    /**
     * 检查餐桌占用冲突
     */
    @GetMapping("/checkConflict")
    public Result checkTableConflict(@RequestParam String tableNumber) {
        try {
            tableService.checkTableConflict(tableNumber);
            return Result.success("餐桌可用");
        } catch (Exception e) {
            return Result.error("400", e.getMessage());
        }
    }

    /**
     * 同步所有餐桌状态（根据订单数据更新餐桌状态）
     */
    @PostMapping("/syncStatus")
    public Result syncTableStatus() {
        int updatedCount = tableService.syncTableStatus();
        return Result.success("已同步 " + updatedCount + " 个餐桌的状态");
    }
} 