/*
 Navicat Premium Data Transfer

 Source Server         : help
 Source Server Type    : MySQL
 Source Server Version : 80041 (8.0.41)
 Source Host           : localhost:3306
 Source Schema         : diancan002

 Target Server Type    : MySQL
 Target Server Version : 80041 (8.0.41)
 File Encoding         : 65001

 Date: 21/07/2025 23:02:58
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sf_admin
-- ----------------------------
DROP TABLE IF EXISTS `sf_admin`;
CREATE TABLE `sf_admin`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '姓名',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '电话',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像',
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'ADMIN' COMMENT '角色标识',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE,
  INDEX `idx_sf_admin_username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '管理员表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_admin
-- ----------------------------
INSERT INTO `sf_admin` VALUES (1, 'admin', '123456', '系统管理员', '13800138000', '<EMAIL>', NULL, 'ADMIN');

-- ----------------------------
-- Table structure for sf_blog
-- ----------------------------
DROP TABLE IF EXISTS `sf_blog`;
CREATE TABLE `sf_blog`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '博客标题',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '博客内容',
  `sf_cover_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '博客封面图片',
  `sf_created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '发布' COMMENT '博客状态',
  `sf_category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类名称',
  `sf_tags` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '博客标签',
  `sf_view_count` int NULL DEFAULT 0 COMMENT '浏览次数',
  `sf_author_id` int NULL DEFAULT NULL COMMENT '作者ID',
  `sf_author_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '作者名称',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sf_author_id`(`sf_author_id` ASC) USING BTREE,
  INDEX `idx_sf_blog_title`(`title` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '博客表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_blog
-- ----------------------------
INSERT INTO `sf_blog` VALUES (1, '美食制作技巧分享', '分享一些实用的美食制作技巧...', NULL, '2025-07-16 10:00:00', '发布', '美食', '美食,技巧', 15, 1, 'user1');
INSERT INTO `sf_blog` VALUES (2, '餐厅经营心得', '作为一名餐厅老板，我想分享一些经营心得...', NULL, '2025-07-17 15:30:00', '发布', '经营', '餐厅,经营', 23, 1, 'user1');
INSERT INTO `sf_blog` VALUES (3, '川菜文化探索', '川菜作为中国八大菜系之一，有着深厚的文化底蕴和独特的风味特色...', NULL, '2025-07-18 09:15:00', '发布', '美食文化', '川菜,文化,传统', 18, 4, '张三');
INSERT INTO `sf_blog` VALUES (4, '健康饮食搭配指南', '在快节奏的现代生活中，如何保持健康的饮食习惯是每个人都关心的话题...', NULL, '2025-07-19 14:20:00', '发布', '健康', '健康,饮食,营养', 32, 5, '李四');
INSERT INTO `sf_blog` VALUES (5, '夏日消暑甜品制作', '炎炎夏日，一份清爽的甜品能够带来丝丝凉意，今天分享几道简单易做的消暑甜品...', NULL, '2025-07-20 16:45:00', '发布', '甜品', '甜品,夏日,制作', 27, 6, '王五');
INSERT INTO `sf_blog` VALUES (6, '餐厅服务礼仪要点', '优质的服务是餐厅成功的关键因素之一，今天来谈谈餐厅服务中的一些重要礼仪...', NULL, '2025-07-21 11:30:00', '发布', '服务', '服务,礼仪,餐厅', 12, 7, '赵六');

-- ----------------------------
-- Table structure for sf_business
-- ----------------------------
DROP TABLE IF EXISTS `sf_business`;
CREATE TABLE `sf_business`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '姓名',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '电话',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像',
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'BUSINESS' COMMENT '角色标识',
  `sf_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '商家描述',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '待审核' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE,
  INDEX `idx_sf_business_username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商家表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_business
-- ----------------------------
INSERT INTO `sf_business` VALUES (1, 'business1', '123456', '商家1', '***********', '<EMAIL>', NULL, 'BUSINESS', '这是一家优质餐厅', '已审核');
INSERT INTO `sf_business` VALUES (2, 'business2', '123456', '商家2', '***********', '<EMAIL>', NULL, 'BUSINESS', '这是一家特色小吃店', '待审核');

-- ----------------------------
-- Table structure for sf_cart
-- ----------------------------
DROP TABLE IF EXISTS `sf_cart`;
CREATE TABLE `sf_cart`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `food_id` int NOT NULL COMMENT '商品ID',
  `quantity` int NOT NULL DEFAULT 1 COMMENT '商品数量',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_food`(`user_id` ASC, `food_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_food_id`(`food_id` ASC) USING BTREE,
  CONSTRAINT `fk_cart_food` FOREIGN KEY (`food_id`) REFERENCES `sf_food` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_cart_user` FOREIGN KEY (`user_id`) REFERENCES `sf_user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '购物车表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_cart
-- ----------------------------
INSERT INTO `sf_cart` VALUES (2, 1, 4, 1, '2025-07-21 19:29:49', '2025-07-21 19:29:49');
INSERT INTO `sf_cart` VALUES (3, 2, 6, 1, '2025-07-21 20:15:30', '2025-07-21 20:15:30');
INSERT INTO `sf_cart` VALUES (4, 3, 13, 2, '2025-07-21 21:30:15', '2025-07-21 21:30:15');
INSERT INTO `sf_cart` VALUES (5, 4, 5, 1, '2025-07-21 18:45:20', '2025-07-21 18:45:20');
INSERT INTO `sf_cart` VALUES (6, 4, 28, 1, '2025-07-21 19:20:35', '2025-07-21 19:20:35');
INSERT INTO `sf_cart` VALUES (7, 5, 11, 1, '2025-07-21 20:50:10', '2025-07-21 20:50:10');
INSERT INTO `sf_cart` VALUES (8, 5, 23, 2, '2025-07-21 21:10:25', '2025-07-21 21:10:25');
INSERT INTO `sf_cart` VALUES (9, 6, 7, 1, '2025-07-21 19:35:40', '2025-07-21 19:35:40');
INSERT INTO `sf_cart` VALUES (10, 7, 15, 1, '2025-07-21 20:25:55', '2025-07-21 20:25:55');
INSERT INTO `sf_cart` VALUES (11, 8, 9, 2, '2025-07-21 21:40:30', '2025-07-21 21:40:30');

-- ----------------------------
-- Table structure for sf_category
-- ----------------------------
DROP TABLE IF EXISTS `sf_category`;
CREATE TABLE `sf_category`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '分类描述',
  `icon` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类图标',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序权重',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '启用' COMMENT '分类状态：启用/禁用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_name`(`name` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_sf_category_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '食物分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_category
-- ----------------------------
INSERT INTO `sf_category` VALUES (1, '热门推荐', '系统推荐的热门食物', NULL, 1, '启用', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_category` VALUES (2, '川菜', '麻辣鲜香的川菜系列', NULL, 2, '启用', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_category` VALUES (3, '家常菜', '传统家常菜系列', NULL, 3, '启用', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_category` VALUES (4, '主食', '米饭、面条等主食类', NULL, 4, '启用', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_category` VALUES (5, '饮品', '各类饮料饮品', NULL, 5, '启用', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_category` VALUES (6, '甜品', '各类甜品点心', NULL, 6, '启用', '2025-07-21 17:50:04', '2025-07-21 17:50:04');

-- ----------------------------
-- Table structure for sf_comment
-- ----------------------------
DROP TABLE IF EXISTS `sf_comment`;
CREATE TABLE `sf_comment`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '评论内容',
  `time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '评论时间',
  `sf_user_id` int NULL DEFAULT NULL COMMENT '用户ID',
  `sf_user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户名',
  `sf_blog_id` int NULL DEFAULT NULL COMMENT '博客ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sf_user_id`(`sf_user_id` ASC) USING BTREE,
  INDEX `idx_sf_blog_id`(`sf_blog_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评论表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_comment
-- ----------------------------
INSERT INTO `sf_comment` VALUES (1, '这篇文章写得很好，学到了很多制作技巧！', '2025-07-17 11:30:00', 2, 'user2', 1);
INSERT INTO `sf_comment` VALUES (2, '川菜确实博大精深，期待更多分享。', '2025-07-18 15:20:00', 4, '张三', 3);
INSERT INTO `sf_comment` VALUES (3, '健康饮食很重要，感谢分享这些实用建议。', '2025-07-19 16:45:00', 5, '李四', 4);
INSERT INTO `sf_comment` VALUES (4, '夏天就需要这样的甜品，回家试试做。', '2025-07-20 18:30:00', 6, '王五', 5);
INSERT INTO `sf_comment` VALUES (5, '服务礼仪确实很重要，学习了。', '2025-07-21 13:15:00', 7, '赵六', 6);
INSERT INTO `sf_comment` VALUES (6, '作为餐厅从业者，这些经验很宝贵。', '2025-07-18 10:45:00', 8, '孙七', 2);

-- ----------------------------
-- Table structure for sf_complaint
-- ----------------------------
DROP TABLE IF EXISTS `sf_complaint`;
CREATE TABLE `sf_complaint`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `sf_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '投诉内容',
  `sf_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '投诉图片',
  `complaint_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '投诉类型',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '待处理' COMMENT '投诉状态',
  `sf_complaint_date` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '投诉日期',
  `reply` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '回复信息',
  `sf_user_id` int NULL DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sf_user_id`(`sf_user_id` ASC) USING BTREE,
  INDEX `idx_sf_complaint_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '投诉表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_complaint
-- ----------------------------
INSERT INTO `sf_complaint` VALUES (1, '123123123', '123123123123132', 'http://localhost:9090/files/1753103800761-默认头像.jpg', 'food_quality', '18822686555', '已处理', '2025-07-21 21:16:42', '感谢您的反馈，我们已经改进了相关问题。', 3);
INSERT INTO `sf_complaint` VALUES (2, '菜品温度问题', '今天点的菜品送到时温度偏低，影响了用餐体验。', NULL, 'service_quality', '13900139001', '已处理', '2025-07-18 19:30:00', '我们已经加强了保温措施，感谢您的建议。', 4);
INSERT INTO `sf_complaint` VALUES (3, '等餐时间过长', '等了40分钟才上菜，希望能够改善出餐速度。', NULL, 'service_speed', '13900139002', '已处理', '2025-07-19 20:15:00', '我们已经优化了厨房流程，会尽快改善。', 5);
INSERT INTO `sf_complaint` VALUES (4, '包装问题', '外卖包装有破损，汤汁洒漏，建议改进包装。', NULL, 'packaging', '13900139003', '处理中', '2025-07-20 14:45:00', '我们正在更换更好的包装材料。', 6);
INSERT INTO `sf_complaint` VALUES (5, '菜品口味偏咸', '今天的回锅肉口味偏咸，希望能够调整一下。', NULL, 'food_quality', '13900139004', '待处理', '2025-07-21 16:20:00', NULL, 7);

-- ----------------------------
-- Table structure for sf_food
-- ----------------------------
DROP TABLE IF EXISTS `sf_food`;
CREATE TABLE `sf_food`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '食品名称',
  `sf_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '食品图片',
  `sf_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '食品描述',
  `sf_category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '食品类型（保留兼容性）',
  `category_id` int NULL DEFAULT NULL COMMENT '分类ID',
  `sf_price` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '食品价格',
  `sf_stock` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '库存数量',
  `sf_shelf_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '上架' COMMENT '上架状态',
  `average_rating` decimal(3, 2) NULL DEFAULT 0.00 COMMENT '平均评分',
  `review_count` int NULL DEFAULT 0 COMMENT '评价总数',
  `last_review_time` datetime NULL DEFAULT NULL COMMENT '最新评价时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE,
  INDEX `idx_sf_food_name`(`name` ASC) USING BTREE,
  INDEX `idx_average_rating`(`average_rating` ASC) USING BTREE,
  INDEX `idx_review_count`(`review_count` ASC) USING BTREE,
  CONSTRAINT `fk_food_category` FOREIGN KEY (`category_id`) REFERENCES `sf_category` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '食品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_food
-- ----------------------------
INSERT INTO `sf_food` VALUES (1, '宫保鸡丁', NULL, '经典川菜，麻辣鲜香', '川菜', 2, '28.00', '100', '上架', 4.50, 2, '2025-07-16 19:45:00');
INSERT INTO `sf_food` VALUES (2, '红烧肉', NULL, '传统家常菜，肥而不腻', '家常菜', 3, '35.00', '49', '上架', 5.00, 1, '2025-07-17 13:20:00');
INSERT INTO `sf_food` VALUES (3, '麻婆豆腐', NULL, '四川名菜，麻辣鲜嫩', '川菜', 2, '18.00', '79', '上架', 3.00, 1, '2025-07-18 20:15:00');
INSERT INTO `sf_food` VALUES (4, '糖醋排骨', NULL, '酸甜可口，老少皆宜', '家常菜', 3, '32.00', '59', '上架', 5.00, 1, '2025-07-21 20:01:51');
-- 热门推荐
INSERT INTO `sf_food` VALUES (5, '口水鸡', NULL, '四川特色凉菜，麻辣鲜香', '热门推荐', 1, '26.00', '85', '上架', 4.20, 3, '2025-07-19 15:30:00');
INSERT INTO `sf_food` VALUES (6, '水煮鱼', NULL, '经典川菜，鱼肉鲜嫩', '热门推荐', 1, '48.00', '65', '上架', 4.80, 4, '2025-07-20 18:20:00');
INSERT INTO `sf_food` VALUES (7, '毛血旺', NULL, '重庆名菜，麻辣过瘾', '热门推荐', 1, '38.00', '72', '上架', 4.60, 2, '2025-07-19 20:10:00');
-- 川菜系列
INSERT INTO `sf_food` VALUES (8, '回锅肉', NULL, '四川传统名菜，肥而不腻', '川菜', 2, '32.00', '88', '上架', 4.40, 5, '2025-07-17 10:15:00');
INSERT INTO `sf_food` VALUES (9, '鱼香肉丝', NULL, '经典川菜，酸甜可口', '川菜', 2, '24.00', '95', '上架', 4.30, 3, '2025-07-17 14:30:00');
INSERT INTO `sf_food` VALUES (10, '辣子鸡', NULL, '重庆特色，香辣下饭', '川菜', 2, '36.00', '76', '上架', 4.50, 4, '2025-07-18 11:45:00');
INSERT INTO `sf_food` VALUES (11, '水煮牛肉', NULL, '川菜经典，牛肉嫩滑', '川菜', 2, '42.00', '68', '上架', 4.70, 3, '2025-07-18 16:20:00');
INSERT INTO `sf_food` VALUES (12, '夫妻肺片', NULL, '成都名小吃，麻辣鲜香', '川菜', 2, '28.00', '82', '上架', 4.20, 2, '2025-07-19 09:30:00');
INSERT INTO `sf_food` VALUES (13, '麻辣香锅', NULL, '自选配菜，麻辣过瘾', '川菜', 2, '45.00', '55', '上架', 4.60, 6, '2025-07-19 12:15:00');
INSERT INTO `sf_food` VALUES (14, '干煸豆角', NULL, '家常川菜，下饭神器', '川菜', 2, '22.00', '90', '上架', 4.10, 2, '2025-07-20 10:45:00');
-- 家常菜系列
INSERT INTO `sf_food` VALUES (15, '红烧排骨', NULL, '传统家常菜，香甜可口', '家常菜', 3, '38.00', '73', '上架', 4.50, 4, '2025-07-17 15:20:00');
INSERT INTO `sf_food` VALUES (16, '糖醋里脊', NULL, '酸甜开胃，老少皆宜', '家常菜', 3, '30.00', '86', '上架', 4.30, 3, '2025-07-18 13:40:00');
INSERT INTO `sf_food` VALUES (17, '青椒肉丝', NULL, '经典搭配，营养均衡', '家常菜', 3, '26.00', '92', '上架', 4.00, 2, '2025-07-18 17:10:00');
INSERT INTO `sf_food` VALUES (18, '西红柿鸡蛋', NULL, '家常必备，酸甜下饭', '家常菜', 3, '18.00', '98', '上架', 4.20, 5, '2025-07-19 08:30:00');
INSERT INTO `sf_food` VALUES (19, '醋溜土豆丝', NULL, '清爽开胃，制作简单', '家常菜', 3, '15.00', '100', '上架', 3.90, 3, '2025-07-19 14:20:00');
INSERT INTO `sf_food` VALUES (20, '蒜蓉菠菜', NULL, '清淡素菜，营养丰富', '家常菜', 3, '16.00', '95', '上架', 4.10, 2, '2025-07-20 11:30:00');
-- 主食系列
INSERT INTO `sf_food` VALUES (21, '白米饭', NULL, '优质大米，粒粒饱满', '主食', 4, '3.00', '200', '上架', 4.00, 8, '2025-07-16 08:00:00');
INSERT INTO `sf_food` VALUES (22, '蛋炒饭', NULL, '经典炒饭，香味扑鼻', '主食', 4, '12.00', '150', '上架', 4.20, 6, '2025-07-17 09:15:00');
INSERT INTO `sf_food` VALUES (23, '牛肉面', NULL, '汤浓面劲，牛肉鲜美', '主食', 4, '25.00', '80', '上架', 4.50, 4, '2025-07-18 12:30:00');
INSERT INTO `sf_food` VALUES (24, '小笼包', NULL, '皮薄馅大，汤汁鲜美', '主食', 4, '20.00', '120', '上架', 4.40, 5, '2025-07-19 07:45:00');
-- 饮品系列
INSERT INTO `sf_food` VALUES (25, '可乐', NULL, '经典碳酸饮料，冰爽解腻', '饮品', 5, '6.00', '300', '上架', 4.00, 10, '2025-07-16 10:00:00');
INSERT INTO `sf_food` VALUES (26, '鲜榨橙汁', NULL, '新鲜橙子榨制，维C丰富', '饮品', 5, '15.00', '100', '上架', 4.30, 7, '2025-07-17 11:20:00');
INSERT INTO `sf_food` VALUES (27, '柠檬蜂蜜茶', NULL, '清香柠檬，甜蜜蜂蜜', '饮品', 5, '12.00', '150', '上架', 4.20, 5, '2025-07-18 15:40:00');
-- 甜品系列
INSERT INTO `sf_food` VALUES (28, '提拉米苏', NULL, '意式经典，浓郁香甜', '甜品', 6, '28.00', '50', '上架', 4.70, 3, '2025-07-19 16:30:00');
INSERT INTO `sf_food` VALUES (29, '红豆冰', NULL, '夏日消暑，甜而不腻', '甜品', 6, '18.00', '80', '上架', 4.10, 4, '2025-07-20 14:15:00');
INSERT INTO `sf_food` VALUES (30, '芒果布丁', NULL, '热带风味，口感顺滑', '甜品', 6, '22.00', '60', '上架', 4.50, 2, '2025-07-21 10:20:00');

-- ----------------------------
-- Table structure for sf_food_review
-- ----------------------------
DROP TABLE IF EXISTS `sf_food_review`;
CREATE TABLE `sf_food_review`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int NOT NULL COMMENT '评价用户ID',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户昵称（冗余字段，提高查询效率）',
  `food_id` int NOT NULL COMMENT '菜品ID',
  `food_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '菜品名称（冗余字段）',
  `order_id` int NULL DEFAULT NULL COMMENT '关联订单ID',
  `rating` int NOT NULL COMMENT '评分（1-5星）',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '评价内容',
  `images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '评价图片（多张图片以逗号分隔）',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '正常' COMMENT '评价状态：正常、已删除、待审核',
  `reply_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '商家/管理员回复内容',
  `reply_time` datetime NULL DEFAULT NULL COMMENT '回复时间',
  `reply_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '回复人',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '评价时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_anonymous` tinyint NULL DEFAULT 0 COMMENT '是否匿名评价（0-否，1-是）',
  `helpful_count` int NULL DEFAULT 0 COMMENT '有用评价数量',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_food`(`user_id` ASC, `food_id` ASC) USING BTREE,
  INDEX `idx_food_id`(`food_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_rating`(`rating` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_sf_food_review_status`(`status` ASC) USING BTREE,
  INDEX `idx_sf_food_review_create_time`(`create_time` ASC) USING BTREE,
  CONSTRAINT `fk_review_food` FOREIGN KEY (`food_id`) REFERENCES `sf_food` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_review_user` FOREIGN KEY (`user_id`) REFERENCES `sf_user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 111 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '菜品评价表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_food_review
-- ----------------------------
INSERT INTO `sf_food_review` VALUES (1, 1, 'user1', 1, '宫保鸡丁', NULL, 5, '味道很棒，麻辣鲜香，分量也足够，值得推荐！', NULL, '正常', NULL, NULL, NULL, '2025-07-17 12:30:00', '2025-07-17 12:30:00', 0, 3);
INSERT INTO `sf_food_review` VALUES (2, 2, 'user2', 1, '宫保鸡丁', NULL, 4, '口感不错，就是稍微有点咸，整体还是很满意的。', NULL, '正常', NULL, NULL, NULL, '2025-07-17 19:45:00', '2025-07-17 19:45:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (3, 1, 'user1', 2, '红烧肉', NULL, 5, '红烧肉做得很地道，肥而不腻，入口即化，强烈推荐！', NULL, '正常', NULL, NULL, NULL, '2025-07-18 13:20:00', '2025-07-18 13:20:00', 0, 5);
INSERT INTO `sf_food_review` VALUES (4, 2, 'user2', 3, '麻婆豆腐', NULL, 3, '豆腐很嫩，但是辣度不够，希望能做得更正宗一些。', NULL, '正常', NULL, NULL, NULL, '2025-07-19 20:15:00', '2025-07-19 20:15:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (5, 1, '用户1', 4, '糖醋排骨', NULL, 5, '很好吃超级无敌好吃啊', NULL, '正常', NULL, NULL, NULL, '2025-07-21 20:01:51', '2025-07-21 20:01:51', 1, 0);
-- 新增评价数据
INSERT INTO `sf_food_review` VALUES (6, 4, '张三', 5, '口水鸡', NULL, 4, '口水鸡做得很正宗，麻辣鲜香，就是稍微有点油腻。', NULL, '正常', NULL, NULL, NULL, '2025-07-19 16:30:00', '2025-07-19 16:30:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (7, 5, '李四', 5, '口水鸡', NULL, 4, '味道不错，鸡肉很嫩，调料很香，值得再来。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 11:15:00', '2025-07-20 11:15:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (8, 6, '王五', 5, '口水鸡', NULL, 4, '经典川菜，麻辣适中，很下饭，推荐！', NULL, '正常', NULL, NULL, NULL, '2025-07-20 18:45:00', '2025-07-20 18:45:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (9, 3, '123123', 6, '水煮鱼', NULL, 5, '鱼肉非常鲜嫩，汤底麻辣鲜香，分量很足，超级满意！', NULL, '正常', NULL, NULL, NULL, '2025-07-20 19:30:00', '2025-07-20 19:30:00', 0, 4);
INSERT INTO `sf_food_review` VALUES (10, 4, '张三', 6, '水煮鱼', NULL, 5, '这是我吃过最好吃的水煮鱼，鱼片嫩滑，辣度刚好。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 12:20:00', '2025-07-21 12:20:00', 0, 3);
INSERT INTO `sf_food_review` VALUES (11, 7, '赵六', 6, '水煮鱼', NULL, 5, '超级棒！鱼肉新鲜，调料丰富，下次还要点。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 15:40:00', '2025-07-21 15:40:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (12, 8, '孙七', 6, '水煮鱼', NULL, 4, '味道很好，就是有点辣，不过很过瘾。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 20:10:00', '2025-07-21 20:10:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (13, 5, '李四', 7, '毛血旺', NULL, 5, '重庆味道很正宗，麻辣鲜香，血旺嫩滑。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 14:25:00', '2025-07-20 14:25:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (14, 6, '王五', 7, '毛血旺', NULL, 4, '料很丰富，味道不错，就是有点咸。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 11:50:00', '2025-07-21 11:50:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (15, 1, 'user1', 8, '回锅肉', NULL, 4, '经典川菜，肥瘦相间，很香很下饭。', NULL, '正常', NULL, NULL, NULL, '2025-07-18 14:30:00', '2025-07-18 14:30:00', 0, 3);
INSERT INTO `sf_food_review` VALUES (16, 2, 'user2', 8, '回锅肉', NULL, 4, '味道正宗，就是有点油，整体还不错。', NULL, '正常', NULL, NULL, NULL, '2025-07-19 10:15:00', '2025-07-19 10:15:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (17, 4, '张三', 8, '回锅肉', NULL, 5, '超级好吃！肉质鲜美，调料丰富，强烈推荐。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 16:40:00', '2025-07-20 16:40:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (18, 7, '赵六', 8, '回锅肉', NULL, 4, '传统川菜，味道不错，分量也够。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 13:25:00', '2025-07-21 13:25:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (19, 8, '孙七', 8, '回锅肉', NULL, 5, '非常正宗的川菜，肉片厚实，很满意。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 18:15:00', '2025-07-21 18:15:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (20, 3, '123123', 9, '鱼香肉丝', NULL, 4, '酸甜可口，很开胃，肉丝很嫩。', NULL, '正常', NULL, NULL, NULL, '2025-07-18 17:20:00', '2025-07-18 17:20:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (21, 5, '李四', 9, '鱼香肉丝', NULL, 4, '经典川菜，味道很棒，就是稍微有点甜。', NULL, '正常', NULL, NULL, NULL, '2025-07-19 12:45:00', '2025-07-19 12:45:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (22, 6, '王五', 9, '鱼香肉丝', NULL, 5, '非常好吃，酸甜适中，肉丝嫩滑，推荐！', NULL, '正常', NULL, NULL, NULL, '2025-07-20 15:30:00', '2025-07-20 15:30:00', 0, 3);
INSERT INTO `sf_food_review` VALUES (23, 1, 'user1', 10, '辣子鸡', NULL, 5, '重庆特色，香辣过瘾，鸡肉很嫩。', NULL, '正常', NULL, NULL, NULL, '2025-07-19 13:15:00', '2025-07-19 13:15:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (24, 4, '张三', 10, '辣子鸡', NULL, 4, '味道不错，就是有点干，整体还可以。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 10:20:00', '2025-07-20 10:20:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (25, 7, '赵六', 10, '辣子鸡', NULL, 5, '超级香辣，很下饭，分量也足。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 14:40:00', '2025-07-21 14:40:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (26, 8, '孙七', 10, '辣子鸡', NULL, 4, '辣度刚好，鸡肉新鲜，值得推荐。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 19:25:00', '2025-07-21 19:25:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (27, 2, 'user2', 11, '水煮牛肉', NULL, 5, '牛肉片很嫩，汤底麻辣鲜香，超级棒！', NULL, '正常', NULL, NULL, NULL, '2025-07-19 16:50:00', '2025-07-19 16:50:00', 0, 4);
INSERT INTO `sf_food_review` VALUES (28, 5, '李四', 11, '水煮牛肉', NULL, 4, '味道很好，牛肉质量不错，就是有点辣。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 12:30:00', '2025-07-20 12:30:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (29, 6, '王五', 11, '水煮牛肉', NULL, 5, '经典川菜，牛肉嫩滑，汤汁浓郁，很满意。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 16:15:00', '2025-07-21 16:15:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (30, 3, '123123', 12, '夫妻肺片', NULL, 4, '成都特色，麻辣鲜香，口感丰富。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 09:40:00', '2025-07-20 09:40:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (31, 7, '赵六', 12, '夫妻肺片', NULL, 4, '传统小吃，味道正宗，分量适中。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 11:20:00', '2025-07-21 11:20:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (32, 1, 'user1', 13, '麻辣香锅', NULL, 5, '配菜丰富，可以自选，麻辣过瘾！', NULL, '正常', NULL, NULL, NULL, '2025-07-20 13:45:00', '2025-07-20 13:45:00', 0, 3);
INSERT INTO `sf_food_review` VALUES (33, 4, '张三', 13, '麻辣香锅', NULL, 4, '味道不错，配菜新鲜，就是有点油。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 18:20:00', '2025-07-20 18:20:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (34, 8, '孙七', 13, '麻辣香锅', NULL, 5, '超级好吃，配菜种类多，很过瘾。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 12:50:00', '2025-07-21 12:50:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (35, 2, 'user2', 13, '麻辣香锅', NULL, 4, '味道很棒，分量足，性价比高。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 17:30:00', '2025-07-21 17:30:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (36, 5, '李四', 13, '麻辣香锅', NULL, 5, '非常满意，配菜搭配合理，很香很辣。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 20:40:00', '2025-07-21 20:40:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (37, 6, '王五', 13, '麻辣香锅', NULL, 4, '整体不错，就是稍微有点咸，其他都很好。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 21:15:00', '2025-07-21 21:15:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (38, 3, '123123', 14, '干煸豆角', NULL, 4, '家常川菜，豆角很嫩，很下饭。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 10:30:00', '2025-07-21 10:30:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (39, 7, '赵六', 14, '干煸豆角', NULL, 4, '制作精细，豆角入味，口感不错。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 15:20:00', '2025-07-21 15:20:00', 0, 0);
-- 家常菜评价
INSERT INTO `sf_food_review` VALUES (40, 1, 'user1', 15, '红烧排骨', NULL, 5, '排骨炖得很烂，香甜可口，很下饭。', NULL, '正常', NULL, NULL, NULL, '2025-07-18 16:30:00', '2025-07-18 16:30:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (41, 4, '张三', 15, '红烧排骨', NULL, 4, '味道不错，排骨很嫩，就是有点甜。', NULL, '正常', NULL, NULL, NULL, '2025-07-19 11:45:00', '2025-07-19 11:45:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (42, 5, '李四', 15, '红烧排骨', NULL, 5, '经典家常菜，制作精良，很满意。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 14:20:00', '2025-07-20 14:20:00', 0, 3);
INSERT INTO `sf_food_review` VALUES (43, 8, '孙七', 15, '红烧排骨', NULL, 4, '传统做法，排骨软烂，味道正宗。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 13:10:00', '2025-07-21 13:10:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (44, 2, 'user2', 16, '糖醋里脊', NULL, 4, '酸甜开胃，里脊肉很嫩，老少皆宜。', NULL, '正常', NULL, NULL, NULL, '2025-07-19 14:50:00', '2025-07-19 14:50:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (45, 6, '王五', 16, '糖醋里脊', NULL, 4, '口感不错，酸甜适中，分量也够。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 17:15:00', '2025-07-20 17:15:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (46, 7, '赵六', 16, '糖醋里脊', NULL, 5, '超级好吃，外酥内嫩，酸甜可口。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 12:40:00', '2025-07-21 12:40:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (47, 3, '123123', 17, '青椒肉丝', NULL, 4, '经典搭配，青椒脆嫩，肉丝鲜美。', NULL, '正常', NULL, NULL, NULL, '2025-07-19 18:25:00', '2025-07-19 18:25:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (48, 8, '孙七', 17, '青椒肉丝', NULL, 4, '家常菜做得很好，营养搭配合理。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 16:50:00', '2025-07-21 16:50:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (49, 1, 'user1', 18, '西红柿鸡蛋', NULL, 4, '经典家常菜，酸甜下饭，制作用心。', NULL, '正常', NULL, NULL, NULL, '2025-07-19 09:15:00', '2025-07-19 09:15:00', 0, 3);
INSERT INTO `sf_food_review` VALUES (50, 4, '张三', 18, '西红柿鸡蛋', NULL, 4, '味道很棒，西红柿很新鲜，鸡蛋嫩滑。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 11:30:00', '2025-07-20 11:30:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (51, 5, '李四', 18, '西红柿鸡蛋', NULL, 4, '家常必备，制作精细，很下饭。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 15:45:00', '2025-07-20 15:45:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (52, 6, '王五', 18, '西红柿鸡蛋', NULL, 5, '超级好吃，酸甜适中，很有家的味道。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 10:20:00', '2025-07-21 10:20:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (53, 7, '赵六', 18, '西红柿鸡蛋', NULL, 4, '经典搭配，味道正宗，分量适中。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 14:35:00', '2025-07-21 14:35:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (54, 2, 'user2', 19, '醋溜土豆丝', NULL, 4, '清爽开胃，土豆丝切得很细，口感脆嫩。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 12:15:00', '2025-07-20 12:15:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (55, 3, '123123', 19, '醋溜土豆丝', NULL, 4, '制作简单但味道很好，很开胃。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 16:40:00', '2025-07-20 16:40:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (56, 8, '孙七', 19, '醋溜土豆丝', NULL, 3, '味道一般，土豆丝有点软，不够脆。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 11:25:00', '2025-07-21 11:25:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (57, 4, '张三', 20, '蒜蓉菠菜', NULL, 4, '清淡素菜，菠菜很新鲜，蒜香浓郁。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 12:30:00', '2025-07-21 12:30:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (58, 5, '李四', 20, '蒜蓉菠菜', NULL, 4, '营养丰富，制作精细，很健康的菜品。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 18:45:00', '2025-07-21 18:45:00', 0, 0);
-- 主食评价
INSERT INTO `sf_food_review` VALUES (59, 1, 'user1', 21, '白米饭', NULL, 4, '米饭粒粒饱满，质量很好，很香。', NULL, '正常', NULL, NULL, NULL, '2025-07-17 12:20:00', '2025-07-17 12:20:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (60, 2, 'user2', 21, '白米饭', NULL, 4, '优质大米，口感不错，很满意。', NULL, '正常', NULL, NULL, NULL, '2025-07-18 13:45:00', '2025-07-18 13:45:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (61, 3, '123123', 21, '白米饭', NULL, 4, '米饭很香，分量足够，性价比高。', NULL, '正常', NULL, NULL, NULL, '2025-07-19 11:30:00', '2025-07-19 11:30:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (62, 4, '张三', 21, '白米饭', NULL, 4, '基本主食，质量稳定，没什么问题。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 14:15:00', '2025-07-20 14:15:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (63, 5, '李四', 21, '白米饭', NULL, 4, '米饭很棒，粒粒分明，很香甜。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 18:30:00', '2025-07-20 18:30:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (64, 6, '王五', 21, '白米饭', NULL, 4, '优质主食，口感很好，推荐。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 09:45:00', '2025-07-21 09:45:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (65, 7, '赵六', 21, '白米饭', NULL, 4, '米饭质量不错，很香很软。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 13:20:00', '2025-07-21 13:20:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (66, 8, '孙七', 21, '白米饭', NULL, 4, '基础主食，质量稳定，很满意。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 17:10:00', '2025-07-21 17:10:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (67, 1, 'user1', 22, '蛋炒饭', NULL, 4, '经典炒饭，蛋香浓郁，粒粒分明。', NULL, '正常', NULL, NULL, NULL, '2025-07-18 10:30:00', '2025-07-18 10:30:00', 0, 3);
INSERT INTO `sf_food_review` VALUES (68, 3, '123123', 22, '蛋炒饭', NULL, 4, '制作精细，味道很好，很香。', NULL, '正常', NULL, NULL, NULL, '2025-07-19 15:20:00', '2025-07-19 15:20:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (69, 5, '李四', 22, '蛋炒饭', NULL, 4, '经典搭配，鸡蛋很香，米饭不粘。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 12:45:00', '2025-07-20 12:45:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (70, 7, '赵六', 22, '蛋炒饭', NULL, 5, '超级好吃，蛋香米香，很满意。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 11:15:00', '2025-07-21 11:15:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (71, 8, '孙七', 22, '蛋炒饭', NULL, 4, '传统做法，味道正宗，分量足。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 16:30:00', '2025-07-21 16:30:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (72, 2, 'user2', 22, '蛋炒饭', NULL, 4, '制作用心，口感很好，推荐。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 20:25:00', '2025-07-21 20:25:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (73, 2, 'user2', 23, '牛肉面', NULL, 5, '汤浓面劲，牛肉很嫩，超级满意！', NULL, '正常', NULL, NULL, NULL, '2025-07-19 13:30:00', '2025-07-19 13:30:00', 0, 4);
INSERT INTO `sf_food_review` VALUES (74, 4, '张三', 23, '牛肉面', NULL, 4, '面条很劲道，汤底浓郁，牛肉分量足。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 11:40:00', '2025-07-20 11:40:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (75, 6, '王五', 23, '牛肉面', NULL, 5, '非常棒的牛肉面，汤鲜肉嫩，很满意。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 14:50:00', '2025-07-21 14:50:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (76, 8, '孙七', 23, '牛肉面', NULL, 4, '味道不错，面条有嚼劲，汤很香。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 19:15:00', '2025-07-21 19:15:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (77, 1, 'user1', 24, '小笼包', NULL, 4, '皮薄馅大，汤汁鲜美，制作精良。', NULL, '正常', NULL, NULL, NULL, '2025-07-19 08:45:00', '2025-07-19 08:45:00', 0, 3);
INSERT INTO `sf_food_review` VALUES (78, 3, '123123', 24, '小笼包', NULL, 5, '超级好吃！汤汁丰富，肉馅鲜美。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 10:20:00', '2025-07-20 10:20:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (79, 5, '李四', 24, '小笼包', NULL, 4, '传统小笼包，皮很薄，汤汁很鲜。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 15:30:00', '2025-07-20 15:30:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (80, 6, '王五', 24, '小笼包', NULL, 5, '非常棒，皮薄汁多，肉馅调味很好。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 09:10:00', '2025-07-21 09:10:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (81, 7, '赵六', 24, '小笼包', NULL, 4, '制作精细，口感很好，推荐尝试。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 12:25:00', '2025-07-21 12:25:00', 0, 2);
-- 饮品评价
INSERT INTO `sf_food_review` VALUES (82, 1, 'user1', 25, '可乐', NULL, 4, '经典饮料，冰爽解腻，很棒。', NULL, '正常', NULL, NULL, NULL, '2025-07-17 14:20:00', '2025-07-17 14:20:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (83, 2, 'user2', 25, '可乐', NULL, 4, '碳酸饮料，口感不错，很解腻。', NULL, '正常', NULL, NULL, NULL, '2025-07-18 16:45:00', '2025-07-18 16:45:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (84, 3, '123123', 25, '可乐', NULL, 4, '经典搭配，冰镇的很爽口。', NULL, '正常', NULL, NULL, NULL, '2025-07-19 12:30:00', '2025-07-19 12:30:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (85, 4, '张三', 25, '可乐', NULL, 4, '标准饮料，质量稳定，没问题。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 13:15:00', '2025-07-20 13:15:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (86, 5, '李四', 25, '可乐', NULL, 4, '很好喝，冰爽解腻，推荐。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 17:40:00', '2025-07-20 17:40:00', 0, 3);
INSERT INTO `sf_food_review` VALUES (87, 6, '王五', 25, '可乐', NULL, 4, '经典饮品，口感很好，很满意。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 10:50:00', '2025-07-21 10:50:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (88, 7, '赵六', 25, '可乐', NULL, 4, '碳酸饮料，很爽口，解腻效果好。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 15:25:00', '2025-07-21 15:25:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (89, 8, '孙七', 25, '可乐', NULL, 4, '经典选择，冰镇后很好喝。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 18:35:00', '2025-07-21 18:35:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (90, 2, 'user2', 26, '鲜榨橙汁', NULL, 4, '新鲜橙子榨制，维C丰富，很健康。', NULL, '正常', NULL, NULL, NULL, '2025-07-18 12:40:00', '2025-07-18 12:40:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (91, 4, '张三', 26, '鲜榨橙汁', NULL, 5, '超级新鲜，橙香浓郁，很好喝！', NULL, '正常', NULL, NULL, NULL, '2025-07-19 16:20:00', '2025-07-19 16:20:00', 0, 3);
INSERT INTO `sf_food_review` VALUES (92, 5, '李四', 26, '鲜榨橙汁', NULL, 4, '口感很好，很新鲜，酸甜适中。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 11:15:00', '2025-07-20 11:15:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (93, 7, '赵六', 26, '鲜榨橙汁', NULL, 4, '新鲜果汁，营养丰富，推荐。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 14:30:00', '2025-07-21 14:30:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (94, 8, '孙七', 26, '鲜榨橙汁', NULL, 5, '非常棒的果汁，橙味浓郁，很满意。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 17:45:00', '2025-07-21 17:45:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (95, 1, 'user1', 26, '鲜榨橙汁', NULL, 4, '果汁很新鲜，橙香浓郁，健康饮品。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 20:30:00', '2025-07-21 20:30:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (96, 6, '王五', 26, '鲜榨橙汁', NULL, 4, '新鲜果汁，口感很好，维C丰富。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 21:10:00', '2025-07-21 21:10:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (97, 3, '123123', 27, '柠檬蜂蜜茶', NULL, 4, '清香柠檬，甜蜜蜂蜜，很好喝。', NULL, '正常', NULL, NULL, NULL, '2025-07-19 16:40:00', '2025-07-19 16:40:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (98, 5, '李四', 27, '柠檬蜂蜜茶', NULL, 4, '酸甜适中，很清香，解腻效果好。', NULL, '正常', NULL, NULL, NULL, '2025-07-20 14:25:00', '2025-07-20 14:25:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (99, 6, '王五', 27, '柠檬蜂蜜茶', NULL, 5, '超级好喝，柠檬香味浓郁，蜂蜜甜度刚好。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 11:35:00', '2025-07-21 11:35:00', 0, 3);
INSERT INTO `sf_food_review` VALUES (100, 7, '赵六', 27, '柠檬蜂蜜茶', NULL, 4, '清香怡人，酸甜可口，很满意。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 16:20:00', '2025-07-21 16:20:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (101, 8, '孙七', 27, '柠檬蜂蜜茶', NULL, 4, '健康饮品，口感很好，推荐。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 19:50:00', '2025-07-21 19:50:00', 0, 1);
-- 甜品评价
INSERT INTO `sf_food_review` VALUES (102, 2, 'user2', 28, '提拉米苏', NULL, 5, '意式经典，浓郁香甜，口感丰富！', NULL, '正常', NULL, NULL, NULL, '2025-07-20 17:30:00', '2025-07-20 17:30:00', 0, 4);
INSERT INTO `sf_food_review` VALUES (103, 4, '张三', 28, '提拉米苏', NULL, 5, '超级棒的甜品，奶香浓郁，很满意。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 13:45:00', '2025-07-21 13:45:00', 0, 3);
INSERT INTO `sf_food_review` VALUES (104, 6, '王五', 28, '提拉米苏', NULL, 4, '经典意式甜品，制作精良，很好吃。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 18:20:00', '2025-07-21 18:20:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (105, 1, 'user1', 29, '红豆冰', NULL, 4, '夏日消暑，甜而不腻，红豆很香。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 15:15:00', '2025-07-21 15:15:00', 0, 2);
INSERT INTO `sf_food_review` VALUES (106, 3, '123123', 29, '红豆冰', NULL, 4, '清爽解暑，红豆软糯，很好吃。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 16:40:00', '2025-07-21 16:40:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (107, 5, '李四', 29, '红豆冰', NULL, 4, '夏季必备，口感很好，甜度适中。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 19:25:00', '2025-07-21 19:25:00', 0, 0);
INSERT INTO `sf_food_review` VALUES (108, 7, '赵六', 29, '红豆冰', NULL, 4, '消暑甜品，制作精细，很满意。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 21:05:00', '2025-07-21 21:05:00', 0, 1);
INSERT INTO `sf_food_review` VALUES (109, 4, '张三', 30, '芒果布丁', NULL, 5, '热带风味，口感顺滑，芒果香浓！', NULL, '正常', NULL, NULL, NULL, '2025-07-21 11:40:00', '2025-07-21 11:40:00', 0, 3);
INSERT INTO `sf_food_review` VALUES (110, 8, '孙七', 30, '芒果布丁', NULL, 4, '布丁很嫩滑，芒果味很浓，不错。', NULL, '正常', NULL, NULL, NULL, '2025-07-21 17:55:00', '2025-07-21 17:55:00', 0, 2);

-- ----------------------------
-- Table structure for sf_message
-- ----------------------------
DROP TABLE IF EXISTS `sf_message`;
CREATE TABLE `sf_message`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sf_user_id` int NULL DEFAULT NULL COMMENT '用户ID',
  `sf_question` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '问题内容',
  `reply` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '回复内容',
  `sf_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '留言图片',
  `sf_leave_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '留言时间',
  `sf_reply_time` datetime NULL DEFAULT NULL COMMENT '回复时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sf_user_id`(`sf_user_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '留言表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_message
-- ----------------------------
INSERT INTO `sf_message` VALUES (1, 1, '希望能够增加更多素食选择，谢谢！', '我们会考虑增加更多素食菜品，感谢建议。', NULL, '2025-07-17 14:20:00', '2025-07-17 16:30:00');
INSERT INTO `sf_message` VALUES (2, 2, '能否提供无糖饮品选择？', '我们已经准备上架几款无糖饮品，敬请期待。', NULL, '2025-07-18 11:45:00', '2025-07-18 15:20:00');
INSERT INTO `sf_message` VALUES (3, 4, '餐厅环境很好，希望能够增加一些背景音乐。', '感谢建议，我们会考虑播放轻柔的背景音乐。', NULL, '2025-07-19 13:30:00', '2025-07-19 17:45:00');
INSERT INTO `sf_message` VALUES (4, 5, '外卖能否提供餐具选择？环保考虑。', '好建议！我们正在准备可选餐具的功能。', NULL, '2025-07-20 16:15:00', '2025-07-20 19:30:00');
INSERT INTO `sf_message` VALUES (5, 6, '希望能够推出会员卡优惠活动。', '会员优惠活动正在筹备中，请关注我们的公告。', NULL, '2025-07-21 10:25:00', '2025-07-21 14:40:00');
INSERT INTO `sf_message` VALUES (6, 7, '菜品分量很足，口味也很棒，继续保持！', '感谢您的认可，我们会继续努力提供优质服务。', NULL, '2025-07-21 18:50:00', NULL);

-- ----------------------------
-- Table structure for sf_notice
-- ----------------------------
DROP TABLE IF EXISTS `sf_notice`;
CREATE TABLE `sf_notice`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '内容',
  `time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建时间',
  `user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sf_notice_title`(`title` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '公告信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_notice
-- ----------------------------
INSERT INTO `sf_notice` VALUES (1, '系统维护通知', '系统将于本周日凌晨2:00-4:00进行维护，请合理安排时间。', '2025-07-16 10:00:00', 'admin');
INSERT INTO `sf_notice` VALUES (2, '新功能上线', '新增了在线客服功能，欢迎大家使用。', '2025-07-17 14:30:00', 'admin');
INSERT INTO `sf_notice` VALUES (3, '夏季新品上市', '本店推出多款夏季清爽菜品和消暑饮品，欢迎品尝！', '2025-07-18 09:00:00', 'admin');
INSERT INTO `sf_notice` VALUES (4, '营业时间调整', '由于夏季客流增加，本店营业时间延长至晚上11点，感谢支持！', '2025-07-19 16:20:00', 'admin');
INSERT INTO `sf_notice` VALUES (5, '会员积分活动', '即日起，会员消费满100元可获得10积分，积分可兑换精美礼品。', '2025-07-20 12:15:00', 'admin');
INSERT INTO `sf_notice` VALUES (6, '食品安全承诺', '本店严格按照食品安全标准操作，确保每一道菜品的质量和安全。', '2025-07-21 08:30:00', 'admin');

-- ----------------------------
-- Table structure for sf_order
-- ----------------------------
DROP TABLE IF EXISTS `sf_order`;
CREATE TABLE `sf_order`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sf_user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户名',
  `sf_user_id` int NULL DEFAULT NULL COMMENT '用户ID',
  `table_id` int NULL DEFAULT NULL COMMENT '餐桌ID',
  `table_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '餐桌号',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '待支付' COMMENT '订单状态：待支付、已支付、制作中、待取餐、已完成、已取消、退款中、已退款',
  `sf_order_number` int NULL DEFAULT NULL COMMENT '订单编号',
  `sf_create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `sf_remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户备注',
  `sf_evaluation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户评价',
  `sf_total_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '订单总价格',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sf_user_id`(`sf_user_id` ASC) USING BTREE,
  INDEX `idx_sf_order_number`(`sf_order_number` ASC) USING BTREE,
  INDEX `idx_table_id`(`table_id` ASC) USING BTREE,
  INDEX `idx_sf_order_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_order
-- ----------------------------
INSERT INTO `sf_order` VALUES (1, 'user1', 1, 1, 'A01', '已完成', 40527290, '2025-07-21 19:25:30', '购物车批量结算，共1件商品', NULL, 32.00);
INSERT INTO `sf_order` VALUES (2, '123123', 3, 1, 'A01', '已完成', 78949677, '2025-07-21 21:09:17', '单个商品结算：麻婆豆腐', NULL, 18.00);
INSERT INTO `sf_order` VALUES (3, '123123', 3, 1, 'A01', '制作中', 19837094, '2025-07-21 21:14:38', '立即购买：红烧肉', NULL, 35.00);
-- 新增订单数据
INSERT INTO `sf_order` VALUES (4, 'user1', 1, 2, 'A02', '已完成', 12345678, '2025-07-17 12:30:00', '午餐订单', NULL, 56.00);
INSERT INTO `sf_order` VALUES (5, 'user2', 2, 3, 'A03', '已完成', 23456789, '2025-07-17 18:45:00', '晚餐订单', NULL, 74.00);
INSERT INTO `sf_order` VALUES (6, '张三', 4, 4, 'A04', '已完成', 34567890, '2025-07-18 11:20:00', '朋友聚餐', NULL, 128.00);
INSERT INTO `sf_order` VALUES (7, '李四', 5, 5, 'B01', '已完成', 45678901, '2025-07-18 13:15:00', '包间用餐', NULL, 186.00);
INSERT INTO `sf_order` VALUES (8, '王五', 6, 6, 'B02', '已完成', 56789012, '2025-07-18 19:30:00', '家庭聚餐', NULL, 245.00);
INSERT INTO `sf_order` VALUES (9, '赵六', 7, 7, 'C01', '已完成', 67890123, '2025-07-19 12:10:00', '情侣用餐', NULL, 89.00);
INSERT INTO `sf_order` VALUES (10, '孙七', 8, 8, 'C02', '已完成', 78901234, '2025-07-19 14:25:00', '商务午餐', NULL, 156.00);
INSERT INTO `sf_order` VALUES (11, 'user1', 1, 2, 'A02', '已完成', 89012345, '2025-07-19 17:40:00', '下午茶', NULL, 46.00);
INSERT INTO `sf_order` VALUES (12, 'user2', 2, 3, 'A03', '已完成', 90123456, '2025-07-19 20:15:00', '夜宵订单', NULL, 68.00);
INSERT INTO `sf_order` VALUES (13, '张三', 4, 1, 'A01', '已完成', 01234567, '2025-07-20 11:30:00', '工作餐', NULL, 95.00);
INSERT INTO `sf_order` VALUES (14, '李四', 5, 4, 'A04', '已完成', 12340567, '2025-07-20 13:45:00', '午餐时光', NULL, 112.00);
INSERT INTO `sf_order` VALUES (15, '王五', 6, 5, 'B01', '已完成', 23450678, '2025-07-20 16:20:00', '下午聚会', NULL, 178.00);
INSERT INTO `sf_order` VALUES (16, '赵六', 7, 6, 'B02', '已完成', 34560789, '2025-07-20 19:50:00', '晚餐聚餐', NULL, 203.00);
INSERT INTO `sf_order` VALUES (17, '孙七', 8, 7, 'C01', '已完成', 45670890, '2025-07-21 10:15:00', '早午餐', NULL, 87.00);
INSERT INTO `sf_order` VALUES (18, 'user1', 1, 8, 'C02', '已完成', 56780901, '2025-07-21 12:40:00', '午餐订单', NULL, 134.00);
INSERT INTO `sf_order` VALUES (19, 'user2', 2, 2, 'A02', '已完成', 67890012, '2025-07-21 15:25:00', '下午用餐', NULL, 76.00);
INSERT INTO `sf_order` VALUES (20, '张三', 4, 3, 'A03', '已完成', 78900123, '2025-07-21 18:10:00', '晚餐时间', NULL, 98.00);
INSERT INTO `sf_order` VALUES (21, '李四', 5, 1, 'A01', '待取餐', 89010234, '2025-07-21 20:30:00', '夜宵订单', NULL, 145.00);
INSERT INTO `sf_order` VALUES (22, '王五', 6, 4, 'A04', '制作中', 90120345, '2025-07-21 21:45:00', '宵夜时光', NULL, 167.00);
INSERT INTO `sf_order` VALUES (23, '赵六', 7, 5, 'B01', '已支付', 01230456, '2025-07-21 22:15:00', '深夜美食', NULL, 189.00);

-- ----------------------------
-- Table structure for sf_order_item
-- ----------------------------
DROP TABLE IF EXISTS `sf_order_item`;
CREATE TABLE `sf_order_item`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` int NOT NULL COMMENT '订单ID',
  `food_id` int NOT NULL COMMENT '商品ID',
  `food_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品名称',
  `food_price` decimal(10, 2) NOT NULL COMMENT '商品单价',
  `quantity` int NOT NULL COMMENT '商品数量',
  `subtotal` decimal(10, 2) NOT NULL COMMENT '小计金额',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE,
  INDEX `idx_food_id`(`food_id` ASC) USING BTREE,
  CONSTRAINT `fk_order_item_order` FOREIGN KEY (`order_id`) REFERENCES `sf_order` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 88 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单详情表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_order_item
-- ----------------------------
INSERT INTO `sf_order_item` VALUES (1, 1, 4, '糖醋排骨', 32.00, 1, 32.00);
INSERT INTO `sf_order_item` VALUES (2, 2, 3, '麻婆豆腐', 18.00, 1, 18.00);
INSERT INTO `sf_order_item` VALUES (3, 3, 2, '红烧肉', 35.00, 1, 35.00);
-- 新增订单详情数据
INSERT INTO `sf_order_item` VALUES (4, 4, 1, '宫保鸡丁', 28.00, 1, 28.00);
INSERT INTO `sf_order_item` VALUES (5, 4, 2, '红烧肉', 35.00, 1, 35.00);
INSERT INTO `sf_order_item` VALUES (6, 5, 8, '回锅肉', 32.00, 1, 32.00);
INSERT INTO `sf_order_item` VALUES (7, 5, 9, '鱼香肉丝', 24.00, 1, 24.00);
INSERT INTO `sf_order_item` VALUES (8, 5, 18, '西红柿鸡蛋', 18.00, 1, 18.00);
INSERT INTO `sf_order_item` VALUES (9, 6, 6, '水煮鱼', 48.00, 1, 48.00);
INSERT INTO `sf_order_item` VALUES (10, 6, 11, '水煮牛肉', 42.00, 1, 42.00);
INSERT INTO `sf_order_item` VALUES (11, 6, 13, '麻辣香锅', 45.00, 1, 45.00);
INSERT INTO `sf_order_item` VALUES (12, 7, 5, '口水鸡', 26.00, 2, 52.00);
INSERT INTO `sf_order_item` VALUES (13, 7, 15, '红烧排骨', 38.00, 2, 76.00);
INSERT INTO `sf_order_item` VALUES (14, 7, 22, '蛋炒饭', 12.00, 2, 24.00);
INSERT INTO `sf_order_item` VALUES (15, 7, 25, '可乐', 6.00, 2, 12.00);
INSERT INTO `sf_order_item` VALUES (16, 7, 23, '牛肉面', 25.00, 1, 25.00);
INSERT INTO `sf_order_item` VALUES (17, 8, 7, '毛血旺', 38.00, 2, 76.00);
INSERT INTO `sf_order_item` VALUES (18, 8, 10, '辣子鸡', 36.00, 2, 72.00);
INSERT INTO `sf_order_item` VALUES (19, 8, 16, '糖醋里脊', 30.00, 2, 60.00);
INSERT INTO `sf_order_item` VALUES (20, 8, 26, '鲜榨橙汁', 15.00, 2, 30.00);
INSERT INTO `sf_order_item` VALUES (21, 8, 21, '白米饭', 3.00, 2, 6.00);
INSERT INTO `sf_order_item` VALUES (22, 9, 12, '夫妻肺片', 28.00, 1, 28.00);
INSERT INTO `sf_order_item` VALUES (23, 9, 17, '青椒肉丝', 26.00, 1, 26.00);
INSERT INTO `sf_order_item` VALUES (24, 9, 19, '醋溜土豆丝', 15.00, 1, 15.00);
INSERT INTO `sf_order_item` VALUES (25, 9, 27, '柠檬蜂蜜茶', 12.00, 1, 12.00);
INSERT INTO `sf_order_item` VALUES (26, 9, 21, '白米饭', 3.00, 2, 6.00);
INSERT INTO `sf_order_item` VALUES (27, 10, 14, '干煸豆角', 22.00, 1, 22.00);
INSERT INTO `sf_order_item` VALUES (28, 10, 20, '蒜蓉菠菜', 16.00, 1, 16.00);
INSERT INTO `sf_order_item` VALUES (29, 10, 24, '小笼包', 20.00, 2, 40.00);
INSERT INTO `sf_order_item` VALUES (30, 10, 28, '提拉米苏', 28.00, 2, 56.00);
INSERT INTO `sf_order_item` VALUES (31, 10, 25, '可乐', 6.00, 2, 12.00);
INSERT INTO `sf_order_item` VALUES (32, 11, 29, '红豆冰', 18.00, 1, 18.00);
INSERT INTO `sf_order_item` VALUES (33, 11, 30, '芒果布丁', 22.00, 1, 22.00);
INSERT INTO `sf_order_item` VALUES (34, 11, 27, '柠檬蜂蜜茶', 12.00, 1, 12.00);
INSERT INTO `sf_order_item` VALUES (35, 12, 3, '麻婆豆腐', 18.00, 1, 18.00);
INSERT INTO `sf_order_item` VALUES (36, 12, 4, '糖醋排骨', 32.00, 1, 32.00);
INSERT INTO `sf_order_item` VALUES (37, 12, 18, '西红柿鸡蛋', 18.00, 1, 18.00);
INSERT INTO `sf_order_item` VALUES (38, 13, 1, '宫保鸡丁', 28.00, 1, 28.00);
INSERT INTO `sf_order_item` VALUES (39, 13, 8, '回锅肉', 32.00, 1, 32.00);
INSERT INTO `sf_order_item` VALUES (40, 13, 22, '蛋炒饭', 12.00, 2, 24.00);
INSERT INTO `sf_order_item` VALUES (41, 13, 26, '鲜榨橙汁', 15.00, 1, 15.00);
INSERT INTO `sf_order_item` VALUES (42, 14, 9, '鱼香肉丝', 24.00, 1, 24.00);
INSERT INTO `sf_order_item` VALUES (43, 14, 15, '红烧排骨', 38.00, 1, 38.00);
INSERT INTO `sf_order_item` VALUES (44, 14, 16, '糖醋里脊', 30.00, 1, 30.00);
INSERT INTO `sf_order_item` VALUES (45, 14, 21, '白米饭', 3.00, 2, 6.00);
INSERT INTO `sf_order_item` VALUES (46, 14, 25, '可乐', 6.00, 2, 12.00);
INSERT INTO `sf_order_item` VALUES (47, 15, 5, '口水鸡', 26.00, 2, 52.00);
INSERT INTO `sf_order_item` VALUES (48, 15, 7, '毛血旺', 38.00, 1, 38.00);
INSERT INTO `sf_order_item` VALUES (49, 15, 10, '辣子鸡', 36.00, 1, 36.00);
INSERT INTO `sf_order_item` VALUES (50, 15, 23, '牛肉面', 25.00, 2, 50.00);
INSERT INTO `sf_order_item` VALUES (51, 16, 6, '水煮鱼', 48.00, 2, 96.00);
INSERT INTO `sf_order_item` VALUES (52, 16, 11, '水煮牛肉', 42.00, 1, 42.00);
INSERT INTO `sf_order_item` VALUES (53, 16, 12, '夫妻肺片', 28.00, 1, 28.00);
INSERT INTO `sf_order_item` VALUES (54, 16, 17, '青椒肉丝', 26.00, 1, 26.00);
INSERT INTO `sf_order_item` VALUES (55, 16, 26, '鲜榨橙汁', 15.00, 1, 15.00);
INSERT INTO `sf_order_item` VALUES (56, 17, 13, '麻辣香锅', 45.00, 1, 45.00);
INSERT INTO `sf_order_item` VALUES (57, 17, 19, '醋溜土豆丝', 15.00, 1, 15.00);
INSERT INTO `sf_order_item` VALUES (58, 17, 20, '蒜蓉菠菜', 16.00, 1, 16.00);
INSERT INTO `sf_order_item` VALUES (59, 17, 27, '柠檬蜂蜜茶', 12.00, 1, 12.00);
INSERT INTO `sf_order_item` VALUES (60, 18, 2, '红烧肉', 35.00, 1, 35.00);
INSERT INTO `sf_order_item` VALUES (61, 18, 14, '干煸豆角', 22.00, 1, 22.00);
INSERT INTO `sf_order_item` VALUES (62, 18, 24, '小笼包', 20.00, 2, 40.00);
INSERT INTO `sf_order_item` VALUES (63, 18, 28, '提拉米苏', 28.00, 1, 28.00);
INSERT INTO `sf_order_item` VALUES (64, 18, 25, '可乐', 6.00, 1, 6.00);
INSERT INTO `sf_order_item` VALUES (65, 19, 3, '麻婆豆腐', 18.00, 1, 18.00);
INSERT INTO `sf_order_item` VALUES (66, 19, 18, '西红柿鸡蛋', 18.00, 1, 18.00);
INSERT INTO `sf_order_item` VALUES (67, 19, 22, '蛋炒饭', 12.00, 2, 24.00);
INSERT INTO `sf_order_item` VALUES (68, 19, 29, '红豆冰', 18.00, 1, 18.00);
INSERT INTO `sf_order_item` VALUES (69, 20, 1, '宫保鸡丁', 28.00, 1, 28.00);
INSERT INTO `sf_order_item` VALUES (70, 20, 9, '鱼香肉丝', 24.00, 1, 24.00);
INSERT INTO `sf_order_item` VALUES (71, 20, 15, '红烧排骨', 38.00, 1, 38.00);
INSERT INTO `sf_order_item` VALUES (72, 20, 21, '白米饭', 3.00, 2, 6.00);
INSERT INTO `sf_order_item` VALUES (73, 21, 4, '糖醋排骨', 32.00, 1, 32.00);
INSERT INTO `sf_order_item` VALUES (74, 21, 8, '回锅肉', 32.00, 1, 32.00);
INSERT INTO `sf_order_item` VALUES (75, 21, 16, '糖醋里脊', 30.00, 1, 30.00);
INSERT INTO `sf_order_item` VALUES (76, 21, 23, '牛肉面', 25.00, 1, 25.00);
INSERT INTO `sf_order_item` VALUES (77, 21, 26, '鲜榨橙汁', 15.00, 2, 30.00);
INSERT INTO `sf_order_item` VALUES (78, 22, 5, '口水鸡', 26.00, 2, 52.00);
INSERT INTO `sf_order_item` VALUES (79, 22, 7, '毛血旺', 38.00, 1, 38.00);
INSERT INTO `sf_order_item` VALUES (80, 22, 11, '水煮牛肉', 42.00, 1, 42.00);
INSERT INTO `sf_order_item` VALUES (81, 22, 17, '青椒肉丝', 26.00, 1, 26.00);
INSERT INTO `sf_order_item` VALUES (82, 22, 25, '可乐', 6.00, 1, 6.00);
INSERT INTO `sf_order_item` VALUES (83, 23, 6, '水煮鱼', 48.00, 2, 96.00);
INSERT INTO `sf_order_item` VALUES (84, 23, 10, '辣子鸡', 36.00, 1, 36.00);
INSERT INTO `sf_order_item` VALUES (85, 23, 12, '夫妻肺片', 28.00, 1, 28.00);
INSERT INTO `sf_order_item` VALUES (86, 23, 30, '芒果布丁', 22.00, 1, 22.00);
INSERT INTO `sf_order_item` VALUES (87, 23, 27, '柠檬蜂蜜茶', 12.00, 1, 12.00);

-- ----------------------------
-- Table structure for sf_table
-- ----------------------------
DROP TABLE IF EXISTS `sf_table`;
CREATE TABLE `sf_table`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `table_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '餐桌号',
  `seats` int NULL DEFAULT 4 COMMENT '座位数',
  `area` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '区域（大厅、包间等）',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '空闲' COMMENT '状态：空闲、使用中、清洁中、维修中',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_table_number`(`table_number` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '餐桌管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_table
-- ----------------------------
INSERT INTO `sf_table` VALUES (1, 'A01', 2, '大厅', '使用中', '2025-07-21 17:50:04', '2025-07-21 21:14:37');
INSERT INTO `sf_table` VALUES (2, 'A02', 4, '大厅', '空闲', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_table` VALUES (3, 'A03', 4, '大厅', '空闲', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_table` VALUES (4, 'A04', 6, '大厅', '空闲', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_table` VALUES (5, 'B01', 8, '包间', '空闲', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_table` VALUES (6, 'B02', 10, '包间', '空闲', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_table` VALUES (7, 'C01', 2, '靠窗', '空闲', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_table` VALUES (8, 'C02', 2, '靠窗', '空闲', '2025-07-21 17:50:04', '2025-07-21 17:50:04');

-- ----------------------------
-- Table structure for sf_user
-- ----------------------------
DROP TABLE IF EXISTS `sf_user`;
CREATE TABLE `sf_user`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '昵称',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '电话',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像',
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'USER' COMMENT '角色标识',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE,
  INDEX `idx_sf_user_username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_user
-- ----------------------------
INSERT INTO `sf_user` VALUES (1, 'user1', '123456', '用户1', '13800138001', '<EMAIL>', NULL, 'USER');
INSERT INTO `sf_user` VALUES (2, 'user2', '123456', '用户2', '13800138002', '<EMAIL>', NULL, 'USER');
INSERT INTO `sf_user` VALUES (3, '123123', '123456', '123123', '18585295391', NULL, NULL, 'USER');
INSERT INTO `sf_user` VALUES (4, 'user3', '123456', '张三', '13900139001', '<EMAIL>', NULL, 'USER');
INSERT INTO `sf_user` VALUES (5, 'user4', '123456', '李四', '13900139002', '<EMAIL>', NULL, 'USER');
INSERT INTO `sf_user` VALUES (6, 'user5', '123456', '王五', '13900139003', '<EMAIL>', NULL, 'USER');
INSERT INTO `sf_user` VALUES (7, 'user6', '123456', '赵六', '13900139004', '<EMAIL>', NULL, 'USER');
INSERT INTO `sf_user` VALUES (8, 'user7', '123456', '孙七', '13900139005', '<EMAIL>', NULL, 'USER');

-- ----------------------------
-- View structure for v_food_review_stats
-- ----------------------------
DROP VIEW IF EXISTS `v_food_review_stats`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_food_review_stats` AS select `f`.`id` AS `food_id`,`f`.`name` AS `food_name`,coalesce(avg(`fr`.`rating`),0) AS `avg_rating`,count(`fr`.`id`) AS `total_reviews`,count((case when (`fr`.`rating` = 5) then 1 end)) AS `five_star`,count((case when (`fr`.`rating` = 4) then 1 end)) AS `four_star`,count((case when (`fr`.`rating` = 3) then 1 end)) AS `three_star`,count((case when (`fr`.`rating` = 2) then 1 end)) AS `two_star`,count((case when (`fr`.`rating` = 1) then 1 end)) AS `one_star`,max(`fr`.`create_time`) AS `latest_review_time` from (`sf_food` `f` left join `sf_food_review` `fr` on(((`f`.`id` = `fr`.`food_id`) and (`fr`.`status` = '正常')))) group by `f`.`id`,`f`.`name`;

-- ----------------------------
-- View structure for v_table_status
-- ----------------------------
DROP VIEW IF EXISTS `v_table_status`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_table_status` AS select `t`.`id` AS `id`,`t`.`table_number` AS `table_number`,`t`.`seats` AS `seats`,`t`.`area` AS `area`,`t`.`status` AS `table_status`,(case when (count(`o`.`id`) > 0) then '占用中' else '空闲' end) AS `occupy_status`,count(`o`.`id`) AS `active_orders` from (`sf_table` `t` left join `sf_order` `o` on(((`t`.`table_number` = `o`.`table_number`) and (`o`.`status` in ('待支付','已支付','制作中','待取餐'))))) group by `t`.`id`,`t`.`table_number`,`t`.`seats`,`t`.`area`,`t`.`status`;

SET FOREIGN_KEY_CHECKS = 1;
