/*
 Navicat Premium Data Transfer

 Source Server         : help
 Source Server Type    : MySQL
 Source Server Version : 80041 (8.0.41)
 Source Host           : localhost:3306
 Source Schema         : diancan002

 Target Server Type    : MySQL
 Target Server Version : 80041 (8.0.41)
 File Encoding         : 65001

 Date: 21/07/2025 23:02:58
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for sf_admin
-- ----------------------------
DROP TABLE IF EXISTS `sf_admin`;
CREATE TABLE `sf_admin`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '姓名',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '电话',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像',
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'ADMIN' COMMENT '角色标识',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE,
  INDEX `idx_sf_admin_username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '管理员表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_admin
-- ----------------------------
INSERT INTO `sf_admin` VALUES (1, 'admin', '123456', '系统管理员', '13800138000', '<EMAIL>', NULL, 'ADMIN');

-- ----------------------------
-- Table structure for sf_blog
-- ----------------------------
DROP TABLE IF EXISTS `sf_blog`;
CREATE TABLE `sf_blog`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '博客标题',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '博客内容',
  `sf_cover_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '博客封面图片',
  `sf_created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '发布' COMMENT '博客状态',
  `sf_category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类名称',
  `sf_tags` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '博客标签',
  `sf_view_count` int NULL DEFAULT 0 COMMENT '浏览次数',
  `sf_author_id` int NULL DEFAULT NULL COMMENT '作者ID',
  `sf_author_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '作者名称',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sf_author_id`(`sf_author_id` ASC) USING BTREE,
  INDEX `idx_sf_blog_title`(`title` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '博客表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_blog
-- ----------------------------
INSERT INTO `sf_blog` VALUES (1, '美食制作技巧分享', '分享一些实用的美食制作技巧...', NULL, '2024-01-01 10:00:00', '发布', '美食', '美食,技巧', 0, 1, 'user1');
INSERT INTO `sf_blog` VALUES (2, '餐厅经营心得', '作为一名餐厅老板，我想分享一些经营心得...', NULL, '2024-01-02 15:30:00', '发布', '经营', '餐厅,经营', 2, 1, 'user1');

-- ----------------------------
-- Table structure for sf_business
-- ----------------------------
DROP TABLE IF EXISTS `sf_business`;
CREATE TABLE `sf_business`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '姓名',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '电话',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像',
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'BUSINESS' COMMENT '角色标识',
  `sf_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '商家描述',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '待审核' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE,
  INDEX `idx_sf_business_username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商家表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_business
-- ----------------------------
INSERT INTO `sf_business` VALUES (1, 'business1', '123456', '商家1', '***********', '<EMAIL>', NULL, 'BUSINESS', '这是一家优质餐厅', '已审核');
INSERT INTO `sf_business` VALUES (2, 'business2', '123456', '商家2', '***********', '<EMAIL>', NULL, 'BUSINESS', '这是一家特色小吃店', '待审核');

-- ----------------------------
-- Table structure for sf_cart
-- ----------------------------
DROP TABLE IF EXISTS `sf_cart`;
CREATE TABLE `sf_cart`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `food_id` int NOT NULL COMMENT '商品ID',
  `quantity` int NOT NULL DEFAULT 1 COMMENT '商品数量',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_food`(`user_id` ASC, `food_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_food_id`(`food_id` ASC) USING BTREE,
  CONSTRAINT `fk_cart_food` FOREIGN KEY (`food_id`) REFERENCES `sf_food` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_cart_user` FOREIGN KEY (`user_id`) REFERENCES `sf_user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '购物车表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_cart
-- ----------------------------
INSERT INTO `sf_cart` VALUES (2, 1, 4, 1, '2025-07-21 19:29:49', '2025-07-21 19:29:49');

-- ----------------------------
-- Table structure for sf_category
-- ----------------------------
DROP TABLE IF EXISTS `sf_category`;
CREATE TABLE `sf_category`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '分类描述',
  `icon` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类图标',
  `sort_order` int NULL DEFAULT 0 COMMENT '排序权重',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '启用' COMMENT '分类状态：启用/禁用',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_name`(`name` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_sort_order`(`sort_order` ASC) USING BTREE,
  INDEX `idx_sf_category_name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '食物分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_category
-- ----------------------------
INSERT INTO `sf_category` VALUES (1, '热门推荐', '系统推荐的热门食物', NULL, 1, '启用', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_category` VALUES (2, '川菜', '麻辣鲜香的川菜系列', NULL, 2, '启用', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_category` VALUES (3, '家常菜', '传统家常菜系列', NULL, 3, '启用', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_category` VALUES (4, '主食', '米饭、面条等主食类', NULL, 4, '启用', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_category` VALUES (5, '饮品', '各类饮料饮品', NULL, 5, '启用', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_category` VALUES (6, '甜品', '各类甜品点心', NULL, 6, '启用', '2025-07-21 17:50:04', '2025-07-21 17:50:04');

-- ----------------------------
-- Table structure for sf_comment
-- ----------------------------
DROP TABLE IF EXISTS `sf_comment`;
CREATE TABLE `sf_comment`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '评论内容',
  `time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '评论时间',
  `sf_user_id` int NULL DEFAULT NULL COMMENT '用户ID',
  `sf_user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户名',
  `sf_blog_id` int NULL DEFAULT NULL COMMENT '博客ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sf_user_id`(`sf_user_id` ASC) USING BTREE,
  INDEX `idx_sf_blog_id`(`sf_blog_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评论表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_comment
-- ----------------------------

-- ----------------------------
-- Table structure for sf_complaint
-- ----------------------------
DROP TABLE IF EXISTS `sf_complaint`;
CREATE TABLE `sf_complaint`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `sf_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '投诉内容',
  `sf_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '投诉图片',
  `complaint_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '投诉类型',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '待处理' COMMENT '投诉状态',
  `sf_complaint_date` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '投诉日期',
  `reply` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '回复信息',
  `sf_user_id` int NULL DEFAULT NULL COMMENT '用户ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sf_user_id`(`sf_user_id` ASC) USING BTREE,
  INDEX `idx_sf_complaint_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '投诉表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_complaint
-- ----------------------------
INSERT INTO `sf_complaint` VALUES (1, '123123123', '123123123123132', 'http://localhost:9090/files/1753103800761-默认头像.jpg', 'food_quality', '18822686555', '未审核', '2025-07-21 21:16:42', '111111', 3);

-- ----------------------------
-- Table structure for sf_food
-- ----------------------------
DROP TABLE IF EXISTS `sf_food`;
CREATE TABLE `sf_food`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '食品名称',
  `sf_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '食品图片',
  `sf_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '食品描述',
  `sf_category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '食品类型（保留兼容性）',
  `category_id` int NULL DEFAULT NULL COMMENT '分类ID',
  `sf_price` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '食品价格',
  `sf_stock` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '库存数量',
  `sf_shelf_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '上架' COMMENT '上架状态',
  `average_rating` decimal(3, 2) NULL DEFAULT 0.00 COMMENT '平均评分',
  `review_count` int NULL DEFAULT 0 COMMENT '评价总数',
  `last_review_time` datetime NULL DEFAULT NULL COMMENT '最新评价时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE,
  INDEX `idx_sf_food_name`(`name` ASC) USING BTREE,
  INDEX `idx_average_rating`(`average_rating` ASC) USING BTREE,
  INDEX `idx_review_count`(`review_count` ASC) USING BTREE,
  CONSTRAINT `fk_food_category` FOREIGN KEY (`category_id`) REFERENCES `sf_category` (`id`) ON DELETE SET NULL ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '食品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_food
-- ----------------------------
INSERT INTO `sf_food` VALUES (1, '宫保鸡丁', NULL, '经典川菜，麻辣鲜香', '川菜', 2, '28.00', '100', '上架', 4.50, 2, '2024-01-16 19:45:00');
INSERT INTO `sf_food` VALUES (2, '红烧肉', NULL, '传统家常菜，肥而不腻', '家常菜', 3, '35.00', '49', '上架', 5.00, 1, '2024-01-17 13:20:00');
INSERT INTO `sf_food` VALUES (3, '麻婆豆腐', NULL, '四川名菜，麻辣鲜嫩', '川菜', 2, '18.00', '79', '上架', 3.00, 1, '2024-01-18 20:15:00');
INSERT INTO `sf_food` VALUES (4, '糖醋排骨', NULL, '酸甜可口，老少皆宜', '家常菜', 3, '32.00', '59', '上架', 5.00, 1, '2025-07-21 20:01:51');

-- ----------------------------
-- Table structure for sf_food_review
-- ----------------------------
DROP TABLE IF EXISTS `sf_food_review`;
CREATE TABLE `sf_food_review`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int NOT NULL COMMENT '评价用户ID',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户昵称（冗余字段，提高查询效率）',
  `food_id` int NOT NULL COMMENT '菜品ID',
  `food_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '菜品名称（冗余字段）',
  `order_id` int NULL DEFAULT NULL COMMENT '关联订单ID',
  `rating` int NOT NULL COMMENT '评分（1-5星）',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '评价内容',
  `images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '评价图片（多张图片以逗号分隔）',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '正常' COMMENT '评价状态：正常、已删除、待审核',
  `reply_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '商家/管理员回复内容',
  `reply_time` datetime NULL DEFAULT NULL COMMENT '回复时间',
  `reply_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '回复人',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '评价时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_anonymous` tinyint NULL DEFAULT 0 COMMENT '是否匿名评价（0-否，1-是）',
  `helpful_count` int NULL DEFAULT 0 COMMENT '有用评价数量',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_food`(`user_id` ASC, `food_id` ASC) USING BTREE,
  INDEX `idx_food_id`(`food_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_rating`(`rating` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE,
  INDEX `idx_status`(`status` ASC) USING BTREE,
  INDEX `idx_sf_food_review_status`(`status` ASC) USING BTREE,
  INDEX `idx_sf_food_review_create_time`(`create_time` ASC) USING BTREE,
  CONSTRAINT `fk_review_food` FOREIGN KEY (`food_id`) REFERENCES `sf_food` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_review_user` FOREIGN KEY (`user_id`) REFERENCES `sf_user` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '菜品评价表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_food_review
-- ----------------------------
INSERT INTO `sf_food_review` VALUES (1, 1, 'user1', 1, '宫保鸡丁', NULL, 5, '味道很棒，麻辣鲜香，分量也足够，值得推荐！', NULL, '正常', NULL, NULL, NULL, '2024-01-15 12:30:00', '2025-07-21 17:50:04', 0, 3);
INSERT INTO `sf_food_review` VALUES (2, 2, 'user2', 1, '宫保鸡丁', NULL, 4, '口感不错，就是稍微有点咸，整体还是很满意的。', NULL, '正常', NULL, NULL, NULL, '2024-01-16 19:45:00', '2025-07-21 17:50:04', 0, 1);
INSERT INTO `sf_food_review` VALUES (3, 1, 'user1', 2, '红烧肉', NULL, 5, '红烧肉做得很地道，肥而不腻，入口即化，强烈推荐！', NULL, '正常', NULL, NULL, NULL, '2024-01-17 13:20:00', '2025-07-21 17:50:04', 0, 5);
INSERT INTO `sf_food_review` VALUES (4, 2, 'user2', 3, '麻婆豆腐', NULL, 3, '豆腐很嫩，但是辣度不够，希望能做得更正宗一些。', NULL, '正常', NULL, NULL, NULL, '2024-01-18 20:15:00', '2025-07-21 17:50:04', 0, 0);
INSERT INTO `sf_food_review` VALUES (5, 1, '用户1', 4, '糖醋排骨', NULL, 5, '很好吃超级无敌好吃啊', 'http://localhost:9090/files/1753099300473-默认头像.jpg', '正常', NULL, NULL, NULL, '2025-07-21 20:01:51', '2025-07-21 20:01:51', 1, 0);

-- ----------------------------
-- Table structure for sf_message
-- ----------------------------
DROP TABLE IF EXISTS `sf_message`;
CREATE TABLE `sf_message`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sf_user_id` int NULL DEFAULT NULL COMMENT '用户ID',
  `sf_question` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '问题内容',
  `reply` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '回复内容',
  `sf_image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '留言图片',
  `sf_leave_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '留言时间',
  `sf_reply_time` datetime NULL DEFAULT NULL COMMENT '回复时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sf_user_id`(`sf_user_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '留言表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_message
-- ----------------------------

-- ----------------------------
-- Table structure for sf_notice
-- ----------------------------
DROP TABLE IF EXISTS `sf_notice`;
CREATE TABLE `sf_notice`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '内容',
  `time` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建时间',
  `user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sf_notice_title`(`title` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '公告信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_notice
-- ----------------------------
INSERT INTO `sf_notice` VALUES (1, '系统维护通知', '系统将于本周日凌晨2:00-4:00进行维护，请合理安排时间。', '2024-01-01 10:00:00', 'admin');
INSERT INTO `sf_notice` VALUES (2, '新功能上线', '新增了在线客服功能，欢迎大家使用。', '2024-01-02 14:30:00', 'admin');

-- ----------------------------
-- Table structure for sf_order
-- ----------------------------
DROP TABLE IF EXISTS `sf_order`;
CREATE TABLE `sf_order`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sf_user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户名',
  `sf_user_id` int NULL DEFAULT NULL COMMENT '用户ID',
  `table_id` int NULL DEFAULT NULL COMMENT '餐桌ID',
  `table_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '餐桌号',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '待支付' COMMENT '订单状态：待支付、已支付、制作中、待取餐、已完成、已取消、退款中、已退款',
  `sf_order_number` int NULL DEFAULT NULL COMMENT '订单编号',
  `sf_create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `sf_remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户备注',
  `sf_evaluation` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '用户评价',
  `sf_total_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '订单总价格',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_sf_user_id`(`sf_user_id` ASC) USING BTREE,
  INDEX `idx_sf_order_number`(`sf_order_number` ASC) USING BTREE,
  INDEX `idx_table_id`(`table_id` ASC) USING BTREE,
  INDEX `idx_sf_order_status`(`status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_order
-- ----------------------------
INSERT INTO `sf_order` VALUES (1, 'user1', 1, 1, 'A01', '已完成', 40527290, '2025-07-21 19:25:30', '购物车批量结算，共1件商品', NULL, 32.00);
INSERT INTO `sf_order` VALUES (2, '123123', 3, 1, 'A01', '已完成', 78949677, '2025-07-21 21:09:17', '单个商品结算：麻婆豆腐', NULL, 18.00);
INSERT INTO `sf_order` VALUES (3, '123123', 3, 1, 'A01', '制作中', 19837094, '2025-07-21 21:14:38', '立即购买：红烧肉', NULL, 35.00);

-- ----------------------------
-- Table structure for sf_order_item
-- ----------------------------
DROP TABLE IF EXISTS `sf_order_item`;
CREATE TABLE `sf_order_item`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` int NOT NULL COMMENT '订单ID',
  `food_id` int NOT NULL COMMENT '商品ID',
  `food_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品名称',
  `food_price` decimal(10, 2) NOT NULL COMMENT '商品单价',
  `quantity` int NOT NULL COMMENT '商品数量',
  `subtotal` decimal(10, 2) NOT NULL COMMENT '小计金额',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE,
  INDEX `idx_food_id`(`food_id` ASC) USING BTREE,
  CONSTRAINT `fk_order_item_order` FOREIGN KEY (`order_id`) REFERENCES `sf_order` (`id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单详情表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_order_item
-- ----------------------------
INSERT INTO `sf_order_item` VALUES (1, 1, 4, '糖醋排骨', 32.00, 1, 32.00);
INSERT INTO `sf_order_item` VALUES (2, 2, 3, '麻婆豆腐', 18.00, 1, 18.00);
INSERT INTO `sf_order_item` VALUES (3, 3, 2, '红烧肉', 35.00, 1, 35.00);

-- ----------------------------
-- Table structure for sf_table
-- ----------------------------
DROP TABLE IF EXISTS `sf_table`;
CREATE TABLE `sf_table`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `table_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '餐桌号',
  `seats` int NULL DEFAULT 4 COMMENT '座位数',
  `area` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '区域（大厅、包间等）',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '空闲' COMMENT '状态：空闲、使用中、清洁中、维修中',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_table_number`(`table_number` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '餐桌管理表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_table
-- ----------------------------
INSERT INTO `sf_table` VALUES (1, 'A01', 2, '大厅', '使用中', '2025-07-21 17:50:04', '2025-07-21 21:14:37');
INSERT INTO `sf_table` VALUES (2, 'A02', 4, '大厅', '空闲', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_table` VALUES (3, 'A03', 4, '大厅', '空闲', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_table` VALUES (4, 'A04', 6, '大厅', '空闲', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_table` VALUES (5, 'B01', 8, '包间', '空闲', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_table` VALUES (6, 'B02', 10, '包间', '空闲', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_table` VALUES (7, 'C01', 2, '靠窗', '空闲', '2025-07-21 17:50:04', '2025-07-21 17:50:04');
INSERT INTO `sf_table` VALUES (8, 'C02', 2, '靠窗', '空闲', '2025-07-21 17:50:04', '2025-07-21 17:50:04');

-- ----------------------------
-- Table structure for sf_user
-- ----------------------------
DROP TABLE IF EXISTS `sf_user`;
CREATE TABLE `sf_user`  (
  `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '昵称',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '电话',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像',
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'USER' COMMENT '角色标识',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username` ASC) USING BTREE,
  INDEX `idx_sf_user_username`(`username` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of sf_user
-- ----------------------------
INSERT INTO `sf_user` VALUES (1, 'user1', '123456', '用户1', '13800138001', '<EMAIL>', NULL, 'USER');
INSERT INTO `sf_user` VALUES (2, 'user2', '123456', '用户2', '13800138002', '<EMAIL>', NULL, 'USER');
INSERT INTO `sf_user` VALUES (3, '123123', '123456', '123123', '18585295391', NULL, NULL, 'USER');

-- ----------------------------
-- View structure for v_food_review_stats
-- ----------------------------
DROP VIEW IF EXISTS `v_food_review_stats`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_food_review_stats` AS select `f`.`id` AS `food_id`,`f`.`name` AS `food_name`,coalesce(avg(`fr`.`rating`),0) AS `avg_rating`,count(`fr`.`id`) AS `total_reviews`,count((case when (`fr`.`rating` = 5) then 1 end)) AS `five_star`,count((case when (`fr`.`rating` = 4) then 1 end)) AS `four_star`,count((case when (`fr`.`rating` = 3) then 1 end)) AS `three_star`,count((case when (`fr`.`rating` = 2) then 1 end)) AS `two_star`,count((case when (`fr`.`rating` = 1) then 1 end)) AS `one_star`,max(`fr`.`create_time`) AS `latest_review_time` from (`sf_food` `f` left join `sf_food_review` `fr` on(((`f`.`id` = `fr`.`food_id`) and (`fr`.`status` = '正常')))) group by `f`.`id`,`f`.`name`;

-- ----------------------------
-- View structure for v_table_status
-- ----------------------------
DROP VIEW IF EXISTS `v_table_status`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_table_status` AS select `t`.`id` AS `id`,`t`.`table_number` AS `table_number`,`t`.`seats` AS `seats`,`t`.`area` AS `area`,`t`.`status` AS `table_status`,(case when (count(`o`.`id`) > 0) then '占用中' else '空闲' end) AS `occupy_status`,count(`o`.`id`) AS `active_orders` from (`sf_table` `t` left join `sf_order` `o` on(((`t`.`table_number` = `o`.`table_number`) and (`o`.`status` in ('待支付','已支付','制作中','待取餐'))))) group by `t`.`id`,`t`.`table_number`,`t`.`seats`,`t`.`area`,`t`.`status`;

SET FOREIGN_KEY_CHECKS = 1;
