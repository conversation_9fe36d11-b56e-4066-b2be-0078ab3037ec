package com.example.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 短信配置类
 */
@Configuration
@ConfigurationProperties(prefix = "sms")
public class SmsConfig {
    
    /**
     * 阿里云短信API地址
     */
    private String host = "https://dfsns.market.alicloudapi.com";
    
    /**
     * 短信发送路径
     */
    private String path = "/data/send_sms";
    
    /**
     * AppCode
     */
    private String appCode = "55296e6f6dee401186cbea47d0fd0988";
    
    /**
     * 模板ID
     */
    private String templateId = "CST_ptdie100";
    
    /**
     * 验证码有效期（秒）
     */
    private int codeExpireTime = 300; // 5分钟
    
    /**
     * 发送间隔限制（秒）
     */
    private int sendInterval = 60; // 60秒
    
    /**
     * 验证码长度
     */
    private int codeLength = 6;

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getAppCode() {
        return appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public int getCodeExpireTime() {
        return codeExpireTime;
    }

    public void setCodeExpireTime(int codeExpireTime) {
        this.codeExpireTime = codeExpireTime;
    }

    public int getSendInterval() {
        return sendInterval;
    }

    public void setSendInterval(int sendInterval) {
        this.sendInterval = sendInterval;
    }

    public int getCodeLength() {
        return codeLength;
    }

    public void setCodeLength(int codeLength) {
        this.codeLength = codeLength;
    }
} 