package com.example.mapper;

import com.example.entity.Table;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 餐桌数据访问层接口
 */
@Mapper
public interface TableMapper {

    /**
     * 查询所有餐桌（支持条件查询）
     */
    List<Table> selectAll(Table table);

    /**
     * 根据ID查询餐桌
     */
    Table selectById(Integer id);

    /**
     * 根据餐桌号查询餐桌
     */
    Table selectByTableNumber(String tableNumber);

    /**
     * 查询所有可用餐桌（状态为空闲且无未完成订单）
     */
    List<Table> selectAvailable();

    /**
     * 新增餐桌
     */
    int insert(Table table);

    /**
     * 更新餐桌
     */
    int updateById(Table table);

    /**
     * 删除餐桌
     */
    int deleteById(Integer id);

    /**
     * 检查餐桌是否被占用（有未完成订单）
     */
    int checkTableOccupied(@Param("tableNumber") String tableNumber);

    /**
     * 查询餐桌状态统计
     */
    List<Map<String, Object>> selectTableStatusStats();

    /**
     * 查询餐桌详细状态（包含占用信息）
     */
    List<Map<String, Object>> selectTableStatusDetail();
} 