package com.example.controller;

import cn.hutool.core.util.ObjectUtil;
import com.example.common.Result;
import com.example.common.enums.ResultCodeEnum;
import com.example.entity.Account;
import com.example.entity.Dingdan;
import com.example.entity.Foods;
import com.example.entity.OrderItem;
import com.example.mapper.FoodsMapper;
import com.example.service.DingdanService;
import com.example.utils.TokenUtils;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 订单表前端操作接口
 **/
@RestController
@RequestMapping("/dingdan")
public class DingdanController {

    @Resource
    private DingdanService dingdanService;
    @Resource
    private FoodsMapper foodsMapper;


    /**
     * 获取订单状态统计
     */
    @GetMapping("/stats/status")
    public Result getStatusStats() {
        return Result.success(dingdanService.getStatusStats());
    }

    /**
     * 获取每月订单量统计
     */
    @GetMapping("/stats/monthly")
    public Result getMonthlyStats(@RequestParam(required = false) Integer year) {
        return Result.success(dingdanService.getMonthlyStats(year));
    }

    /**
     * 获取收入统计
     */
    @GetMapping("/stats/revenue")
    public Result getRevenueStats(@RequestParam(required = false) Integer year) {
        return Result.success(dingdanService.getRevenueStats(year));
    }

    /**
     * 获取菜品销量排行
     */
    @GetMapping("/stats/popularFoods")
    public Result getPopularFoodsStats(@RequestParam(defaultValue = "10") Integer limit) {
        return Result.success(dingdanService.getPopularFoodsStats(limit));
    }

    /**
     * 获取用户注册趋势
     */
    @GetMapping("/stats/userRegistration")
    public Result getUserRegistrationStats(@RequestParam(required = false) Integer year) {
        return Result.success(dingdanService.getUserRegistrationStats(year));
    }



    /**
     * 获取分类销售占比
     */
    @GetMapping("/stats/categoryRevenue")
    public Result getCategoryRevenueStats() {
        return Result.success(dingdanService.getCategoryRevenueStats());
    }



    /**
     * 获取用户活跃度统计
     */
    @GetMapping("/stats/userActivity")
    public Result getUserActivityStats(@RequestParam(required = false) Integer days) {
        if (days == null) days = 30;
        return Result.success(dingdanService.getUserActivityStats(days));
    }

    /**
     * 获取综合运营数据概览
     */
    @GetMapping("/stats/overview")
    public Result getOverviewStats() {
        return Result.success(dingdanService.getOverviewStats());
    }

    /**
     * 新增订单（已弃用，使用购物车结算）
     * @deprecated 使用购物车结算功能
     */
    @Deprecated
    @PostMapping("/add")
    public Result add(@RequestBody Dingdan dingdan) {
        // 强制禁用此接口，引导用户使用购物车结算
        return Result.error("5001", "此接口已弃用，请使用购物车结算功能。立即购买请先加入购物车再结算。");
    }

    /**
     * 删除
     */
    @DeleteMapping("/delete/{id}")
    public Result deleteById(@PathVariable Integer id) {
        dingdanService.deleteById(id);
        return Result.success();
    }

    /**
     * 批量删除
     */
    @DeleteMapping("/delete/batch")
    public Result deleteBatch(@RequestBody List<Integer> ids) {
        dingdanService.deleteBatch(ids);
        return Result.success();
    }

    /**
     * 修改
     */
    @PutMapping("/update")
    public Result updateById(@RequestBody Dingdan dingdan) {
        dingdanService.updateById(dingdan);
        return Result.success();
    }

    /**
     * 根据ID查询
     */
    @GetMapping("/selectById/{id}")
    public Result selectById(@PathVariable Integer id) {
        Dingdan dingdan = dingdanService.selectById(id);
        return Result.success(dingdan);
    }

    /**
     * 获取订单详情（商品列表）
     */
    @GetMapping("/details/{id}")
    public Result getOrderDetails(@PathVariable Integer id) {
        List<OrderItem> orderItems = dingdanService.getOrderDetails(id);
        return Result.success(orderItems);
    }

    /**
     * 查询所有
     */
    @GetMapping("/selectAll")
    public Result selectAll(Dingdan dingdan) {
        List<Dingdan> list = dingdanService.selectAll(dingdan);
        return Result.success(list);
    }

    /**
     * 分页查询
     */
    @GetMapping("/selectPage")
    public Result selectPage(Dingdan dingdan,
                             @RequestParam(defaultValue = "1") Integer pageNum,
                             @RequestParam(defaultValue = "10") Integer pageSize) {
        PageInfo<Dingdan> page = dingdanService.selectPage(dingdan, pageNum, pageSize);
        return Result.success(page);
    }


    /**
     * 分页查询
     */
    @GetMapping("/selectPages")
    public Result selectPages(Dingdan dingdan,
                             @RequestParam(defaultValue = "1") Integer pageNum,
                             @RequestParam(defaultValue = "10") Integer pageSize) {
        PageInfo<Dingdan> page = dingdanService.selectPages(dingdan, pageNum, pageSize);
        return Result.success(page);
    }

    /**
     * 更新订单状态
     */
    @PutMapping("/updateStatus")
    public Result updateOrderStatus(@RequestParam Integer orderId, @RequestParam String status) {
        dingdanService.updateOrderStatus(orderId, status);
        return Result.success();
    }

    /**
     * 开始制作（已支付 → 制作中）
     */
    @PutMapping("/startCooking/{orderId}")
    public Result startCooking(@PathVariable Integer orderId) {
        dingdanService.updateOrderStatus(orderId, "制作中");
        return Result.success();
    }

    /**
     * 制作完成（制作中 → 待取餐）
     */
    @PutMapping("/finishCooking/{orderId}")
    public Result finishCooking(@PathVariable Integer orderId) {
        dingdanService.updateOrderStatus(orderId, "待取餐");
        return Result.success();
    }

    /**
     * 确认取餐（待取餐 → 已完成）
     */
    @PutMapping("/confirmPickup/{orderId}")
    public Result confirmPickup(@PathVariable Integer orderId) {
        dingdanService.updateOrderStatus(orderId, "已完成");
        return Result.success();
    }

    /**
     * 取消订单
     */
    @PutMapping("/cancel/{orderId}")
    public Result cancelOrder(@PathVariable Integer orderId) {
        dingdanService.updateOrderStatus(orderId, "已取消");
        return Result.success();
    }

    /**
     * 申请退款（已支付/制作中 → 退款中）
     */
    @PutMapping("/requestRefund/{orderId}")
    public Result requestRefund(@PathVariable Integer orderId) {
        dingdanService.updateOrderStatus(orderId, "退款中");
        return Result.success();
    }

    /**
     * 确认退款（退款中 → 已退款）
     */
    @PutMapping("/confirmRefund/{orderId}")
    public Result confirmRefund(@PathVariable Integer orderId) {
        dingdanService.updateOrderStatus(orderId, "已退款");
        return Result.success();
    }
}