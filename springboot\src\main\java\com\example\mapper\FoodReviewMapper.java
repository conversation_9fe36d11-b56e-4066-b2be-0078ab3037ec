package com.example.mapper;

import com.example.entity.FoodReview;
import com.example.entity.FoodReviewStats;
import java.util.List;
import java.util.Map;

/**
 * 操作菜品评价表相关数据接口
 */
public interface FoodReviewMapper {

    /**
     * 新增评价
     */
    int insert(FoodReview foodReview);

    /**
     * 删除评价
     */
    int deleteById(Integer id);

    /**
     * 修改评价
     */
    int updateById(FoodReview foodReview);

    /**
     * 根据ID查询评价
     */
    FoodReview selectById(Integer id);

    /**
     * 查询所有评价
     */
    List<FoodReview> selectAll(FoodReview foodReview);

    /**
     * 根据菜品ID查询评价列表（分页）
     */
    List<FoodReview> selectByFoodId(Map<String, Object> params);

    /**
     * 根据用户ID查询评价历史
     */
    List<FoodReview> selectByUserId(Map<String, Object> params);

    /**
     * 检查用户是否已评价某菜品
     */
    int checkUserReviewExists(Integer userId, Integer foodId);

    /**
     * 获取菜品评价统计信息
     */
    FoodReviewStats getReviewStats(Integer foodId);

    /**
     * 检查用户是否购买过某菜品
     */
    int checkUserPurchased(Integer userId, Integer foodId);

    /**
     * 根据状态查询评价列表
     */
    List<FoodReview> selectByStatus(Map<String, Object> params);

    /**
     * 统计菜品评价数量
     */
    int countByFoodId(Integer foodId);

    /**
     * 统计用户评价数量
     */
    int countByUserId(Integer userId);

    /**
     * 统计用户评价数量（支持筛选条件）
     */
    int countByUserIdWithFilter(Map<String, Object> params);

    /**
     * 更新评价状态
     */
    int updateStatus(Integer id, String status);

    /**
     * 批量删除评价
     */
    int deleteByIds(List<Integer> ids);

    /**
     * 获取用户对菜品的评价
     */
    FoodReview getUserFoodReview(Integer userId, Integer foodId);

    /**
     * 更新菜品评价统计信息
     */
    int updateFoodReviewStats(Integer foodId, Double avgRating, Integer reviewCount, String lastReviewTime);
} 