<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.ComplaintMapper">

    <sql id="Base_Column_List">
        id,title,sf_content,sf_image,complaint_type,phone,status,sf_complaint_date,reply,sf_user_id
    </sql>

    <select id="selectAll" resultType="com.example.entity.Complaint">
        select
        <include refid="Base_Column_List" />
        from sf_complaint
        <where>
            <if test="id != null"> and id = #{id}</if>
            <if test="title != null"> and title like concat('%', #{title}, '%')</if>
            <if test="sfContent != null"> and sf_content like concat('%', #{sfContent}, '%')</if>
            <if test="sfImage != null"> and sf_image like concat('%', #{sfImage}, '%')</if>
            <if test="complaintType != null"> and complaint_type like concat('%', #{complaintType}, '%')</if>
            <if test="phone != null"> and phone like concat('%', #{phone}, '%')</if>
            <if test="status != null"> and status like concat('%', #{status}, '%')</if>
            <if test="sfComplaintDate != null"> and sf_complaint_date = #{sfComplaintDate}</if>
            <if test="reply != null"> and reply like concat('%', #{reply}, '%')</if>
            <if test="sfUserId != null"> and sf_user_id = #{sfUserId}</if>
        </where>
        order by id desc
    </select>

    <select id="selectById" resultType="com.example.entity.Complaint">
        select
        <include refid="Base_Column_List" />
        from sf_complaint
        where id = #{id}
    </select>

    <delete id="deleteById">
        delete from sf_complaint
        where  id = #{id}
    </delete>

    <insert id="insert" parameterType="com.example.entity.Complaint" useGeneratedKeys="true" keyProperty="id">
        insert into sf_complaint
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null">title,</if>
            <if test="sfContent != null">sf_content,</if>
            <if test="sfImage != null">sf_image,</if>
            <if test="complaintType != null">complaint_type,</if>
            <if test="phone != null">phone,</if>
            <if test="status != null">status,</if>
            <if test="sfComplaintDate != null">sf_complaint_date,</if>
            <if test="reply != null">reply,</if>
            <if test="sfUserId != null">sf_user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null">#{title},</if>
            <if test="sfContent != null">#{sfContent},</if>
            <if test="sfImage != null">#{sfImage},</if>
            <if test="complaintType != null">#{complaintType},</if>
            <if test="phone != null">#{phone},</if>
            <if test="status != null">#{status},</if>
            <if test="sfComplaintDate != null">#{sfComplaintDate},</if>
            <if test="reply != null">#{reply},</if>
            <if test="sfUserId != null">#{sfUserId},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.example.entity.Complaint">
        update sf_complaint
        <set>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="sfContent != null">
                sf_content = #{sfContent},
            </if>
            <if test="sfImage != null">
                sf_image = #{sfImage},
            </if>
            <if test="complaintType != null">
                complaint_type = #{complaintType},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="sfComplaintDate != null">
                sf_complaint_date = #{sfComplaintDate},
            </if>
            <if test="reply != null">
                reply = #{reply},
            </if>
            <if test="sfUserId != null">
                sf_user_id = #{sfUserId},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>
