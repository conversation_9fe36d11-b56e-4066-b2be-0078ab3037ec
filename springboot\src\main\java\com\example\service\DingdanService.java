package com.example.service;

import cn.hutool.core.util.ObjectUtil;
import com.example.common.Result;
import com.example.common.constants.RedisKeyConstants;
import com.example.common.enums.ResultCodeEnum;
import com.example.common.enums.RoleEnum;
import com.example.common.service.CacheService;
import com.example.entity.Account;
import com.example.entity.Dingdan;
import com.example.entity.OrderItem;
import com.example.exception.CustomException;
import com.example.mapper.DingdanMapper;
import com.example.mapper.OrderItemMapper;
import com.example.service.FoodsService;
import com.example.utils.TokenUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;

import javax.annotation.Resource;
import java.util.*;

/**
 * 订单表业务处理
 */
@Service
public class DingdanService {

    @Resource
    private DingdanMapper dingdanMapper;

    @Resource
    private OrderItemMapper orderItemMapper;

    @Resource
    private CacheService cacheService;

    @Resource
    private TableService tableService;

    @Resource
    private com.example.mapper.TableMapper tableMapper;

    @Resource
    private FoodsService foodsService;

    /**
     * 统计订单状态分布（带缓存）
     */
    public Map<String, Object> getStatusStats() {
        // 尝试从缓存获取
        Map<String, Object> cachedStats = cacheService.get(RedisKeyConstants.ORDER_STATS_STATUS, Map.class);
        if (cachedStats != null) {
            return cachedStats;
        }
        
        // 从数据库查询
        List<Map<String, Object>> statusList = dingdanMapper.countByStatus();

        List<String> names = new ArrayList<>();
        List<Map<String, Object>> values = new ArrayList<>();

        for (Map<String, Object> item : statusList) {
            names.add((String) item.get("name"));

            Map<String, Object> valueItem = new HashMap<>();
            // COUNT(*)返回Long类型，需要转换为Integer
            Long valueLong = (Long) item.get("value");
            Integer value = valueLong != null ? valueLong.intValue() : 0;
            valueItem.put("value", value);
            valueItem.put("name", item.get("name"));
            values.add(valueItem);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("names", names);
        result.put("values", values);

        // 缓存结果
        cacheService.set(RedisKeyConstants.ORDER_STATS_STATUS, result, RedisKeyConstants.ExpireTime.ORDER_STATS);
        
        return result;
    }

    /**
     * 统计每月订单量（带缓存）
     */
    public Map<String, Object> getMonthlyStats(Integer year) {
        if (year == null) {
            year = Calendar.getInstance().get(Calendar.YEAR);
        }
        
        // 尝试从缓存获取
        String cacheKey = RedisKeyConstants.ORDER_STATS_MONTHLY_PREFIX + year;
        Map<String, Object> cachedStats = cacheService.get(cacheKey, Map.class);
        if (cachedStats != null) {
            return cachedStats;
        }
        
        // 从数据库查询
        List<Map<String, Object>> monthlyList = dingdanMapper.countByMonth(year);

        List<String> months = new ArrayList<>();
        List<Integer> counts = new ArrayList<>();

        // 初始化12个月的数据
        for (int i = 1; i <= 12; i++) {
            months.add(i + "月");
            counts.add(0);
        }

        // 填充实际数据
        for (Map<String, Object> item : monthlyList) {
            Integer month = (Integer) item.get("month");
            // COUNT(*)返回Long类型，需要转换为Integer
            Long countLong = (Long) item.get("count");
            Integer count = countLong != null ? countLong.intValue() : 0;
            if (month != null && month >= 1 && month <= 12) {
                counts.set(month - 1, count);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("months", months);
        result.put("counts", counts);

        // 缓存结果
        cacheService.set(cacheKey, result, RedisKeyConstants.ExpireTime.ORDER_STATS);
        
        return result;
    }

    /**
     * 统计每月收入
     */
    public Map<String, Object> getRevenueStats(Integer year) {
        if (year == null) {
            year = Calendar.getInstance().get(Calendar.YEAR);
        }
        
        String cacheKey = RedisKeyConstants.ORDER_STATS_MONTHLY_PREFIX + "revenue_" + year;
        Map<String, Object> cachedStats = cacheService.get(cacheKey, Map.class);
        if (cachedStats != null) {
            return cachedStats;
        }
        
        List<Map<String, Object>> revenueList = dingdanMapper.revenueByMonth(year);

        List<String> months = new ArrayList<>();
        List<Double> revenues = new ArrayList<>();

        // 初始化12个月的数据
        for (int i = 1; i <= 12; i++) {
            months.add(i + "月");
            revenues.add(0.0);
        }

        // 填充实际数据
        for (Map<String, Object> item : revenueList) {
            Integer month = (Integer) item.get("month");
            Object revenueObj = item.get("revenue");
            Double revenue = 0.0;
            if (revenueObj instanceof java.math.BigDecimal) {
                revenue = ((java.math.BigDecimal) revenueObj).doubleValue();
            } else if (revenueObj != null) {
                revenue = Double.valueOf(revenueObj.toString());
            }
            if (month != null && month >= 1 && month <= 12) {
                revenues.set(month - 1, revenue);
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("months", months);
        result.put("revenues", revenues);

        cacheService.set(cacheKey, result, RedisKeyConstants.ExpireTime.ORDER_STATS);
        return result;
    }

    /**
     * 统计菜品销量排行
     */
    public List<Map<String, Object>> getPopularFoodsStats(Integer limit) {
        String cacheKey = RedisKeyConstants.ORDER_STATS_STATUS + "_popular_foods_" + limit;
        List<Map<String, Object>> cachedStats = cacheService.get(cacheKey, List.class);
        if (cachedStats != null) {
            return cachedStats;
        }
        
        List<Map<String, Object>> result = dingdanMapper.getPopularFoods(limit);
        
        // 处理数值类型转换
        for (Map<String, Object> item : result) {
            Object salesObj = item.get("sales");
            if (salesObj instanceof java.math.BigDecimal) {
                item.put("sales", ((java.math.BigDecimal) salesObj).intValue());
            }
            Object revenueObj = item.get("revenue");
            if (revenueObj instanceof java.math.BigDecimal) {
                item.put("revenue", ((java.math.BigDecimal) revenueObj).doubleValue());
            }
            Object avgRatingObj = item.get("avgRating");
            if (avgRatingObj instanceof java.math.BigDecimal) {
                item.put("avgRating", ((java.math.BigDecimal) avgRatingObj).doubleValue());
            }
        }
        
        cacheService.set(cacheKey, result, RedisKeyConstants.ExpireTime.ORDER_STATS);
        return result;
    }

    /**
     * 统计用户注册趋势
     */
    public Map<String, Object> getUserRegistrationStats(Integer year) {
        if (year == null) {
            year = Calendar.getInstance().get(Calendar.YEAR);
        }
        
        String cacheKey = RedisKeyConstants.ORDER_STATS_STATUS + "_user_reg_" + year;
        Map<String, Object> cachedStats = cacheService.get(cacheKey, Map.class);
        if (cachedStats != null) {
            return cachedStats;
        }
        
        List<Map<String, Object>> registrationList = dingdanMapper.getUserRegistrationByMonth(year);

        List<String> months = new ArrayList<>();
        List<Integer> counts = new ArrayList<>();

        for (Map<String, Object> item : registrationList) {
            // 正确处理month字段的类型转换
            Object monthObj = item.get("month");
            Integer month = 0;
            if (monthObj instanceof Long) {
                month = ((Long) monthObj).intValue();
            } else if (monthObj instanceof Integer) {
                month = (Integer) monthObj;
            } else if (monthObj != null) {
                month = Integer.valueOf(monthObj.toString());
            }
            
            // 正确处理count字段的类型转换
            Object countObj = item.get("count");
            Integer count = 0;
            if (countObj instanceof Long) {
                count = ((Long) countObj).intValue();
            } else if (countObj instanceof Integer) {
                count = (Integer) countObj;
            } else if (countObj != null) {
                count = Integer.valueOf(countObj.toString());
            }
            
            months.add(month + "月");
            counts.add(count);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("months", months);
        result.put("counts", counts);

        cacheService.set(cacheKey, result, RedisKeyConstants.ExpireTime.ORDER_STATS);
        return result;
    }



    /**
     * 统计分类销售占比
     */
    public Map<String, Object> getCategoryRevenueStats() {
        String cacheKey = RedisKeyConstants.ORDER_STATS_STATUS + "_category_revenue";
        Map<String, Object> cachedStats = cacheService.get(cacheKey, Map.class);
        if (cachedStats != null) {
            return cachedStats;
        }
        
        List<Map<String, Object>> categoryList = dingdanMapper.getCategoryRevenueStats();

        List<String> names = new ArrayList<>();
        List<Map<String, Object>> values = new ArrayList<>();

        for (Map<String, Object> item : categoryList) {
            names.add((String) item.get("name"));

            Map<String, Object> valueItem = new HashMap<>();
            Object valueObj = item.get("value");
            Double value = 0.0;
            if (valueObj instanceof java.math.BigDecimal) {
                value = ((java.math.BigDecimal) valueObj).doubleValue();
            } else if (valueObj != null) {
                value = Double.valueOf(valueObj.toString());
            }
            valueItem.put("value", value);
            valueItem.put("name", item.get("name"));
            values.add(valueItem);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("names", names);
        result.put("values", values);

        cacheService.set(cacheKey, result, RedisKeyConstants.ExpireTime.ORDER_STATS);
        return result;
    }



    /**
     * 统计用户活跃度
     */
    public List<Map<String, Object>> getUserActivityStats(Integer days) {
        String cacheKey = RedisKeyConstants.ORDER_STATS_STATUS + "_user_activity_" + days;
        List<Map<String, Object>> cachedStats = cacheService.get(cacheKey, List.class);
        if (cachedStats != null) {
            return cachedStats;
        }
        
        List<Map<String, Object>> result = dingdanMapper.getUserActivityStats(days);
        
        // 处理数值类型转换
        for (Map<String, Object> item : result) {
            Long activeUsersLong = (Long) item.get("activeUsers");
            Long orderCountLong = (Long) item.get("orderCount");
            
            item.put("activeUsers", activeUsersLong != null ? activeUsersLong.intValue() : 0);
            item.put("orderCount", orderCountLong != null ? orderCountLong.intValue() : 0);
        }
        
        cacheService.set(cacheKey, result, RedisKeyConstants.ExpireTime.ORDER_STATS);
        return result;
    }

    /**
     * 获取综合运营数据概览
     */
    public Map<String, Object> getOverviewStats() {
        String cacheKey = RedisKeyConstants.ORDER_STATS_STATUS + "_overview";
        Map<String, Object> cachedStats = cacheService.get(cacheKey, Map.class);
        if (cachedStats != null) {
            return cachedStats;
        }
        
        Map<String, Object> result = dingdanMapper.getOverviewStats();
        
        // 处理数值类型转换
        if (result.get("totalRevenue") instanceof java.math.BigDecimal) {
            result.put("totalRevenue", ((java.math.BigDecimal) result.get("totalRevenue")).doubleValue());
        }
        if (result.get("avgRating") instanceof java.math.BigDecimal) {
            result.put("avgRating", ((java.math.BigDecimal) result.get("avgRating")).doubleValue());
        }
        
        cacheService.set(cacheKey, result, RedisKeyConstants.ExpireTime.ORDER_STATS);
        return result;
    }

    /**
     * 新增订单（已弃用，建议使用购物车结算功能）
     * @deprecated 建议使用 CartService.checkout() 方法
     */
    @Deprecated
    public void add(Dingdan dingdan) {
        // 设置订单基本信息
        Account currentUser = TokenUtils.getCurrentUser();
        dingdan.setSfUserId(currentUser.getId());
        dingdan.setSfUserName(currentUser.getUsername());
        dingdan.setSfOrderNumber(generateOrderId());
        dingdan.setSfCreateTime(new Date());
        
        // 处理餐桌信息（使用分布式锁）
        String lockKey = null;
        String lockValue = null;
        if (dingdan.getTableNumber() != null && !dingdan.getTableNumber().trim().isEmpty()) {
            // 生成锁的键和值
            lockKey = "table:lock:" + dingdan.getTableNumber();
            lockValue = "user:" + currentUser.getId() + ":" + System.currentTimeMillis();
            
            // 尝试获取餐桌锁（30秒过期）
            if (!cacheService.tryLock(lockKey, lockValue, 30)) {
                throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, "该餐桌正在被其他用户选择，请稍后重试");
            }
            
            try {
                // 检查餐桌是否存在且可用
                if (!tableService.isTableAvailable(dingdan.getTableNumber())) {
                    throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, "该餐桌不可用或已被占用");
                }
                
                // 检查餐桌占用冲突
                tableService.checkTableConflict(dingdan.getTableNumber());
                
                // 获取餐桌ID
                com.example.entity.Table table = tableService.selectByTableNumber(dingdan.getTableNumber());
                if (table != null) {
                    dingdan.setTableId(table.getId());
                }
            } catch (Exception e) {
                // 如果检查失败，释放锁
                cacheService.releaseLock(lockKey, lockValue);
                throw e;
            }
        }
        
        // 如果没有设置状态，默认为待支付
        if (dingdan.getStatus() == null || dingdan.getStatus().trim().isEmpty()) {
            dingdan.setStatus("待支付");
        }

        dingdanMapper.insert(dingdan);
        
        // 注意：此方法已弃用，不包含库存扣减逻辑
        // 如果需要库存管理，请使用购物车结算功能
        System.err.println("警告：使用了已弃用的订单创建方法，该方法不会扣减库存！订单ID: " + dingdan.getId());
        
        // 如果有餐桌，更新餐桌状态为使用中
        if (dingdan.getTableNumber() != null && !dingdan.getTableNumber().trim().isEmpty()) {
            com.example.entity.Table table = tableService.selectByTableNumber(dingdan.getTableNumber());
            if (table != null && "空闲".equals(table.getStatus())) {
                tableService.updateTableStatus(table.getId(), "使用中");
            }
        }
        
        // 释放餐桌锁
        if (lockKey != null && lockValue != null) {
            cacheService.releaseLock(lockKey, lockValue);
        }
        
        // 清除统计缓存
        clearStatsCaches();
    }

    /**
     * 生成订单ID
     */
    public Integer generateOrderId() {
        Random random = new Random();
        // 生成10000000到99999999之间的随机数
        return 10000000 + random.nextInt(90000000);
    }

    /**
     * 删除
     */
    public void deleteById(Integer id) {
        dingdanMapper.deleteById(id);
        // 清除统计缓存
        clearStatsCaches();
    }

    /**
     * 批量删除
     */
    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            dingdanMapper.deleteById(id);
        }
        // 清除统计缓存
        clearStatsCaches();
    }

    /**
     * 修改
     */
    public void updateById(Dingdan dingdan) {
        // 如果修改了餐桌信息，需要进行验证
        if (dingdan.getTableNumber() != null && !dingdan.getTableNumber().trim().isEmpty()) {
            // 获取原订单信息
            Dingdan existingOrder = dingdanMapper.selectById(dingdan.getId());
            
            // 如果餐桌号发生变化，需要检查新餐桌的可用性
            if (existingOrder != null && !dingdan.getTableNumber().equals(existingOrder.getTableNumber())) {
                // 检查新餐桌是否存在且可用
                if (!tableService.isTableAvailable(dingdan.getTableNumber())) {
                    throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, "该餐桌不可用或已被占用");
                }
                
                // 检查新餐桌占用冲突
                tableService.checkTableConflict(dingdan.getTableNumber());
                
                // 获取新餐桌ID
                com.example.entity.Table table = tableService.selectByTableNumber(dingdan.getTableNumber());
                if (table != null) {
                    dingdan.setTableId(table.getId());
                }
            }
        }
        
        dingdanMapper.updateById(dingdan);
        // 清除统计缓存
        clearStatsCaches();
    }

    /**
     * 更新订单状态
     */
    public void updateOrderStatus(Integer orderId, String newStatus) {
        Dingdan order = dingdanMapper.selectById(orderId);
        if (order == null) {
            throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, "订单不存在");
        }

        // 验证状态流转的合法性
        validateStatusTransition(order.getStatus(), newStatus);

        Dingdan updateOrder = new Dingdan();
        updateOrder.setId(orderId);
        updateOrder.setStatus(newStatus);
        
        dingdanMapper.updateById(updateOrder);
        
        // 如果订单取消或退款，恢复商品库存
        if ("已取消".equals(newStatus) || "已退款".equals(newStatus)) {
            try {
                // 获取订单详情，恢复库存
                List<OrderItem> orderItems = orderItemMapper.selectByOrderId(orderId);
                for (OrderItem item : orderItems) {
                    foodsService.restoreStock(item.getFoodId(), item.getQuantity());
                }
            } catch (Exception e) {
                // 库存恢复失败时记录日志，但不影响订单状态更新
                System.err.println("订单" + orderId + "库存恢复失败: " + e.getMessage());
            }
        }
        
        // 如果订单完成或取消，检查是否需要释放餐桌
        if (("已完成".equals(newStatus) || "已取消".equals(newStatus) || "已退款".equals(newStatus)) 
            && order.getTableNumber() != null && !order.getTableNumber().trim().isEmpty()) {
            
            // 检查该餐桌是否还有其他未完成订单
            int remainingOrders = tableMapper.checkTableOccupied(order.getTableNumber());
            if (remainingOrders == 0) {
                // 没有其他未完成订单，将餐桌状态改为空闲
                com.example.entity.Table table = tableService.selectByTableNumber(order.getTableNumber());
                if (table != null && "使用中".equals(table.getStatus())) {
                    tableService.updateTableStatus(table.getId(), "空闲");
                }
            }
        }
        
        // 清除统计缓存
        clearStatsCaches();
    }

    /**
     * 验证订单状态流转的合法性
     */
    private void validateStatusTransition(String currentStatus, String newStatus) {
        // 定义合法的状态流转
        Map<String, List<String>> allowedTransitions = new HashMap<>();
        allowedTransitions.put("待支付", Arrays.asList("已支付", "已取消"));
        allowedTransitions.put("已支付", Arrays.asList("制作中", "已取消", "退款中"));
        allowedTransitions.put("制作中", Arrays.asList("待取餐", "已取消"));
        allowedTransitions.put("待取餐", Arrays.asList("已完成"));
        allowedTransitions.put("已完成", Arrays.asList()); // 完成状态不能再转换
        allowedTransitions.put("已取消", Arrays.asList());
        allowedTransitions.put("退款中", Arrays.asList("已退款"));
        allowedTransitions.put("已退款", Arrays.asList());

        List<String> allowedNext = allowedTransitions.get(currentStatus);
        if (allowedNext == null || !allowedNext.contains(newStatus)) {
            throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, 
                "不能从状态 '" + currentStatus + "' 转换到 '" + newStatus + "'");
        }
    }

    /**
     * 根据ID查询
     */
    public Dingdan selectById(Integer id) {
        return dingdanMapper.selectById(id);
    }

    /**
     * 查询所有
     */
    public List<Dingdan> selectAll(Dingdan dingdan) {
        return dingdanMapper.selectAll(dingdan);
    }

    /**
     * 分页查询
     */
    public PageInfo<Dingdan> selectPage(Dingdan dingdan, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Dingdan> list = dingdanMapper.selectAll(dingdan);
        return PageInfo.of(list);
    }
    
    public PageInfo<Dingdan> selectPages(Dingdan dingdan, Integer pageNum, Integer pageSize) {
        Account currentUser = TokenUtils.getCurrentUser();
        if (ObjectUtil.isNull(currentUser)) {
            throw new CustomException(ResultCodeEnum.TOKEN_CHECK_ERROR);
        }
        
        // 用户只能查看自己的订单
        if (RoleEnum.USER.name().equals(currentUser.getRole())) {
            dingdan.setSfUserId(currentUser.getId());
        }
        
        // 商家和管理员可以查看所有订单
        
        
        PageHelper.startPage(pageNum, pageSize);
        List<Dingdan> list = dingdanMapper.selectAll(dingdan);
        return PageInfo.of(list);
    }

    /**
     * 清除统计相关缓存
     */
    private void clearStatsCaches() {
        // 清除状态统计缓存
        cacheService.delete(RedisKeyConstants.ORDER_STATS_STATUS);
        
        // 清除月度统计缓存（当前年份和前一年）
        int currentYear = Calendar.getInstance().get(Calendar.YEAR);
        cacheService.delete(RedisKeyConstants.ORDER_STATS_MONTHLY_PREFIX + currentYear);
        cacheService.delete(RedisKeyConstants.ORDER_STATS_MONTHLY_PREFIX + (currentYear - 1));
    }

    /**
     * 获取订单详情（商品列表）
     */
    public List<OrderItem> getOrderDetails(Integer orderId) {
        if (orderId == null) {
            return new ArrayList<>();
        }
        return orderItemMapper.selectByOrderId(orderId);
    }
}