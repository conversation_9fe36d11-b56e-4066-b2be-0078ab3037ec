<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.mapper.OrderItemMapper">

    <resultMap id="BaseResultMap" type="com.example.entity.OrderItem">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="food_id" property="foodId" />
        <result column="food_name" property="foodName" />
        <result column="food_price" property="foodPrice" />
        <result column="quantity" property="quantity" />
        <result column="subtotal" property="subtotal" />
    </resultMap>

    <!-- 根据订单ID查询订单详情列表 -->
    <select id="selectByOrderId" resultMap="BaseResultMap">
        SELECT * FROM sf_order_item 
        WHERE order_id = #{orderId}
        ORDER BY id ASC
    </select>

    <!-- 插入订单详情 -->
    <insert id="insert" parameterType="com.example.entity.OrderItem">
        INSERT INTO sf_order_item (order_id, food_id, food_name, food_price, quantity, subtotal)
        VALUES (#{orderId}, #{foodId}, #{foodName}, #{foodPrice}, #{quantity}, #{subtotal})
    </insert>

    <!-- 批量插入订单详情 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO sf_order_item (order_id, food_id, food_name, food_price, quantity, subtotal)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.orderId}, #{item.foodId}, #{item.foodName}, #{item.foodPrice}, #{item.quantity}, #{item.subtotal})
        </foreach>
    </insert>

    <!-- 根据ID删除订单详情 -->
    <delete id="deleteById">
        DELETE FROM sf_order_item WHERE id = #{id}
    </delete>

    <!-- 根据订单ID删除所有订单详情 -->
    <delete id="deleteByOrderId">
        DELETE FROM sf_order_item WHERE order_id = #{orderId}
    </delete>

</mapper> 