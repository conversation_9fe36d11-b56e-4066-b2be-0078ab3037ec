package com.example.service;

import com.example.common.constants.RedisKeyConstants;
import com.example.common.service.CacheService;
import com.example.entity.Foods;
import com.example.entity.FoodReviewStats;
import com.example.mapper.FoodsMapper;
import com.example.mapper.FoodReviewMapper;
import com.example.common.enums.ResultCodeEnum;
import com.example.exception.CustomException;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.List;
import java.util.HashMap;
import java.util.Map;

/**
 * 商品表业务处理
 */
@Service
public class FoodsService {

    @Resource
    private FoodsMapper foodsMapper;

    @Resource
    private CacheService cacheService;

    @Resource
    private FoodReviewMapper foodReviewMapper;

    /**
     * 新增
     */
    public void add(Foods foods) {
        foodsMapper.insert(foods);
    }

    /**
     * 删除
     */
    public void deleteById(Integer id) {
        foodsMapper.deleteById(id);
        // 删除缓存
        cacheService.delete(RedisKeyConstants.GOODS_INFO_PREFIX + id);
    }

    /**
     * 批量删除
     */
    public void deleteBatch(List<Integer> ids) {
        for (Integer id : ids) {
            deleteById(id);
        }
    }

    /**
     * 修改
     */
    public void updateById(Foods foods) {
        foodsMapper.updateById(foods);
        // 更新缓存
        if (foods.getId() != null) {
            cacheService.set(RedisKeyConstants.GOODS_INFO_PREFIX + foods.getId(), foods, RedisKeyConstants.ExpireTime.GOODS_INFO);
        }
    }

    /**
     * 根据ID查询（带缓存）
     */
    public Foods selectById(Integer id) {
        if (id == null) {
            return null;
        }
        
        // 先从缓存获取
        String cacheKey = RedisKeyConstants.GOODS_INFO_PREFIX + id;
        Foods foods = cacheService.get(cacheKey, Foods.class);
        if (foods != null) {
            return foods;
        }
        
        // 缓存未命中，从数据库查询
        foods = foodsMapper.selectById(id);
        if (foods != null) {
            // 写入缓存
            cacheService.set(cacheKey, foods, RedisKeyConstants.ExpireTime.GOODS_INFO);
        }
        return foods;
    }

    /**
     * 查询所有
     */
    public List<Foods> selectAll(Foods foods) {
        return foodsMapper.selectAll(foods);
    }

    /**
     * 分页查询
     */
    public PageInfo<Foods> selectPage(Foods foods, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Foods> list = foodsMapper.selectAll(foods);
        return PageInfo.of(list);
    }

    /**
     * 按分类查询食物
     */
    public List<Foods> selectByCategoryId(Integer categoryId) {
        return foodsMapper.selectByCategoryId(categoryId);
    }

    /**
     * 按分类分页查询
     */
    public PageInfo<Foods> selectPageByCategory(Integer categoryId, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Foods> list = foodsMapper.selectByCategoryId(categoryId);
        return PageInfo.of(list);
    }

    // ==================== 库存管理相关方法 ====================

    /**
     * 检查商品库存是否充足
     * @param foodId 商品ID
     * @param requiredQuantity 需要的数量
     * @return true-库存充足，false-库存不足
     */
    public boolean checkStock(Integer foodId, Integer requiredQuantity) {
        if (foodId == null || requiredQuantity == null || requiredQuantity <= 0) {
            return false;
        }
        
        try {
            int result = foodsMapper.checkStock(foodId, requiredQuantity);
            return result > 0;
        } catch (Exception e) {
            // 查询异常时认为库存不足
            return false;
        }
    }

    /**
     * 扣减商品库存（事务性操作）
     * @param foodId 商品ID
     * @param quantity 扣减数量
     * @throws CustomException 库存不足或扣减失败时抛出异常
     */
    @Transactional
    public void reduceStock(Integer foodId, Integer quantity) {
        if (foodId == null || quantity == null || quantity <= 0) {
            throw new CustomException(ResultCodeEnum.PARAM_LOST_ERROR);
        }

        // 先检查库存是否充足
        if (!checkStock(foodId, quantity)) {
            Foods food = selectById(foodId);
            String foodName = food != null ? food.getName() : "商品ID:" + foodId;
            throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, 
                foodName + " 库存不足，无法完成购买");
        }

        // 执行库存扣减
        int updateCount = foodsMapper.reduceStock(foodId, quantity);
        if (updateCount == 0) {
            // 扣减失败，可能是并发导致的库存不足
            Foods food = selectById(foodId);
            String foodName = food != null ? food.getName() : "商品ID:" + foodId;
            throw new CustomException(ResultCodeEnum.PARAM_ERROR.code, 
                foodName + " 库存不足，扣减失败");
        }

        // 清除缓存
        cacheService.delete(RedisKeyConstants.GOODS_INFO_PREFIX + foodId);
    }

    /**
     * 恢复商品库存（事务性操作）
     * @param foodId 商品ID
     * @param quantity 恢复数量
     */
    @Transactional
    public void restoreStock(Integer foodId, Integer quantity) {
        if (foodId == null || quantity == null || quantity <= 0) {
            return;
        }

        try {
            int updateCount = foodsMapper.restoreStock(foodId, quantity);
            if (updateCount > 0) {
                // 清除缓存
                cacheService.delete(RedisKeyConstants.GOODS_INFO_PREFIX + foodId);
            }
        } catch (Exception e) {
            // 恢复库存失败时记录日志但不抛出异常，避免影响主流程
            System.err.println("恢复库存失败: foodId=" + foodId + ", quantity=" + quantity + ", error=" + e.getMessage());
        }
    }

    /**
     * 获取商品当前库存
     * @param foodId 商品ID
     * @return 当前库存数量，查询失败返回0
     */
    public Integer getCurrentStock(Integer foodId) {
        if (foodId == null) {
            return 0;
        }
        
        try {
            Integer stock = foodsMapper.getCurrentStock(foodId);
            return stock != null ? stock : 0;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 批量检查商品库存
     * @param stockCheckList 库存检查列表，格式：[{foodId: 1, quantity: 2}, ...]
     * @return 库存不足的商品列表
     */
    public List<String> batchCheckStock(List<StockCheckItem> stockCheckList) {
        List<String> insufficientItems = new java.util.ArrayList<>();
        
        if (stockCheckList == null || stockCheckList.isEmpty()) {
            return insufficientItems;
        }
        
        for (StockCheckItem item : stockCheckList) {
            if (!checkStock(item.getFoodId(), item.getQuantity())) {
                Foods food = selectById(item.getFoodId());
                String foodName = food != null ? food.getName() : "商品ID:" + item.getFoodId();
                Integer currentStock = getCurrentStock(item.getFoodId());
                insufficientItems.add(foodName + "(需要:" + item.getQuantity() + "，库存:" + currentStock + ")");
            }
        }
        
        return insufficientItems;
    }

    /**
     * 库存检查项内部类
     */
    public static class StockCheckItem {
        private Integer foodId;
        private Integer quantity;
        
        public StockCheckItem() {}
        
        public StockCheckItem(Integer foodId, Integer quantity) {
            this.foodId = foodId;
            this.quantity = quantity;
        }
        
        public Integer getFoodId() { return foodId; }
        public void setFoodId(Integer foodId) { this.foodId = foodId; }
        public Integer getQuantity() { return quantity; }
        public void setQuantity(Integer quantity) { this.quantity = quantity; }
    }

    /**
     * 获取菜品详情（包含评价统计信息）
     */
    public Foods selectByIdWithReviews(Integer id) {
        Foods food = selectById(id);
        if (food != null) {
            try {
                // 获取评价统计信息
                FoodReviewStats reviewStats = foodReviewMapper.getReviewStats(id);
                if (reviewStats != null) {
                    food.setAverageRating(reviewStats.getAvgRating());
                    food.setReviewCount(reviewStats.getTotalReviews());
                    food.setLastReviewTime(reviewStats.getLatestReviewTime());
                }
            } catch (Exception e) {
                // 评价统计获取失败不影响主要信息
                System.err.println("获取菜品评价统计失败：" + e.getMessage());
            }
        }
        return food;
    }

    /**
     * 获取菜品列表（包含评价统计信息）
     */
    public PageInfo<Foods> selectAllWithReviews(Foods foods, Integer pageNum, Integer pageSize) {
        if (pageNum == null) pageNum = 1;
        if (pageSize == null) pageSize = 10;

        PageHelper.startPage(pageNum, pageSize);
        List<Foods> foodsList = foodsMapper.selectAll(foods);
        
        // 为每个菜品添加评价统计信息
        for (Foods food : foodsList) {
            try {
                FoodReviewStats reviewStats = foodReviewMapper.getReviewStats(food.getId());
                if (reviewStats != null) {
                    food.setAverageRating(reviewStats.getAvgRating());
                    food.setReviewCount(reviewStats.getTotalReviews());
                    food.setLastReviewTime(reviewStats.getLatestReviewTime());
                }
            } catch (Exception e) {
                // 评价统计获取失败不影响主要信息
                System.err.println("获取菜品评价统计失败：" + e.getMessage());
            }
        }
        
        return new PageInfo<>(foodsList);
    }

    /**
     * 根据分类ID查询食物（包含评价统计信息）
     */
    public List<Foods> selectByCategoryIdWithReviews(Integer categoryId) {
        List<Foods> foodsList = foodsMapper.selectByCategoryId(categoryId);
        
        // 为每个菜品添加评价统计信息
        for (Foods food : foodsList) {
            try {
                FoodReviewStats reviewStats = foodReviewMapper.getReviewStats(food.getId());
                if (reviewStats != null) {
                    food.setAverageRating(reviewStats.getAvgRating());
                    food.setReviewCount(reviewStats.getTotalReviews());
                    food.setLastReviewTime(reviewStats.getLatestReviewTime());
                }
            } catch (Exception e) {
                // 评价统计获取失败不影响主要信息
                System.err.println("获取菜品评价统计失败：" + e.getMessage());
            }
        }
        
        return foodsList;
    }

    /**
     * 按评分排序获取菜品列表
     */
    public PageInfo<Foods> selectByRatingOrder(Foods foods, Integer pageNum, Integer pageSize, String orderType) {
        if (pageNum == null) pageNum = 1;
        if (pageSize == null) pageSize = 10;

        PageHelper.startPage(pageNum, pageSize);
        List<Foods> foodsList = foodsMapper.selectAll(foods);
        
        // 为每个菜品添加评价统计信息
        for (Foods food : foodsList) {
            try {
                FoodReviewStats reviewStats = foodReviewMapper.getReviewStats(food.getId());
                if (reviewStats != null) {
                    food.setAverageRating(reviewStats.getAvgRating());
                    food.setReviewCount(reviewStats.getTotalReviews());
                    food.setLastReviewTime(reviewStats.getLatestReviewTime());
                }
            } catch (Exception e) {
                food.setAverageRating(0.0);
                food.setReviewCount(0);
            }
        }
        
        // 根据评分排序
        if ("desc".equalsIgnoreCase(orderType)) {
            foodsList.sort((f1, f2) -> Double.compare(
                f2.getAverageRating() != null ? f2.getAverageRating() : 0.0,
                f1.getAverageRating() != null ? f1.getAverageRating() : 0.0
            ));
        } else {
            foodsList.sort((f1, f2) -> Double.compare(
                f1.getAverageRating() != null ? f1.getAverageRating() : 0.0,
                f2.getAverageRating() != null ? f2.getAverageRating() : 0.0
            ));
        }
        
        return new PageInfo<>(foodsList);
    }

    /**
     * 获取评分筛选的菜品列表
     */
    public PageInfo<Foods> selectByRatingRange(Double minRating, Double maxRating, Integer pageNum, Integer pageSize) {
        if (pageNum == null) pageNum = 1;
        if (pageSize == null) pageSize = 10;

        PageHelper.startPage(pageNum, pageSize);
        List<Foods> foodsList = foodsMapper.selectAll(new Foods());
        
        // 筛选评分范围内的菜品
        List<Foods> filteredList = new java.util.ArrayList<>();
        for (Foods food : foodsList) {
            try {
                FoodReviewStats reviewStats = foodReviewMapper.getReviewStats(food.getId());
                if (reviewStats != null) {
                    Double avgRating = reviewStats.getAvgRating();
                    food.setAverageRating(avgRating);
                    food.setReviewCount(reviewStats.getTotalReviews());
                    food.setLastReviewTime(reviewStats.getLatestReviewTime());
                    
                    // 评分范围筛选
                    if (avgRating != null) {
                        if ((minRating == null || avgRating >= minRating) && 
                            (maxRating == null || avgRating <= maxRating)) {
                            filteredList.add(food);
                        }
                    }
                }
            } catch (Exception e) {
                // 评价统计获取失败的菜品如果minRating为null或0，则包含
                if (minRating == null || minRating <= 0) {
                    food.setAverageRating(0.0);
                    food.setReviewCount(0);
                    filteredList.add(food);
                }
            }
        }
        
        PageInfo<Foods> pageInfo = new PageInfo<>();
        pageInfo.setList(filteredList);
        pageInfo.setTotal(filteredList.size());
        pageInfo.setPageNum(pageNum);
        pageInfo.setPageSize(pageSize);
        pageInfo.setPages((filteredList.size() + pageSize - 1) / pageSize);
        
        return pageInfo;
    }

    /**
     * 获取热门推荐菜品（基于评价）
     */
    public List<Foods> getRecommendedFoods(Integer limit) {
        if (limit == null) limit = 10;
        
        List<Foods> foodsList = foodsMapper.selectAll(new Foods());
        
        // 为每个菜品添加评价统计信息并计算推荐分数
        for (Foods food : foodsList) {
            try {
                FoodReviewStats reviewStats = foodReviewMapper.getReviewStats(food.getId());
                if (reviewStats != null) {
                    food.setAverageRating(reviewStats.getAvgRating());
                    food.setReviewCount(reviewStats.getTotalReviews());
                    food.setLastReviewTime(reviewStats.getLatestReviewTime());
                }
            } catch (Exception e) {
                food.setAverageRating(0.0);
                food.setReviewCount(0);
            }
        }
        
        // 按照推荐算法排序：评分 * 0.7 + 评价数量 * 0.3
        foodsList.sort((f1, f2) -> {
            double score1 = (f1.getAverageRating() != null ? f1.getAverageRating() : 0.0) * 0.7 + 
                           (f1.getReviewCount() != null ? f1.getReviewCount() : 0) * 0.3;
            double score2 = (f2.getAverageRating() != null ? f2.getAverageRating() : 0.0) * 0.7 + 
                           (f2.getReviewCount() != null ? f2.getReviewCount() : 0) * 0.3;
            return Double.compare(score2, score1); // 降序
        });
        
        // 返回前N个
        return foodsList.subList(0, Math.min(limit, foodsList.size()));
    }
}