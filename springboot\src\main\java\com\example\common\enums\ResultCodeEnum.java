package com.example.common.enums;

public enum ResultCodeEnum {
    SUCCESS("200", "成功"),

    PARAM_ERROR("400", "参数异常"),
    TOKEN_INVALID_ERROR("401", "无效的token"),
    TOKEN_CHECK_ERROR("401", "token验证失败，请重新登录"),
    PARAM_LOST_ERROR("4001", "参数缺失"),

    SYSTEM_ERROR("500", "系统异常"),
    USER_EXIST_ERROR("5001", "用户名已存在"),
    USER_NOT_LOGIN("5002", "用户未登录"),
    USER_ACCOUNT_ERROR("5003", "账号或密码错误"),
    USER_NOT_EXIST_ERROR("5004", "用户不存在"),
    PARAM_PASSWORD_ERROR("5005", "原密码输入错误"),
    COLLECT_ALREADY_ERROR("5006", "您已收藏过该商品，请勿重复收藏"),
    USER_NO_PERMISSION_ERROR("5007", "权限不足"),
    CATEGORY_NAME_EXIST_ERROR("5008", "分类名称已存在"),
    CATEGORY_IN_USE_ERROR("5009", "该分类下还有食物，无法删除"),
    CATEGORY_NOT_EXIST_ERROR("5010", "分类不存在"),
    ;

    public String code;
    public String msg;

    ResultCodeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
