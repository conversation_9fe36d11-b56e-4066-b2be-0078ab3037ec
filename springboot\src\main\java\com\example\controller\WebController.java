package com.example.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.example.common.Result;
import com.example.common.enums.ResultCodeEnum;
import com.example.common.enums.RoleEnum;
import com.example.entity.Account;
import com.example.service.AdminService;
import com.example.service.BusinessService;
import com.example.service.SmsService;
import com.example.service.UserService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 基础前端接口
 */
@RestController
public class WebController {

    @Resource
    private AdminService adminService;
    @Resource
    private BusinessService businessService;
    @Resource
    private UserService userService;
    @Resource
    private SmsService smsService;

    @GetMapping("/")
    public Result hello() {
        return Result.success("访问成功");
    }

    /**
     * 登录
     */
    @PostMapping("/login")
    public Result login(@RequestBody Account account) {
        if (ObjectUtil.isEmpty(account.getRole())) {
            return Result.error(ResultCodeEnum.PARAM_LOST_ERROR);
        }

        // 短信验证码登录
        if (StrUtil.isNotBlank(account.getPhone()) && StrUtil.isNotBlank(account.getVerifyCode())) {
            // 验证验证码
            if (!smsService.verifyCode(account.getPhone(), account.getVerifyCode())) {
                return Result.error("400", "验证码错误或已过期");
            }
            
            // 通过手机号查找用户并登录
            if (RoleEnum.ADMIN.name().equals(account.getRole())) {
                account = adminService.loginByPhone(account.getPhone());
            } else if (RoleEnum.BUSINESS.name().equals(account.getRole())) {
                account = businessService.loginByPhone(account.getPhone());
            } else if (RoleEnum.USER.name().equals(account.getRole())) {
                account = userService.loginByPhone(account.getPhone());
            }
            return Result.success(account);
        }

        // 用户名密码登录
        if (ObjectUtil.isEmpty(account.getUsername()) || ObjectUtil.isEmpty(account.getPassword())) {
            return Result.error(ResultCodeEnum.PARAM_LOST_ERROR);
        }
        
        if (RoleEnum.ADMIN.name().equals(account.getRole())) {
            account = adminService.login(account);
        }
        if (RoleEnum.BUSINESS.name().equals(account.getRole())) {
            account = businessService.login(account);
        }
        if (RoleEnum.USER.name().equals(account.getRole())) {
            account = userService.login(account);
        }
        return Result.success(account);
    }

    /**
     * 注册
     */
    @PostMapping("/register")
    public Result register(@RequestBody Account account) {
        if (StrUtil.isBlank(account.getUsername()) || StrUtil.isBlank(account.getPassword())
                || ObjectUtil.isEmpty(account.getRole()) || StrUtil.isBlank(account.getPhone())
                || StrUtil.isBlank(account.getVerifyCode())) {
            return Result.error(ResultCodeEnum.PARAM_LOST_ERROR);
        }

        // 验证手机验证码
        if (!smsService.verifyCode(account.getPhone(), account.getVerifyCode())) {
            return Result.error("400", "验证码错误或已过期");
        }

        if (RoleEnum.ADMIN.name().equals(account.getRole())) {
            adminService.register(account);
        }
        if (RoleEnum.BUSINESS.name().equals(account.getRole())) {
            businessService.register(account);
        }
        if (RoleEnum.USER.name().equals(account.getRole())) {
            userService.register(account);
        }
        return Result.success();
    }

    /**
     * 修改密码
     */
    @PutMapping("/updatePassword")
    public Result updatePassword(@RequestBody Account account) {
        if (StrUtil.isBlank(account.getUsername()) || StrUtil.isBlank(account.getNewPassword())
                || ObjectUtil.isEmpty(account.getRole()) || StrUtil.isBlank(account.getPhone())
                || StrUtil.isBlank(account.getVerifyCode())) {
            return Result.error(ResultCodeEnum.PARAM_LOST_ERROR);
        }

        // 验证手机验证码
        if (!smsService.verifyCode(account.getPhone(), account.getVerifyCode())) {
            return Result.error("400", "验证码错误或已过期");
        }

        if (RoleEnum.ADMIN.name().equals(account.getRole())) {
            adminService.updatePasswordByPhone(account);
        }
        if (RoleEnum.BUSINESS.name().equals(account.getRole())) {
            businessService.updatePasswordByPhone(account);
        }
        if (RoleEnum.USER.name().equals(account.getRole())) {
            userService.updatePasswordByPhone(account);
        }
        return Result.success();
    }

}
