package com.example.controller;

import com.example.common.Result;
import com.example.entity.CartStatistics;
import com.example.entity.CartVO;
import com.example.service.CartService;
import com.example.utils.TokenUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 购物车前端操作接口
 */
@RestController
@RequestMapping("/cart")
public class CartController {

    @Resource
    private CartService cartService;

    /**
     * 获取购物车列表
     */
    @GetMapping("/list")
    public Result getCartList() {
        List<CartVO> cartList = cartService.getUserCartList(null);
        return Result.success(cartList);
    }

    /**
     * 添加商品到购物车
     */
    @PostMapping("/add")
    public Result addToCart(@RequestBody Map<String, Object> params) {
        Integer foodId = (Integer) params.get("foodId");
        Integer quantity = params.get("quantity") != null ? (Integer) params.get("quantity") : 1;
        
        cartService.addToCart(null, foodId, quantity);
        return Result.success();
    }

    /**
     * 更新商品数量
     */
    @PutMapping("/update")
    public Result updateQuantity(@RequestBody Map<String, Object> params) {
        Integer foodId = (Integer) params.get("foodId");
        Integer quantity = (Integer) params.get("quantity");
        
        cartService.updateQuantity(null, foodId, quantity);
        return Result.success();
    }

    /**
     * 删除购物车商品
     */
    @DeleteMapping("/remove")
    public Result removeFromCart(@RequestParam Integer foodId) {
        cartService.removeFromCart(null, foodId);
        return Result.success();
    }

    /**
     * 清空购物车
     */
    @DeleteMapping("/clear")
    public Result clearCart() {
        cartService.clearCart(null);
        return Result.success();
    }

    /**
     * 购物车结算
     */
    @PostMapping("/checkout")
    public Result checkout(@RequestBody Map<String, Object> params) {
        String remark = (String) params.get("remark");
        String tableNumber = (String) params.get("tableNumber");
        
        Integer orderId = cartService.checkout(null, remark, tableNumber);
        return Result.success(orderId);
    }

    /**
     * 获取购物车统计信息
     */
    @GetMapping("/statistics")
    public Result getCartStatistics() {
        CartStatistics statistics = cartService.getCartStatistics(null);
        return Result.success(statistics);
    }

    /**
     * 获取购物车商品数量
     */
    @GetMapping("/count")
    public Result getCartCount() {
        Integer count = cartService.getCartCount(null);
        return Result.success(count);
    }
} 